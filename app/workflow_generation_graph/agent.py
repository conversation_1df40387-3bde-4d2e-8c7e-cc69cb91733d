import json
import logging
from typing import Any, Optional

import dirtyjson
import json5
import json_repair
from strands import Agent
from strands.agent.agent_result import Agent<PERSON><PERSON><PERSON>
from strands.multiagent import MultiAgentBase, MultiAgentResult
from strands.multiagent.base import MultiAgentBase, MultiAgentResult, NodeResult, Status
from strands.telemetry.metrics import EventLoopMetrics
from strands.types.content import ContentBlock, Message

from app.shared.config.logging_config import get_logger
from app.workflow_generation_graph.tools.layout import rectangle_layout_with_separation


def calculate_node_size(inputs: int, outputs: int) -> tuple[int, int]:
    width = 208
    base_height = 90
    total_handles = inputs + outputs

    if total_handles <= 2:
        spacing_per_handle = 35
    elif total_handles == 3:
        spacing_per_handle = 25
    else:
        spacing_per_handle = 20

    height = base_height + total_handles * spacing_per_handle
    return width, height


def get_callback_logger(
    agent_name,
):
    def callback_logger(**kwargs):
        kwargs["agent"] = agent_name
        logger = get_logger("app.workflow_generation_graph.agent")
        if "message" in kwargs:
            logger.info(kwargs)
            # print(kwargs)
        elif "reasoning" in kwargs:
            # print(kwargs)
            logger.info(kwargs)
        elif "result" in kwargs:
            logger.info(kwargs)
            # print(kwargs)

    return callback_logger


def get_agent(agent_name, model, tools, system_prompt):
    return Agent(
        tools=tools,
        model=model,
        system_prompt=system_prompt,
        callback_handler=get_callback_logger(agent_name),
    )


def safe_loads(text: Optional[str]) -> Any:
    """
    Safely parse JSON text using multiple parsers with fallback options.

    Args:
        text: JSON string to parse. Can be None.

    Returns:
        Parsed JSON object

    Raises:
        ValueError: If all parsing attempts fail
        TypeError: If text is not a string or None
    """
    if text is None:
        raise ValueError("Cannot parse None as JSON")

    if not isinstance(text, str):
        raise TypeError(f"Expected string or None, got {type(text).__name__}")

    if not text.strip():
        raise ValueError("Cannot parse empty string as JSON")
    try:
        result = json.loads(text)
        return result
    except json.JSONDecodeError as e:
        pass
    try:
        result = json5.loads(text)
        return result
    except Exception as e:
        pass
    try:
        result = dirtyjson.loads(text)
        return result
    except Exception as e:
        pass
    try:
        result = json_repair.repair_json(text, return_objects=True)
        return result
    except Exception as e:
        raise ValueError(f"Could not parse JSON: {e}")


class PostProcessingNode(MultiAgentBase):
    def __init__(self, agent_name, model, tools, system_prompt):
        super().__init__()
        self.name = agent_name
        self.func = tools[0]
        self.callback_logger = get_callback_logger(agent_name)

    async def invoke_async(self, task, **kwargs):
        event_loop = EventLoopMetrics()
        attri = event_loop.start_cycle()
        task = "\n".join(t["text"] for t in task)
        if "```json" in task:
            task = task.split("```json")[1].split("```")[0]
        task = safe_loads(task)
        if "workflow" in task:
            workflow = self.func(task["workflow"])
            nodes = workflow["nodes"]
            rectangles = []
            for node in nodes:
                inputs = node["data"]["definition"]["inputs"]
                inputs = sum([1 for input in inputs if input["is_handle"]])
                outputs = len(node["data"]["definition"]["outputs"])
                width, height = calculate_node_size(inputs, outputs)
                width = width * 388 / 208
                height = height * 388 / 208
                node["width"] = int(width)
                node["height"] = int(height)
                rectangles.append(
                    {
                        "id": node["id"],
                        "x": node["position"]["x"],
                        "y": node["position"]["y"],
                        "w": node["width"],
                        "h": node["height"],
                    }
                )
            separation = 100.0
            new_layout = rectangle_layout_with_separation(
                rectangles, separation=separation, fixed_node_id="start-node"
            )
            new_layout = {
                rect["id"]: {"x": int(rect["x"]), "y": int(rect["y"])}
                for rect in new_layout
            }
            for node in workflow["nodes"]:
                node["position"] = new_layout[node["id"]]
                node["positionAbsolute"] = new_layout[node["id"]]
            task["workflow"] = workflow

        task = json.dumps(task)
        result = f"```json\n{task}\n```"
        # logging.info(
        #     json.dumps(
        #         {
        #             "message": {"role": "assistant", "content": result},
        #             "agent": "post_processing",
        #         }
        #     )
        # )
        self.callback_logger(message=result)
        event_loop.end_cycle(*attri)
        agent_result = AgentResult(
            stop_reason="end_turn",
            message=Message(role="assistant", content=[ContentBlock(text=result)]),
            metrics=event_loop,
            state=None,
        )
        # Return wrapped in MultiAgentResult
        return MultiAgentResult(
            status=Status.COMPLETED,
            results={self.name: NodeResult(result=agent_result)},
        )

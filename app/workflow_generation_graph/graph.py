import json
import logging
import os

from strands import tool
from strands.models.openai import OpenAIModel
from strands.multiagent import GraphBuilder

from app.shared.config.constants import Workflow_SSEEventType
from app.workflow_generation_graph.agent import (
    PostProcessingNode,
    get_agent,
    safe_loads,
)
from app.workflow_generation_graph.agent_prompts.prompt_enhancement import (
    PROMPT_ENHANCEMENT_SYSTEM_PROMPT,
)
from app.workflow_generation_graph.agent_prompts.router import ROUTER_SYSTEM_PROMPT
from app.workflow_generation_graph.agent_prompts.validation import (
    VALIDATION_SYSTEM_PROMPT,
)
from app.workflow_generation_graph.agent_prompts.workflow_generation import (
    WORKFLOW_GENERATION_SYSTEM_PROMPT,
)
from app.workflow_generation_graph.tools.context import get_context
from app.workflow_generation_graph.tools.post_process import post_processing
from app.workflow_generation_graph.tools.RAG import RAG_search

REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")

model = OpenAIModel(
    client_args={
        "api_key": REQUESTY_API_KEY,
        "base_url": "https://router.requesty.ai/v1",
    },
    model_id="anthropic/claude-sonnet-4",
)

reasoning_model = OpenAIModel(
    client_args={
        "api_key": REQUESTY_API_KEY,
        "base_url": "https://router.requesty.ai/v1",
    },
    model_id="anthropic/claude-sonnet-4",
    # reasoning_effort="medium",
)


def get_graph():
    # model = get_model("bedrock", "anthropic/claude-4-sonnet-latest")
    prompt_enhancement_agent = get_agent(
        "prompt_enhancement", model, [], PROMPT_ENHANCEMENT_SYSTEM_PROMPT
    )
    workflow_generation_agent = get_agent(
        "workflow_generation",
        reasoning_model,
        [get_context, RAG_search],
        WORKFLOW_GENERATION_SYSTEM_PROMPT,
    )
    validation_agent = get_agent("validation", model, [], VALIDATION_SYSTEM_PROMPT)

    @tool(
        name="workflow_generation",
        description="Generate a workflow, input is a json with user_prompt, enhanced_prompt, and criteria.",
    )
    def workflow_generation(x):
        return workflow_generation_agent(x)

    @tool(
        name="validator",
        description="Validate a workflow, input is a json with workflow, user_prompt, enhanced_prompt, and criteria.",
    )
    def validator(x):
        return validation_agent(x)

    router_agent = get_agent(
        "router",
        reasoning_model,
        [workflow_generation, validator],
        ROUTER_SYSTEM_PROMPT,
    )
    post_processing_agent = PostProcessingNode(
        "post_processing", None, [post_processing], ""
    )
    builder = GraphBuilder()

    builder.add_node(prompt_enhancement_agent, "prompt_enhancement")
    builder.add_node(post_processing_agent, "post_processing")
    builder.add_node(router_agent, "router")

    builder.add_edge("prompt_enhancement", "router")
    builder.add_edge("router", "post_processing")

    builder.set_entry_point("prompt_enhancement")

    return builder.build()


async def generate_workflow(user_prompt: str, session_id: str):
    """Generate workflow from user prompt."""
    # logging.basicConfig(
    #     level=logging.INFO,  # Set log level to INFO
    #     format="%(asctime)s - %(levelname)s - %(message)s",
    # )
    # logging.info(f"Generating workflow for {user_prompt}")
    graph = get_graph()
    try:
        result = graph(user_prompt)
        result = result.results["post_processing"].result.results["post_processing"]
        post_result = result.result.message["content"][0]["text"]

        if "```json" in post_result:
            post_result = post_result.split("```json")[1].split("```")[0].strip()

        try:
            parsed_result = safe_loads(post_result)
        except:
            parsed_result = {"message": post_result}
        parsed_result["error"] = {}
        parsed_result["data"] = {"session_id": session_id}
        parsed_result["type"] = Workflow_SSEEventType.WORKFLOW_GENERATED.value
        yield parsed_result
    except Exception as e:
        logging.error(f"Error generating workflow: {e}")
        yield {
            "message": "",
            "error": str(e),
            "data": {"session_id": session_id},
            "type": Workflow_SSEEventType.ERROR.value,
        }
    final_result = {
        "message": "",
        "error": {},
        "data": {},
        "type": Workflow_SSEEventType.STREAM_END.value,
    }
    yield final_result


# async def test_graph_process():
#     user_prompt = "Create a workflow which take the meeting information from the calendar and send me summary to my mail"
#     async for response in generate_workflow(user_prompt, "test"):
#         print(response)


# if __name__ == "__main__":
#     test_graph_process()

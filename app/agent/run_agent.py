from typing import Optional
import logging
import asyncio
import time

from app.agent.utils.message_utils import build_user_message
from app.agent.utils.deepagent_utils import create_deep_agent
from app.services.stream_formatter import stream_formatter
from app.services.redis_streams import get_redis_manager
from app.helper.stop_signal import check_stop_signal, clear_stop_signal
from app.helper.background_tasks import background_summarize
from app.shared.config.constants import EventType
from app.utils.tracing import trace_operation
from app.utils.metrics import get_metrics_manager
from app.utils.exceptions import get_exception_tracker
from app.utils.agent_redis_store import get_sources, clear_sources, get_approval_data, clear_approval_data

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()


async def run_agent_stream(
    user_message: str,
    provider: str,
    model: str,
    use_knowledge: bool,
    use_search: bool,
    user_id: str,
    organisation_id: str,
    conversation_id: str,
    agent_id: Optional[str] = None,
    use_memory: bool = True,
    mcp_ids: Optional[list] = None,
    attachments: Optional[list] = None,
    use_thinking: bool = False,
    kb_source: Optional[str] = None,
    kb_file_ids: Optional[list] = None,
    is_global: bool = True,
    timezone: str = "UTC",
    user_name: Optional[str] = None,
    user_email: Optional[str] = None,
):
    # Automatically determine if this is a global agent
    # If agent_id is provided and not empty, it's a non-global agent
    if agent_id:
        is_global = False
    
    redis_client = None
    is_stopped = False
    last_usage_data = {}  # Track last message_end usage data
    agent = None  # Track agent for cleanup
    start_time = time.time()
    metrics_manager = get_metrics_manager()

    with trace_operation(
        "agent.stream",
        attributes={
            "user_id": user_id,
            "user_name": user_name,
            "user_email": user_email,
            "organisation_id": organisation_id,
            "conversation_id": conversation_id,
            "provider": provider,
            "model": model,
            "use_knowledge": use_knowledge,
            "use_search": use_search,
            "use_memory": use_memory,
            "use_thinking": use_thinking,
            "is_global": is_global,
            "agent_id": agent_id or "default",
            "message_length": len(user_message),
        },
    ) as span:
        try:
            # Get the global Redis manager (reuses existing connection)
            redis_manager = await get_redis_manager()
            redis_client = redis_manager.redis_client
            await clear_stop_signal(conversation_id, redis_client)

            with trace_operation(
                "deepagent.create",
                attributes={
                    "provider": provider,
                    "model": model,
                    "agent_id": agent_id or "default",
                    "conversation_id": conversation_id,
                },
            ) as agent_span:
                agent = await create_deep_agent(
                    provider,
                    model,
                    use_knowledge,
                    use_search,
                    user_id,
                    organisation_id,
                    conversation_id,
                    agent_id,
                    use_memory,
                    mcp_ids,
                    use_thinking,
                    is_global,
                    timezone,
                    kb_source,
                    user_name,
                    user_email,
                )

            stream_start_chunk = {"type": EventType.STREAM_START.value}
            yield stream_start_chunk

            # Create async generator for agent stream
            with trace_operation(
                "stream.process",
                attributes={
                    "conversation_id": conversation_id,
                    "recursion_limit": 100,
                    "stream_mode": "events",
                },
            ) as stream_span:
                agent_stream = agent.astream_events(
                    {
                        "messages": [
                            {
                                "role": "user",
                                "content": build_user_message(user_message, attachments),
                            }
                        ],
                    },
                    config={
                        "configurable": {
                            "thread_id": conversation_id,
                            "user_id": user_id,
                            "organisation_id": organisation_id,
                            "conversation_id": conversation_id,
                            "kb_source": kb_source,
                            "kb_file_ids": kb_file_ids if kb_file_ids else [],
                            "timezone": timezone,
                        },
                        "recursion_limit": 100,
                    },
                    stream_mode=["messages", "updates", "values", "custom"],
                    subgraphs=True,
                )

                chunk_count = 0
                # Process stream with periodic stop signal checks
                async for chunk in agent_stream:
                    chunk_count += 1
                    # Check for stop signal
                    if await check_stop_signal(conversation_id, redis_client):
                        is_stopped = True
                        break

                    formatted_chunk = stream_formatter(chunk)
                    if formatted_chunk:
                        # Handle both single events and lists of events
                        chunks_to_yield = formatted_chunk if isinstance(formatted_chunk, list) else [formatted_chunk]
                        
                        for chunk_item in chunks_to_yield:
                            # Track last message_end usage data
                            if chunk_item.get(
                                "type"
                            ) == EventType.MESSAGE_END.value and chunk_item.get(
                                "usage_data"
                            ):
                                # Store the last usage data to send in stream_end
                                last_usage_data = chunk_item["usage_data"]

                            yield chunk_item

                stream_span.set_attribute("chunk_count", chunk_count)
                stream_span.set_attribute("is_stopped", is_stopped)
                stream_span.set_attribute("total_tokens", last_usage_data.get("total_tokens", 0))

            # If stopped, send stream_end with is_stopped flag and clear Redis data
            if is_stopped:
                # Clear any pending data from Redis
                await clear_sources(conversation_id)
                await clear_approval_data(conversation_id)
                
                stream_end_chunk = {
                    "type": EventType.STREAM_END.value,
                    "is_stopped": True,
                }
                yield stream_end_chunk
                return

            # Prepare stream_end chunk with approval data and sources from Redis
            stream_end_chunk = {"type": EventType.STREAM_END.value, "is_stopped": False}

            # Add last usage data if available
            if last_usage_data:
                stream_end_chunk["usage_data"] = last_usage_data

            # Get approval data from Redis
            approval_data = await get_approval_data(conversation_id)
            if approval_data:
                stream_end_chunk["requires_approval"] = approval_data.get(
                    "requires_approval", False
                )
                stream_end_chunk["approval_type"] = approval_data.get("approval_type")
                stream_end_chunk["approval_data"] = approval_data.get("approval_data")

            # Get sources from Redis
            sources = await get_sources(conversation_id)
            if sources:
                stream_end_chunk["sources"] = sources

            yield stream_end_chunk

            # Clear approval data and sources from Redis after sending
            await clear_approval_data(conversation_id)
            await clear_sources(conversation_id)

            # Record successful agent execution metrics
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_agent_execution(
                duration_ms=duration_ms,
                attributes={
                    "provider": provider,
                    "model": model,
                    "user_id": user_id,
                    "user_name": user_name,
                    "user_email": user_email,
                    "agent_id": agent_id or "default",
                    "status": "success",
                    "tokens_used": last_usage_data.get("total_tokens", 0),
                },
            )
        except Exception as e:
            span.set_attribute("error", True)
            span.record_exception(e)
            
            # Track exception with Signoz OTEL
            exception_tracker.track_exception(
                e,
                severity="error",
                attributes={
                    "component": "agent_stream",
                    "provider": provider,
                    "model": model,
                    "user_id": user_id,
                    "user_name": user_name,
                    "user_email": user_email,
                    "organisation_id": organisation_id,
                    "conversation_id": conversation_id,
                    "agent_id": agent_id or "default",
                },
            )
            
            # Record agent error metrics
            metrics_manager.record_agent_error(
                error_type=type(e).__name__,
                attributes={
                    "provider": provider,
                    "model": model,
                    "user_id": user_id,
                    "user_name": user_name,
                    "user_email": user_email,
                    "agent_id": agent_id or "default",
                },
            )
            # Send error in stream_end event
            error_stream_end_chunk = {
                "type": EventType.STREAM_END.value,
                "is_error": True,
                "error": str(e),
            }
            yield error_stream_end_chunk
        finally:
            # Run check_and_summarize in background without blocking
            if agent is not None:
                span.add_event("background.summarize.start")
                asyncio.create_task(
                    background_summarize(
                        supervisor=agent,
                        conversation_id=conversation_id,
                        user_id=user_id,
                        organisation_id=organisation_id,
                        agent_id=agent_id,
                        total_tokens=last_usage_data.get("total_tokens", 0),
                    )
                )

            # Cleanup: Clear stop signal (but DON'T close redis_client - it's shared)
            if redis_client:
                try:
                    await clear_stop_signal(conversation_id, redis_client)
                except Exception:
                    pass

            # Explicit cleanup to help garbage collection
            redis_client = None
            agent = None
            agent_stream = None


async def run_agent(
    user_message: str,
    provider: str,
    model: str,
    use_knowledge: bool,
    use_search: bool,
    user_id: str,
    organisation_id: str,
    conversation_id: str,
    agent_id: Optional[str] = None,
    use_memory: bool = True,
    mcp_ids: Optional[list] = None,
    attachments: Optional[list] = None,
    use_thinking: bool = False,
    kb_source: Optional[str] = None,
    kb_file_ids: Optional[list] = None,
    is_global: bool = True,
    timezone: str = "UTC",
    user_name: Optional[str] = None,
    user_email: Optional[str] = None,
):
    # Automatically determine if this is a global agent
    # If agent_id is provided and not empty, it's a non-global agent
    if agent_id:
        is_global = False
    
    with trace_operation(
        "agent.run",
        attributes={
            "user_id": user_id,
            "user_name": user_name,
            "user_email": user_email,
            "organisation_id": organisation_id,
            "conversation_id": conversation_id,
            "provider": provider,
            "model": model,
            "use_knowledge": use_knowledge,
            "use_search": use_search,
            "use_memory": use_memory,
            "use_thinking": use_thinking,
            "is_global": is_global,
            "agent_id": agent_id or "default",
            "message_length": len(user_message),
        },
    ) as span:
        try:
            with trace_operation(
                "deepagent.create",
                attributes={
                    "provider": provider,
                    "model": model,
                    "agent_id": agent_id or "default",
                    "conversation_id": conversation_id,
                },
            ) as agent_span:
                agent = await create_deep_agent(
                    provider,
                    model,
                    use_knowledge,
                    use_search,
                    user_id,
                    organisation_id,
                    conversation_id,
                    agent_id,
                    use_memory,
                    mcp_ids,
                    use_thinking,
                    is_global,
                    timezone,
                    kb_source,
                    user_name,
                    user_email,
                )

            result = None
            with trace_operation(
                "stream.process",
                attributes={
                    "conversation_id": conversation_id,
                    "recursion_limit": 100,
                    "stream_mode": "values",
                },
            ) as stream_span:
                chunk_count = 0
                async for chunk in agent.astream_events(
                    {
                        "messages": [
                            {
                                "role": "user",
                                "content": build_user_message(user_message, attachments),
                            }
                        ],
                    },
                    config={
                        "configurable": {
                            "thread_id": conversation_id,
                            "user_id": user_id,
                            "organisation_id": organisation_id,
                            "conversation_id": conversation_id,
                            "kb_source": kb_source,
                            "kb_file_ids": kb_file_ids if kb_file_ids else [],
                            "timezone": timezone,
                        },
                        "recursion_limit": 100,
                    },
                    stream_mode="values",
                ):
                    chunk_count += 1
                    result = chunk

                stream_span.set_attribute("chunk_count", chunk_count)

            return result
        except Exception as e:
            span.set_attribute("error", True)
            span.record_exception(e)
            
            # Track exception with Signoz OTEL
            exception_tracker.track_exception(
                e,
                severity="error",
                attributes={
                    "component": "agent_run",
                    "provider": provider,
                    "model": model,
                    "user_id": user_id,
                    "user_name": user_name,
                    "user_email": user_email,
                    "organisation_id": organisation_id,
                    "conversation_id": conversation_id,
                    "agent_id": agent_id or "default",
                },
            )
            raise

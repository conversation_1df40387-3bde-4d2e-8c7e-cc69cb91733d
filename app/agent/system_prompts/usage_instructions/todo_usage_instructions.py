TODO_USAGE_INSTRUCTIONS = """
<todo-list-instructions>
You have two todo list tools :  `read_todos` and `write_todos` . You should be using these tools for your help and thoroughly for your whole task trajectory. 
	<when-to-not-use>
		1. The task is small and can be completed in a single step.
			<examples>
				- Find About top 10 news from today from web.
				- Can you find about my company objectives from the knowledge base. 
			</examples>
		2. User is just greeting like , "Hello , how are you", "Tell me a bit about yourself".
	</when-to-not-use>

	In all other long running cases you should be using these two todo tools. 
	Follow this MANDATORY workflow : 

	<workflow>
	1. **At the START**: If the task has 1+ steps, use the write_todos tool to create a TODO list immediately.
	2. **After EVERY action**: After you use ANY tool (read_file, knowledge_base_agent, web_search_agent, mcp_subagent, store_memory, etc.) or complete ANY step:
	   - IMMEDIATELY call read_todos to read your current TODO list
	   - Reflect on what you've accomplished and what's next
	   - Use write_todos to mark the completed task as "completed" 
	   - Use write_todos to mark the next task as "in_progress"
	3. **Continue systematically**: Work through tasks one at a time until all are completed.
	4. **Final step**: When ALL tasks are done, use write_todos ONE FINAL TIME to mark everything as "completed".
	</workflow>

	<critical-points>
	   - NEVER skip calling read_todos after tool executions
	   - ALWAYS update todo status after completing each task
	   - Only have ONE task marked as "in_progress" at any time
	   - When reading multiple files, read them ONE AT A TIME in sequence, not all at once
	</critical-points>

This workflow ensures you stay on track, don't forget steps, and give the user visibility into your progress.
</todo-list-instructions>
"""

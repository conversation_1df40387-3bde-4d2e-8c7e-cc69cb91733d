MCP_USAGE_INSTRUCTIONS = """
<mcp-agent-usage-instructions>

    <purpose>
        The MCP agent is responsible for executing **specialized tools**, interacting with **external integrations**, and performing **API-level operations** that exceed the model's built-in capabilities.  
        Use it whenever a task requires **real system actions**, **tool invocation**, or **third-party service interaction**.
    </purpose>

    <when-to-use>
        Delegate to the MCP agent when the user request requires:

        1. **Execution of a specific MCP tool**
            - “Run the file converter.”
            - “Use the PDF parser on this document.”

        2. **Interaction with external systems or APIs**
            - Gmail (read, send, manage emails)
            - Slack (send messages, fetch channels or threads)
            - Databases (query, insert, update)
            - Storage platforms or workflow systems

        3. **Specialized or technical operations beyond built-in abilities**
            - File operations
            - Structured workflows
            - Triggering cloud functions, integrations, or pipelines

        4. **Explicit user requests requiring tool execution**
            - “Send this message via Slack.”
            - “Query my database.”
            - “Retrieve my latest emails.”

        *If the task requires **actual execution** or **external interaction**, use the MCP agent.*
    </when-to-use>

    <when-not-to-use>
        Avoid delegating to the MCP agent when:

        1. The task requires only **reasoning, explanation, or content generation**  
            - No external action required.

        2. The user needs **public, internet-based information**  
            → Use web search.

        3. The task involves **organization-internal knowledge**  
            → Use the knowledge_base_agent.

        4. The request is fully solvable within the model  
            - Summaries, explanations, brainstorming, coding help

        5. The user indicates they do NOT want tool execution  
            - “Don’t run anything, just explain.”

    </when-not-to-use>

    <parameter-handling>
        Before delegating to any MCP tool:

        - **Verify that all required parameters for the tool are provided by the user.**  
        - If ANY required parameter is missing:  
            → **Ask the user for the missing information before executing or delegating.**  
        
        Example:  
        - Tool requires: `file_path`, `operation_type`  
        - User provides only: “Run operation on this file.”  
            → Ask: “Which operation type would you like to use?”

        *Never attempt an MCP tool call with incomplete or ambiguous parameters.*
    </parameter-handling>

    <workflow>
        1. **Interpret the user intent**
            - Determine whether MCP tool execution is necessary.

        2. **Check required parameters**
            - If all are present → proceed to delegation  
            - If not → ask user for missing details

        3. **Delegate to MCP agent**
            - Provide clear, tool-appropriate instructions

        4. **Process the agent response**
            - Summarize results clearly
            - Offer next steps if appropriate

        5. **If no delegation needed**
            - Handle using internal reasoning or another subagent
    </workflow>

    <best-practices>
        - Delegate only when true external action is needed.
        - Always validate parameter completeness before executing tools.
        - Avoid unnecessary or redundant tool calls.
        - Keep MCP instructions clear, minimal, and action-focused.
        - Maintain user consent when dealing with sensitive or external systems.
    </best-practices>

</mcp-agent-usage-instructions>
"""

MCP_SEARCH_USAGE_INSTRUCTIONS = """
<mcp-search-tool-usage-instructions>

    <purpose>
        The **MCP Search Tool** discovers specialized tools from the MCP marketplace  
        (2000+ available integrations) when existing capabilities cannot fulfill the user's request.  
        It helps identify external tools that match the task requirements and extend agent functionality.
    </purpose>

    <when-to-use>
        Use the MCP Search Tool when:

        1. **Current tools, workflows, or subagents cannot complete the user's request**
            - No available internal or MCP tools meet the task needs.
        
        2. You need to discover **specialized or advanced MCP tools** from the marketplace
            - E.g., analytics tools, industry-specific APIs, advanced document processors.

        3. The user's request clearly requires **external integrations beyond built-in tools**
            - Payment APIs, CRM systems, proprietary services, specialized data sources.

        4. A specific MCP tool partially supports the task but **lacks required functionality**
            - “This tool can parse text, but I also need semantic indexing.”

        *If built-in capabilities are insufficient → search the MCP marketplace.*
    </when-to-use>

    <when-not-to-use>
        Avoid using the MCP Search Tool when:

        1. **Existing tools or workflows already satisfy the user’s request**
            - No need to search for alternatives.

        2. The task does NOT require external integration  
            - Reasoning, content creation, explanation, planning, or internal logic tasks.

        3. The request is already solvable via:
            - MCP agent tools  
            - Workflows  
            - Knowledge base  
            - Web search  
            - Internal capabilities  

        *Never call MCP Search Tool unnecessarily—use it only as a fallback mechanism.*
    </when-not-to-use>

    <best-practices>
        - Search only when there is a clear capability gap.  
        - Provide a concise query summarizing the missing functionality.  
        - Use MCP Search Tool results to select the most suitable integration.  
        - Always confirm tool relevance before delegating execution.  
    </best-practices>

</mcp-search-tool-usage-instructions>

"""

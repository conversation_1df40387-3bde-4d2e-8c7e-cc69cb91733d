READ_FILE_USAGE_INSTRUCTIONS = """
<read-file-tool-usage-instructions>

    <purpose>
        The **read_file** tool is used to download and parse documents from URLs (PDF, CSV, DOCX, TXT) so their contents can be analyzed or processed.
    </purpose>

    <when-to-use>
        Use the read_file tool when:
        - The user provides a **document URL** that needs to be read, parsed, or analyzed  
        - You need to extract **text**, **tables**, **metadata**, or other structured content from a file  
        - The file content is essential for completing the user’s request  
    </when-to-use>

    <when-not-to-use>
        Do NOT use read_file when:
        - The user does not provide a URL  
        - The request does not require accessing file contents  
    </when-not-to-use>

    <critical-rules>
        - Only use **read_file** to process external documents  
        - **NEVER** use or run the `create_file` tool under any circumstances  
    </critical-rules>

</read-file-tool-usage-instructions>
"""

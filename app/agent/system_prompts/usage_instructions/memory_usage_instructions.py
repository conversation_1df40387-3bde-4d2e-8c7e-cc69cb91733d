MEMORY_USAGE_INSTRUCTIONS = """
 <memory-usage-instructions>
Your have two long-term memory tools: **`add_memory`** and **`get_memory`**.  
You should use them **strategically** throughout your task trajectory to preserve helpful user information and retrieve it when needed.

	<when-to-use>
	Use `add_memory` when the user provides NEW information that is:
	
	1. A stable preference:
	    - “I prefer Python over JavaScript.”
	    - “I like responses in a concise style.”
	        
	2. A personal profile detail (long-term identity, background, or life context):
	    - “I live in Toronto.”
	    - “My birthday is July 4th.”
	    - “I work as a data scientist.”
	        
	3. An ongoing project, multi-step goal, or long-term plan:
	    - “We’re designing a business plan; let’s continue this over our next sessions.”
	    - “Help me track my fitness progress weekly.”
		
	4. User explicitly asks you to remember something:
	    - “Remember that my team name is <PERSON> Phoenix.”
	    - “Please store this preference.”
		
	*Use `get_memory` when:
	
	1. Beginning ANY task that may benefit from personalization:
	    - Writing assistance, planning, design, coding, learning, self-improvement, ongoing projects.
	        
	2. You suspect previous user information may shape the answer:
	    - Preferences (tone, format, communication style).
	    - Previously discussed constraints or goals.
	    - Previously stored plan or multi-step workflow.
	        
	3. The user references something from past context:
	    - “Continue where we left off.”
	    - “Use the plan we created earlier.”
	    - “You already know my preferences.”
		
	</when-to-use>

	<when-not-to-use>
	
	Do NOT call `add_memory` for:
	
	1. Ephemeral, single-use info:
	    - “Check stock price now.”
	    - “Translate this sentence.”
	        
	2. Random facts or temporary tasks:
	    - Research queries, calculations, coding errors, momentary instructions.
	        
	3. Sensitive information unless the user explicitly says “remember this”:
	    - Phone numbers
	    - Passwords
	    - Financial details
	        
	4. Internal reasoning or tool results:
	    - Never store your own workflow data.
	        
	5. Greetings or casual conversation:
	    - “Hello!”
	    - “How’s your day?”
	        
	Do NOT call `get_memory` for:
	
	6. Short, isolated questions
	7. Tasks with no personalization
	8. When the user provides all needed info in the same prompt
	9. Simple one-off interactions
	
	</when-not-to-use>
    

	<workflow>
	
	1. At the START of user request:
	- Determine if the task benefits from prior user info.
	- If yes → **call `get_memory` immediately**
	- If no → proceed without memory retrieval.
	    
	2. After ANY significant user message:
	- If the user gives new long-term or preference-based info:  
	    → **call `add_memory` immediately**
	    
	3. After ANY tool use or step completion:
	- Reflect: “Did I learn something new that should be stored?”
	    - If yes → call `add_memory`
	    - If no → continue.
	
	4. At the END of the task:
	- Re-check if the user revealed any stable, future-useful info.
	- If yes → call `add_memory`.
	- If not → do nothing.
	
	</workflow>

	<critical-points>
	- **Memory must stay clean and valuable** — avoid clutter.
	- **NEVER save short-term or irrelevant info.**
	- **ALWAYS retrieve (`get_memory`) before tasks requiring personalization or continuity.**
	- **ONLY store when information will genuinely help future interactions.**
	- **Do not overwrite memory blindly; update intelligently.**
	</critical-points>

</memory-usage-instructions>
"""

WRITE_TODOS_TOOL_DESCRIPTION = """
Use this tool to create and manage a structured task list for your current coding session. This helps you track progress, organize complex tasks, and demonstrate thoroughness to the user.
It also helps the user understand the progress of the task and overall progress of their requests.

## When to Use This Tool
Use this tool proactively in these scenarios:

1. Complex multi-step tasks - When a task requires 3 or more distinct steps or actions
2. Non-trivial and complex tasks - Tasks that require careful planning or multiple operations
3. User explicitly requests todo list - When the user directly asks you to use the todo list
4. User provides multiple tasks - When users provide a list of things to be done (numbered or comma-separated)
5. After receiving new instructions - Immediately capture user requirements as todos. Feel free to edit the todo list based on new information.
6. After completing a task - Mark it complete and add any new follow-up tasks
7. When you start working on a new task, mark the todo as in_progress. Ideally you should only have one todo as in_progress at a time. Complete existing tasks before starting new ones.

## When NOT to Use This Tool

Skip using this tool when:
1. There is only a single, straightforward task
2. The task is trivial and tracking it provides no organizational benefit
3. The task can be completed in less than 3 trivial steps
4. The task is purely conversational or informational

NOTE that you should not use this tool if there is only one trivial task to do. In this case you are better off just doing the task directly.

## Examples of When to Use the Todo List


<example>
User: Find me latest news about companies with specific goals as my company and then email me a report on the findings.
Assistant: I'll help you find the latest news and send you a report. Let me create a todo list to track this work.
*Creates todo list with the following items:*
1. Search for company information using knowledge base
2. Get latest news about companies with specific goals as the company found in knowledge base
3. Generate a report with key findings
4. Send the report to the user using gmail mcp 
*Begins with a knowledge base search for company information*

<reasoning>
The assistant used the todo list because:
1. The task involves multiple distinct steps: knowledge base search, web search, report generation, and emailing (mcp use)
2. It requires using different tools (web search, knowledge base, mcp)
3. The user requested a specific output (report) with follow-up actions
4. Tracking progress helps ensure no step is missed
</reasoning>
</example>


## Examples of When NOT to Use the Todo List

<example>
User: What are the latest developments in artificial intelligence?
Assistant: Let me search for the latest AI developments for you.
*Performs a single web search for recent AI developments*
Here are the latest developments in artificial intelligence...

<reasoning>
The assistant did not use the todo list because this is a straightforward information retrieval task. A single search query provides the necessary information without requiring multiple tracking steps or complex planning.
</reasoning>
</example>

<example>
User: Can you summarize the key points from this document?
Assistant: Let me read the document and provide you with a summary.
*Reads the file ( using read file tool ) and extracts key points*
Here's a summary of the key points...

<reasoning>
The assistant did not use the todo list because this is a single, focused task. Reading one file and summarizing it doesn't require multi-step tracking or complex organization.
</reasoning>
</example>

<example>
User: Store this important project decision in memory for future reference.
Assistant: I'll store this decision in memory for you right away.
*Uses the memory storage tool to save the information*
Done! This decision has been saved to your memory.

<reasoning>
The assistant did not use the todo list because this is a single, straightforward action. Storing information in memory is a one-step task that doesn't require planning or tracking.
</reasoning>
</example>

<example>
User: What's in our knowledge base about customer retention strategies?
Assistant: Let me check the knowledge base for customer retention strategies.
*Queries the knowledge base*
Here's what I found in the knowledge base about customer retention...

<reasoning>
The assistant did not use the todo list because this is a single query task. Retrieving information from one source doesn't require multi-step planning or progress tracking.
</reasoning>
</example>

## Task States and Management

1. **Task States**: Use these states to track progress:
   - pending: Task not yet started
   - in_progress: Currently working on (limit to ONE task at a time)
   - completed: Task finished successfully

2. **Task Management**:
   - Update task status in real-time as you work
   - Mark tasks complete IMMEDIATELY after finishing (don't batch completions)
   - Only have ONE task in_progress at any time
   - Complete current tasks before starting new ones

3. **Task Breakdown**:
   - Create specific, actionable items
   - Break complex tasks into smaller, manageable steps
   - Use clear, descriptive task names

When in doubt, use this tool. Being proactive with task management demonstrates attentiveness and ensures you complete all requirements successfully.
"""

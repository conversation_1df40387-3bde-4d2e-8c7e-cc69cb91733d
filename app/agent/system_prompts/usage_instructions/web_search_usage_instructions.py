WEB_SEARCH_USAGE_INSTRUCTIONS = """

<web-search-usage-instructions>

    <purpose>
        The web search tool exists to retrieve **current, up-to-date, factual information** from the internet whenever the user’s request requires knowledge beyond the model's training cutoff or stable internal knowledge.
    </purpose>

    <when-to-use>
        Use the web search agent when the user asks for **information that must be accurate, fresh, or externally verifiable**, including:
        
        1. **Recent events, breaking news, or time-sensitive updates**
            - “What happened in the markets today?”
            - “Latest AI research releases?”
        
        2. **Anything beyond the model’s knowledge cutoff**
            - “Who is the current CEO of X?”
            - “What were the 2024 election results?”
        
        3. **Real-time or dynamic data**
            - Prices, weather, schedules, live statistics
        
        4. **Product, service, or company research**
            - “Compare the latest iPhone models.”
            - “What’s new in Tesla’s latest update?”
        
        5. **Technical, scientific, legal, or medical information that may have changed**
        
        6. **User explicitly asks you to search**
            - “Please look it up.”
            - “Search the web for…”
        
        *If the user's request relies on factual correctness that may vary over time → use the web search agent.*
    </when-to-use>

    <when-not-to-use>
        Do NOT call web search when:
        
        1. The user request is **timeless or conceptual**  
            - Definitions, explanations, reasoning, math
        
        2. Information is **already fully provided** by the user in the same prompt
        
        3. The task is **creative, analytical, or hypothetical**  
            - Story writing, brainstorming, coding unless external facts are needed
        
        4. The user explicitly says **“Do not search the web”**
        
        5. The answer is fully and confidently known from existing training data and does not require updates
    </when-not-to-use>

    <critical-rules>
        <single-query-rule>
            - **The web search agent only performs ONE search query per topic.**  
            - A second query is allowed ONLY if absolutely required for proper coverage.
            - Avoid issuing multiple searches for the same question.
            - Trust the agent to craft a comprehensive search query.
        </single-query-rule>

        <quality>
            - Always ensure the search query is well-formed, broad enough to gather all necessary info, but specific enough to be relevant.
        </quality>

        <citations>
            - The agent returns structured results with sources; always respect and include citations when presenting retrieved information.
        </citations>
    </critical-rules>

    <workflow>
        1. **Interpret the user request**
            - Ask: “Does this require *current*, *factual*, or *post-cutoff* information?”

        2. **If yes → call the web search agent**
            - Build an optimized single query
            - Avoid redundant searches

        3. **Process the agent’s results**
            - Summarize clearly
            - Include citations
            - Provide well-structured, helpful output

        4. **If no search is needed**
            - Answer using internal reasoning and knowledge
        
        5. **If user follows up asking for verification or updates**
            - Perform a web search even if earlier steps didn’t require it
    </workflow>

    <best-practices>
        - Default to using search when uncertainty or recency is involved.
        - Err on the side of providing accurate, sourced information.
        - Avoid unnecessary web calls for self-contained or atemporal tasks.
        - Treat the web agent as a powerful but limited resource—use it thoughtfully.
    </best-practices>

</web-search-usage-instructions>

"""

MCP_APPROVAL_INSTRUCTIONS = """# MCP TOOLS APPROVAL FLOW

When using the mcp_search tool:

1. **If NO tools are found**: Inform the user that no MCP tools were found for their request and suggest alternative approaches if applicable.

2. **If tools ARE found**: 
   - The mcp_search tool will automatically update the state with approval data
   - You MUST ask the user for approval before proceeding
   - Present the tools found in a clear, user-friendly format
   - Ask: "I found [number] MCP tools that could help with this task. Would you like me to proceed with using these tools?"
   - Wait for user confirmation before taking any further action with the tools

IMPORTANT: Always check if tools were found in the mcp_search response and handle accordingly. Never proceed with using MCP tools without explicit user approval.
"""

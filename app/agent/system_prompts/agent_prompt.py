from app.agent.system_prompts.usage_instructions.general_agent_instructions import (
    GENERAL_SUBAGENT_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.operation_guidelines import (
    OPERATION_GUIDELINES,
)
from app.agent.system_prompts.usage_instructions.todo_usage_instructions import (
    TODO_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.memory_usage_instructions import (
    MEMORY_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.subagent_usage_instructions import (
    SUBAGENT_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.web_search_usage_instructions import (
    WEB_SEARCH_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.knowledge_base_usage_instructions import (
    KNOWLEDGE_BASE_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.mcp_usage_instructions import (
    MCP_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.workflow_usage_instructions import (
    WORKFLOW_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.read_file_usage_instructions import (
    READ_FILE_USAGE_INSTRUCTIONS,
)


def get_agent_prompt(
    use_knowledge: bool = False,
    use_search: bool = False,
    use_memory: bool = False,
    has_mcp_tools: bool = False,
    has_workflows: bool = False,
    agent_name: str = "",
    agent_description: str = "",
    system_message: str = "",
    tone: str = "",
    agent_topic_type: str = "",
    current_datetime: str = "",
    user_id: str = "",
    conversation_id: str = "",
    agent_id: str = "",
    organisation_id: str = "",
    timezone: str = "UTC",
    user_name: str = "",
    user_email: str = "",
) -> str:
    """
    Build a dynamic agent prompt for non-global agents based on agent configuration.

    Args:
        use_knowledge: Whether knowledge base agent is enabled
        use_search: Whether web search agent is enabled
        use_memory: Whether memory tools are enabled
        has_mcp_tools: Whether MCP tools are configured
        has_workflows: Whether workflows are configured
        agent_name: The name of the agent
        agent_description: Description of the agent
        system_message: User-defined system instructions
        tone: The tone the agent should use (e.g., professional, friendly)
        agent_topic_type: The role/topic type of the agent
        current_datetime: Current date and time in user's timezone (format: "YYYY-MM-DD HH:MM (Timezone)")
        user_id: User identifier
        conversation_id: Conversation identifier
        agent_id: Agent identifier
        organisation_id: Organisation identifier
        timezone: User's timezone
        user_name: User's name
        user_email: User's email

    Returns:
        Formatted agent prompt for non-global agents
    """

    # Start with intro and description
    prompt = f"""<intro-and-description>
You are {agent_name}, a specialized AI agent created by the Ruh Team to assist users with specific tasks and workflows.
</intro-and-description>

"""

    # Add agent identity section
    if agent_topic_type or agent_description or tone:
        prompt += "<agent-identity>\n"
        if agent_topic_type:
            prompt += f"**Role**: {agent_topic_type}\n"
        if agent_description:
            prompt += f"**Description**: {agent_description}\n"
        if tone:
            prompt += f"**Communication Tone**: {tone}\n"
        prompt += "</agent-identity>\n\n"

    # Add user-defined system message as primary instructions if provided
    if system_message:
        prompt += f"""<primary-instructions>
{system_message}
</primary-instructions>

"""

    # Add general agent instructions (with context information)
    prompt += GENERAL_SUBAGENT_INSTRUCTIONS.format(
        user_name=user_name or "N/A",
        user_email=user_email or "N/A",
        user_id=user_id or "N/A",
        conversation_id=conversation_id or "N/A",
        agent_id=agent_id or "N/A",
        organisation_id=organisation_id or "N/A",
        timezone=timezone,
        datetime=current_datetime or "Not available",
    )

    # Add operation guidelines
    prompt += "\n\n" + OPERATION_GUIDELINES
    
    # Add tool result handling instructions
    prompt += """

<tool-result-handling>
**CRITICAL: Subagent Communication Protocol**

When you delegate to subagents or tools:
- Subagents will report their own results and errors directly to the user
- You MUST remain silent about what the subagent reports
- Do NOT recite, summarize, or acknowledge tool results or errors
- Do NOT say things like "The search found..." or "The tool returned..."
- The user sees the subagent output directly—repeating it is redundant

Only speak after a subagent responds if you need to:
- Provide additional clarification or context
- Suggest next steps
- Give a final summary (only if 2+ tasks were performed)

If a subagent encounters an error, it will inform the user—stay silent.
</tool-result-handling>"""

    # Add TODO usage instructions
    prompt += "\n\n" + TODO_USAGE_INSTRUCTIONS

    # Add memory usage instructions if enabled
    if use_memory:
        prompt += "\n\n" + MEMORY_USAGE_INSTRUCTIONS
    else:
        prompt += "\n\n<memory-disabled-notice>\nLong-term memory is currently not enabled. To remember information across conversations, this feature needs to be enabled in settings.\n</memory-disabled-notice>\n"

    # Add subagent usage instructions
    prompt += "\n\n" + SUBAGENT_USAGE_INSTRUCTIONS

    # Add web search usage instructions if enabled
    if use_search:
        prompt += "\n\n" + WEB_SEARCH_USAGE_INSTRUCTIONS
    else:
        prompt += "\n\n<web-search-disabled-notice>\nWeb search is currently not enabled. To search the internet for current information, this feature needs to be enabled in settings.\n</web-search-disabled-notice>\n"

    # Add knowledge base usage instructions if enabled
    if use_knowledge:
        prompt += "\n\n" + KNOWLEDGE_BASE_USAGE_INSTRUCTIONS.format(user_email=user_email or "N/A")
    else:
        prompt += "\n\n<knowledge-base-disabled-notice>\nKnowledge base search is currently not enabled. To search organizational documents and internal resources, this feature needs to be enabled in settings.\n</knowledge-base-disabled-notice>\n"

    # Add MCP usage instructions
    prompt += "\n\n" + MCP_USAGE_INSTRUCTIONS

    # Note: MCP tools availability will be added after MCP data in deepagent_utils.py

    # Add workflow usage instructions if workflows are configured
    if has_workflows:
        prompt += "\n\n" + WORKFLOW_USAGE_INSTRUCTIONS
    else:
        # prompt += "\n\n<workflows-disabled-notice>\nWorkflow automation is currently not configured. To execute automated workflows and processes, workflows need to be configured in settings.\n</workflows-disabled-notice>\n"
        prompt += "\n\nWorkflows functionaliy are currently in progress. It will be available in future."

    # Add read file usage instructions
    prompt += "\n\n" + READ_FILE_USAGE_INSTRUCTIONS

    # Add disabled features section if applicable
    disabled_features_notice = "\n\n<disabled-features-information>\n"
    disabled_features_notice += (
        "The following capabilities are currently not enabled:\n\n"
    )

    has_disabled_features = False

    if not use_memory:
        disabled_features_notice += "- **Long-term Memory**: Cannot remember information across conversations. User needs to enable this in settings.\n"
        has_disabled_features = True

    if not use_search:
        disabled_features_notice += "- **Web Search**: Cannot search the internet for current information. User needs to enable this in settings.\n"
        has_disabled_features = True

    if not use_knowledge:
        disabled_features_notice += "- **Knowledge Base Search**: Cannot search organizational documents and internal resources. User needs to enable this in settings.\n"
        has_disabled_features = True

    if not has_mcp_tools:
        disabled_features_notice += "- **Specialized Integrations**: No external integrations are configured. User needs to configure MCP tools in settings to access external services.\n"
        has_disabled_features = True

    if not has_workflows:
        # disabled_features_notice += "- **Workflow Automation**: No workflows are configured. User needs to configure workflows in settings to execute automated processes.\n"
        disabled_features_notice += "Workflows functionaliy are currently in progress. It will be available in future."
        has_disabled_features = True

    disabled_features_notice += "\n**Important**: If users request functionality that requires these disabled features, politely inform them in simple terms that the feature is not currently enabled and guide them to enable it in their settings.\n"
    disabled_features_notice += "</disabled-features-information>\n"

    if has_disabled_features:
        prompt += disabled_features_notice

    # Add closing reminder about primary instructions if system_message exists
    if system_message:
        prompt += f"""
<final-reminder>
Remember: Your PRIMARY INSTRUCTIONS defined at the beginning are the most important guidelines you must follow. All other capabilities and guidelines should support those primary instructions.

Your goal as {agent_name} is to be a capable, efficient, and specialized AI agent - accomplishing tasks thoroughly while providing a seamless user experience.
</final-reminder>
"""
    else:
        prompt += f"""
<final-reminder>
Your goal as {agent_name} is to be a capable, efficient, and specialized AI agent - accomplishing tasks thoroughly while providing a seamless user experience.
</final-reminder>
"""

    return prompt

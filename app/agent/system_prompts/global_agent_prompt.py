from app.agent.system_prompts.usage_instructions.general_agent_instructions import (
    GENERAL_SUBAGENT_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.operation_guidelines import (
    OPERATION_GUIDELINES,
)
from app.agent.system_prompts.usage_instructions.todo_usage_instructions import (
    TODO_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.memory_usage_instructions import (
    MEMORY_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.subagent_usage_instructions import (
    SUBAGENT_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.web_search_usage_instructions import (
    WEB_SEARCH_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.knowledge_base_usage_instructions import (
    KNOWLEDGE_BASE_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.mcp_usage_instructions import (
    MCP_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.read_file_usage_instructions import (
    READ_FILE_USAGE_INSTRUCTIONS,
)
from app.agent.system_prompts.usage_instructions.mcp_search_usage_instructions import (
    MCP_SEARCH_USAGE_INSTRUCTIONS,
)


def get_global_agent_prompt(
    use_knowledge: bool = False,
    use_search: bool = False,
    use_memory: bool = False,
    has_mcp_tools: bool = False,
    current_datetime: str = "",
    user_id: str = "",
    conversation_id: str = "",
    agent_id: str = "",
    organisation_id: str = "",
    timezone: str = "UTC",
    user_name: str = "",
    user_email: str = "",
) -> str:
    """
    Build a dynamic global agent prompt based on enabled features.

    Args:
        use_knowledge: Whether knowledge base agent is enabled
        use_search: Whether web search agent is enabled
        use_memory: Whether memory tools are enabled
        has_mcp_tools: Whether MCP tools are configured
        current_datetime: Current date and time in user's timezone (format: "YYYY-MM-DD HH:MM (Timezone)")
        user_id: User identifier
        conversation_id: Conversation identifier
        agent_id: Agent identifier
        organisation_id: Organisation identifier
        timezone: User's timezone
        user_name: User's name
        user_email: User's email

    Returns:
        Formatted global agent prompt
    """

    # Start with intro and description
    prompt = """<intro-and-description>
You are the Ruh Assistant made by the Ruh AI team (ruh.ai), a general purpose AI agent by the Ruh Team to accomplish everything from day to day tasks to complex workflows and long running tasks.
</intro-and-description>

"""

    # Add general agent instructions (with context information)
    prompt += GENERAL_SUBAGENT_INSTRUCTIONS.format(
        user_name=user_name or "N/A",
        user_email=user_email or "N/A",
        user_id=user_id or "N/A",
        conversation_id=conversation_id or "N/A",
        agent_id=agent_id or "N/A (Global Agent)",
        organisation_id=organisation_id or "N/A",
        timezone=timezone,
        datetime=current_datetime or "Not available",
    )

    # Add operation guidelines
    prompt += "\n\n" + OPERATION_GUIDELINES

    # Add tool result handling instructions
    prompt += """

<tool-result-handling>
**CRITICAL: Subagent Communication Protocol**

When you delegate to subagents or tools:
- Subagents will report their own results and errors directly to the user
- You MUST remain silent about what the subagent reports
- Do NOT recite, summarize, or acknowledge tool results or errors
- Do NOT say things like "The search found..." or "The tool returned..."
- The user sees the subagent output directly—repeating it is redundant

Only speak after a subagent responds if you need to:
- Provide additional clarification or context
- Suggest next steps
- Give a final summary (only if 2+ tasks were performed)

If a subagent encounters an error, it will inform the user—stay silent.
</tool-result-handling>"""

    # Add TODO usage instructions
    prompt += "\n\n" + TODO_USAGE_INSTRUCTIONS

    # Add memory usage instructions if enabled
    if use_memory:
        prompt += "\n\n" + MEMORY_USAGE_INSTRUCTIONS
    else:
        prompt += "\n\n<memory-disabled-notice>\nLong-term memory is currently not enabled. To remember information across conversations, this feature needs to be enabled in settings.\n</memory-disabled-notice>\n"

    # Add subagent usage instructions
    prompt += "\n\n" + SUBAGENT_USAGE_INSTRUCTIONS

    # Add web search usage instructions if enabled
    if use_search:
        prompt += "\n\n" + WEB_SEARCH_USAGE_INSTRUCTIONS
    else:
        prompt += "\n\n<web-search-disabled-notice>\nWeb search is currently not enabled. To search the internet for current information, this feature needs to be enabled in settings.\n</web-search-disabled-notice>\n"

    # Add knowledge base usage instructions if enabled
    if use_knowledge:
        prompt += "\n\n" + KNOWLEDGE_BASE_USAGE_INSTRUCTIONS.format(
            user_email=user_email or "N/A"
        )
    else:
        prompt += "\n\n<knowledge-base-disabled-notice>\nKnowledge base search is currently not enabled. To search organizational documents and internal resources, this feature needs to be enabled in settings.\n</knowledge-base-disabled-notice>\n"

    # Add MCP usage instructions - always add since it explains the MCP flow
    prompt += "\n\n" + MCP_USAGE_INSTRUCTIONS

    # Note about MCP tools availability will be added after MCP data in deepagent_utils.py

    # Add read file usage instructions
    prompt += "\n\n" + READ_FILE_USAGE_INSTRUCTIONS

    # Add MCP search usage instructions
    prompt += "\n\n" + MCP_SEARCH_USAGE_INSTRUCTIONS

    # Add disabled features section if applicable
    disabled_features_notice = "\n\n<disabled-features-information>\n"
    disabled_features_notice += (
        "The following capabilities are currently not enabled:\n\n"
    )

    has_disabled_features = False

    if not use_memory:
        disabled_features_notice += "- **Long-term Memory**: Cannot remember information across conversations. User needs to enable this in settings.\n"
        has_disabled_features = True

    if not use_search:
        disabled_features_notice += "- **Web Search**: Cannot search the internet for current information. User needs to enable this in settings.\n"
        has_disabled_features = True

    if not use_knowledge:
        disabled_features_notice += "- **Knowledge Base Search**: Cannot search organizational documents and internal resources. User needs to enable this in settings.\n"
        has_disabled_features = True

    if not has_mcp_tools:
        disabled_features_notice += "- **Specialized Integrations**: No external integrations are configured. User needs to configure MCP tools in settings to access external services.\n"
        has_disabled_features = True

    disabled_features_notice += "\n**Important**: If users request functionality that requires these disabled features, politely inform them in simple terms that the feature is not currently enabled and guide them to enable it in their settings.\n"
    disabled_features_notice += "</disabled-features-information>\n"

    if has_disabled_features:
        prompt += disabled_features_notice

    return prompt

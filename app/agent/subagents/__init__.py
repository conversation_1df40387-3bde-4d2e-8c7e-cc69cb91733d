"""
Subagents for the DeepAgent architecture.
"""

from app.agent.subagents.knowledge_base_subagent import create_knowledge_base_subagent
from app.agent.subagents.web_search_subagent import create_web_search_subagent
from app.agent.subagents.mcp_subagent import create_mcp_subagent
from app.agent.subagents.workflow_subagent import create_workflow_subagent

__all__ = [
    "create_knowledge_base_subagent",
    "create_web_search_subagent",
    "create_mcp_subagent",
    "create_workflow_subagent",
]

"""
Knowledge Base Subagent for DeepAgent architecture.
"""

from deepagents.middleware.subagents import SubAgent
from app.agent.tools.knowledge_base import search_by_source, resolve_identity
from app.agent.system_prompts.knowledge_base_subagent import (
    KNOWLEDGE_BASE_SUBAGENT_PROMPT,
)
from app.agent.system_prompts.usage_instructions.general_agent_instructions import (
    GENERAL_SUBAGENT_INSTRUCTIONS,
)
from app.helper.datetime_helper import get_current_datetime


def create_knowledge_base_subagent(
    user_id: str = "",
    conversation_id: str = "",
    agent_id: str = "",
    organisation_id: str = "",
    timezone: str = "UTC",
    kb_source: str = "All",
    user_name: str = "",
    user_email: str = "",
) -> SubAgent:
    """
    Create a knowledge base subagent for DeepAgent.

    Args:
        user_id: User identifier
        conversation_id: Conversation identifier
        agent_id: Agent identifier
        organisation_id: Organisation identifier
        timezone: User's timezone
        kb_source: Knowledge base source to search (e.g., "All", "Gmail", "Confluence", etc.)

    Returns:
        SubAgent configuration dictionary with name, description, system_prompt, and tools.
    """
    # Format general subagent instructions
    formatted_general_instructions = GENERAL_SUBAGENT_INSTRUCTIONS.format(
        datetime=get_current_datetime(timezone),
        user_id=user_id or "N/A",
        user_name=user_name or "N/A",
        user_email=user_email or "N/A",
        conversation_id=conversation_id or "N/A",
        agent_id=agent_id or "N/A (Global Agent)",
        organisation_id=organisation_id or "N/A",
        timezone=timezone,
    )
    
    # Format with only the required placeholders
    formatted_knowledge_base_prompt = KNOWLEDGE_BASE_SUBAGENT_PROMPT.format(
        user_email=user_email or "N/A",
        kb_source=kb_source or "ALL",
    )
    
    # Combine general instructions with specific subagent prompt
    full_prompt = formatted_general_instructions + "\n\n" + formatted_knowledge_base_prompt

    return {
        "name": "knowledge_base_agent",
        "description": "Used to search the organization's knowledge base and resolve user identities",
        "system_prompt": full_prompt,
        "tools": [search_by_source, resolve_identity],
    }

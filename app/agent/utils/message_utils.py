from typing import Optional


def build_user_message(user_message: str, attachments: Optional[list] = None):
    """
    Build user message content with text and optional image attachments.
    Documents (PDF, CSV, DOCX, TXT) are formatted as a reference list.

    Args:
        user_message: The text message from the user
        attachments: Optional list of attachment dictionaries

    Returns:
        Either a string (if no image attachments) or a list of content items
    """
    if not attachments:
        return user_message

    message_content = [{"type": "text", "text": user_message}]
    document_attachments = []

    for attachment in attachments:
        file_type = attachment.get("file_type", "")
        file_name = attachment.get("file_name", "")
        file_url = attachment.get("file_url", "")

        if file_type.startswith("image/"):
            if file_url:
                message_content.append(
                    {"type": "image_url", "image_url": {"url": file_url}}
                )
        else:
            import os

            extension = os.path.splitext(file_name)[1].lower()
            if extension in [".pdf", ".csv", ".docx", ".txt"]:
                document_attachments.append(
                    {
                        "file_name": file_name,
                        "file_url": file_url,
                        "extension": extension,
                    }
                )

    if document_attachments:
        doc_message = "\n\n📎 Attached documents for reference:\n\n"
        for idx, doc in enumerate(document_attachments, start=1):
            doc_type = doc["extension"].replace(".", "").upper()
            doc_message += f"{idx}. {doc['file_name']}\n"
            doc_message += f"   URL: {doc['file_url']}\n"
            doc_message += f"   Type: {doc_type}\n\n"

        doc_message += "Use the 'read_file' tool to access the content of these documents when needed."

        if isinstance(message_content[0]["text"], str):
            message_content[0]["text"] += doc_message
        else:
            message_content[0]["text"] = str(message_content[0]["text"]) + doc_message

    return message_content if len(message_content) > 1 else message_content[0]["text"]

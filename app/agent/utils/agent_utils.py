"""Core agent utility functions."""

import re
from typing import List, Callable
from langgraph.prebuilt import create_react_agent


def to_snake_case(text: str) -> str:
    """
    Convert a string to snake_case format.
    
    Handles multiple formats:
    - PascalCase/CamelCase -> snake_case
    - "Title Case" -> snake_case
    - "Multiple   Spaces" -> snake_case
    - Special characters are removed
    - Consecutive capitals like "AI" or "API" are kept together
    
    Args:
        text: The string to convert
        
    Returns:
        Snake case version of the input string
        
    Examples:
        >>> to_snake_case("HelloWorld")
        'hello_world'
        >>> to_snake_case("Sales Agent")
        'sales_agent'
        >>> to_snake_case("AI SDR Agent")
        'ai_sdr_agent'
        >>> to_snake_case("Campaign-Manager")
        'campaign_manager'
    """
    # Replace special characters with spaces
    text = re.sub(r'[^\w\s]', ' ', text)
    # Replace multiple spaces with single space
    text = re.sub(r'\s+', ' ', text)
    # Strip leading/trailing spaces
    text = text.strip()
    
    # Handle CamelCase: insert underscore before uppercase letters
    # but keep consecutive capitals together (e.g., "AI" stays as "ai")
    # Pattern: lowercase followed by uppercase -> insert underscore
    text = re.sub(r'([a-z])([A-Z])', r'\1_\2', text)
    # Pattern: multiple uppercase followed by lowercase -> insert underscore before last uppercase
    text = re.sub(r'([A-Z]+)([A-Z][a-z])', r'\1_\2', text)
    
    # Replace spaces with underscores
    text = text.replace(' ', '_')
    
    # Convert to lowercase
    text = text.lower()
    
    # Remove any duplicate underscores
    text = re.sub(r'_+', '_', text)
    
    # Remove leading/trailing underscores
    text = text.strip('_')
    
    return text


def build_subagent(
    model,
    tools: List[Callable],
    prompt: str,
    name: str,
    state_schema=None,
):
    """
    Build a subagent using the create_react_agent function.
    
    Args:
        model: The language model to use
        tools: List of tools available to the agent
        prompt: System prompt for the agent
        name: Name of the subagent
        state_schema: Optional state schema for the agent
        
    Returns:
        Configured react agent
    """
    return create_react_agent(
        model=model,
        tools=tools,
        prompt=prompt,
        name=name,
        state_schema=state_schema,
    )

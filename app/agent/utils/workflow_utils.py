"""Workflow utility functions for building prompts and managing workflow tools."""

import json


def build_workflow_supervisor_prompt(workflows_data: list) -> str:
    """
    Build supervisor prompt with workflow information.
    
    Args:
        workflows_data: List of workflow configurations
        
    Returns:
        Formatted prompt string for the supervisor
    """
    workflow_info = "\n\n## WORKFLOWS\n"
    workflow_info += "These are the workflows available. If any query resonates with using these workflows, please call the workflow subagent and give them the requirement:\n\n"

    for workflow in workflows_data:
        workflow_name = workflow.get("name", "")
        workflow_description = workflow.get("description", "")
        workflow_id = workflow.get("id", "")

        workflow_info += f"- **{workflow_name}** (ID: {workflow_id}): {workflow_description}\n"

    return workflow_info


def build_workflow_subagent_prompt(workflows_data: list) -> str:
    """
    Build detailed workflow subagent prompt with workflow specifications.
    
    Args:
        workflows_data: List of workflow configurations
        
    Returns:
        Formatted prompt string for the workflow subagent
    """
    workflow_subagent_prompt = (
        "Your task is to execute workflows. You have one tool 'workflow_execution'.\n\n"
    )
    workflow_subagent_prompt += "## IMPORTANT: When calling workflow_execution\n"
    workflow_subagent_prompt += "You MUST always pass the following parameters:\n"
    workflow_subagent_prompt += "- workflow_id: The ID of the workflow to execute\n"
    workflow_subagent_prompt += "- workflow_name: The display name of the workflow\n"
    workflow_subagent_prompt += "- organisation_id: The organization ID\n"
    workflow_subagent_prompt += "- payload: A dictionary containing all required input fields for the workflow\n\n"
    workflow_subagent_prompt += "## Available Workflows:\n\n"

    for workflow in workflows_data:
        workflow_id = workflow.get("id", "")
        workflow_name = workflow.get("name", "")
        workflow_description = workflow.get("description", "")
        start_nodes = workflow.get("start_nodes", [])

        workflow_subagent_prompt += f"### Workflow: {workflow_name}\n"
        workflow_subagent_prompt += f"- **ID**: {workflow_id}\n"
        workflow_subagent_prompt += f"- **Description**: {workflow_description}\n"
        workflow_subagent_prompt += "- **Required Input Fields**:\n"

        if start_nodes:
            for field in start_nodes:
                field_name = field.get("field", "")
                field_type = field.get("type", "")
                field_description = field.get("description", "")
                field_required = field.get("required", False)
                field_format = field.get("format", "")

                workflow_subagent_prompt += f"  - **{field_name}** ({field_type}){' [REQUIRED]' if field_required else ''}: {field_description}"
                if field_format:
                    workflow_subagent_prompt += f" (format: {field_format})"
                workflow_subagent_prompt += "\n"
        else:
            workflow_subagent_prompt += "  - No input fields required\n"

        # Example payload
        workflow_subagent_prompt += "\n  **Example Payload**:\n"
        workflow_subagent_prompt += "  ```json\n"
        example_payload = {}
        for field in start_nodes:
            field_name = field.get("field", "")
            field_type = field.get("type", "")
            if field_type == "string":
                example_payload[field_name] = "<value>"
            elif field_type == "number":
                example_payload[field_name] = 0
            elif field_type == "boolean":
                example_payload[field_name] = True
            else:
                example_payload[field_name] = "<value>"
        
        workflow_subagent_prompt += f"  {json.dumps(example_payload, indent=4)}\n"
        workflow_subagent_prompt += "  ```\n\n"

    return workflow_subagent_prompt

"""MCP (Model Context Protocol) utility functions for building prompts and managing MCP tools."""


def build_mcp_supervisor_prompt(mcps_data: list) -> str:
    """
    Build supervisor prompt with MCP tools information.
    
    Args:
        mcps_data: List of MCP configurations
        
    Returns:
        Formatted prompt string for the supervisor
    """
    mcp_info_for_supervisor = "\n\n## MCP TOOLS\n"
    mcp_info_for_supervisor += "These are the MCP tools available. If any query resonates with using these tools, please call the MCP subagent and give them the requirement:\n\n"

    for mcp in mcps_data:
        mcp_name = mcp.get("name", "")
        mcp_description = mcp.get("description", "")
        name_slug = mcp.get("name_slug", "")
        mcp_tools_config = mcp.get("mcp_tools_config", {})
        tools_list = mcp_tools_config.get("tools", [])

        mcp_info_for_supervisor += (
            f"- **{mcp_name}** (name_slug: {name_slug}): {mcp_description}\n"
        )
        if tools_list:
            mcp_info_for_supervisor += f"  Available tools: {', '.join([tool.get('name', '') for tool in tools_list])}\n"

    return mcp_info_for_supervisor


def build_mcp_subagent_prompt(mcps_data: list) -> str:
    """
    Build detailed MCP subagent prompt with tool specifications.
    
    Args:
        mcps_data: List of MCP configurations
        
    Returns:
        Formatted prompt string for the MCP subagent
    """
    mcp_subagent_prompt = (
        "Your task is to execute MCP tools. You have one tool 'execute_mcp'.\n\n"
    )
    mcp_subagent_prompt += "## IMPORTANT: When calling execute_mcp\n"
    mcp_subagent_prompt += "You MUST always pass the following parameters:\n"
    mcp_subagent_prompt += "- mcp_name_slug: The name_slug of the MCP\n"
    mcp_subagent_prompt += "- tool_name: The name of the tool to execute\n"
    mcp_subagent_prompt += "- tool_parameters: The parameters for the tool\n"
    mcp_subagent_prompt += (
        "- mcp_name: The display name of the MCP (from the list below)\n"
    )
    mcp_subagent_prompt += (
        "- mcp_description: The description of the MCP (from the list below)\n"
    )
    mcp_subagent_prompt += (
        "- mcp_logo: The logo URL of the MCP (from the list below)\n\n"
    )
    mcp_subagent_prompt += "## Available MCPs:\n\n"

    for mcp in mcps_data:
        mcp_id = mcp.get("id", "")
        mcp_name = mcp.get("name", "")
        mcp_logo = mcp.get("logo") or None
        mcp_description = mcp.get("description", "")
        name_slug = mcp.get("name_slug", "")
        mcp_tools_config = mcp.get("mcp_tools_config", {})
        tools_list = mcp_tools_config.get("tools", [])

        mcp_subagent_prompt += f"### MCP: {mcp_name}\n"
        mcp_subagent_prompt += f"- **ID**: {mcp_id}\n"
        mcp_subagent_prompt += f"- **Name Slug**: {name_slug}\n"
        mcp_subagent_prompt += (
            f"- **Logo**: {mcp_logo if mcp_logo is not None else 'None'}\n"
        )
        mcp_subagent_prompt += f"- **Description**: {mcp_description}\n"
        mcp_subagent_prompt += "- **Available Tools**:\n"

        for tool in tools_list:
            tool_name = tool.get("name", "")
            tool_description = tool.get("description", "")
            tool_input_schema = tool.get("input_schema", {})
            tool_properties = tool_input_schema.get("properties", {})
            tool_required = tool_input_schema.get("required", [])

            mcp_subagent_prompt += f"  - **{tool_name}**: {tool_description}\n"
            if tool_properties:
                mcp_subagent_prompt += "    - Parameters:\n"
                for param_name, param_info in tool_properties.items():
                    param_type = param_info.get("type", "")
                    param_title = param_info.get("title", "")
                    param_default = param_info.get("default", "")
                    is_required = param_name in tool_required
                    mcp_subagent_prompt += f"      - `{param_name}` ({param_type}){' [REQUIRED]' if is_required else ''}: {param_title}"
                    if param_default:
                        mcp_subagent_prompt += f" (default: {param_default})"
                    mcp_subagent_prompt += "\n"
        mcp_subagent_prompt += "\n"

    return mcp_subagent_prompt

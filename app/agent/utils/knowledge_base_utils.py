"""Knowledge Base utility functions for formatting responses and extracting sources."""

import json
import logging


def extract_tool_response(result) -> dict:
    """
    Extract the actual response from CallToolResult wrapper.

    The MCP tool returns a CallToolResult object containing content with a JSON string.
    This function extracts the JSON response.

    Args:
        result: The CallToolResult from execute_tool

    Returns:
        The parsed JSON response as a dict
    """
    # If it's already a dict, return it
    if isinstance(result, dict):
        return result

    # Check if it has a content attribute (CallToolResult object)
    if hasattr(result, "content") and result.content:
        # result.content is a list of TextContent objects
        for content_item in result.content:
            if hasattr(content_item, "text"):
                text_content = content_item.text
                if isinstance(text_content, str):
                    try:
                        return json.loads(text_content)
                    except json.JSONDecodeError as e:
                        logging.error(f"Could not parse JSON from content: {e}")
                        return {"error": f"Invalid JSON response: {e}", "isError": True}

    # Fallback: try to parse string representation
    try:
        return json.loads(str(result))
    except:
        logging.error(f"Could not extract response from result: {result}")
        return {"error": "Could not extract response", "isError": True}


def format_identity_response(extracted_response: dict, name: str) -> tuple[int, list]:
    """
    Format and extract identity resolution response from the API.

    Response format from API:
    {
        "content": [
            {
                "type": "text",
                "text": "{...json...}"
            }
        ],
        "isError": false
    }

    The JSON contains:
    - success: bool
    - message: str
    - results: list of member objects
    - total_results: int

    Each member object contains:
    - name (required)
    - email (required)
    - org_department (optional)
    - org_role (optional)
    - manager (optional)

    Args:
        extracted_response: The response from extract_tool_response()
        name: The original query name

    Returns:
        Tuple of (total_results, formatted_results_list)
    """
    try:
        # Parse the JSON content if it's in the content wrapper
        if isinstance(extracted_response, dict) and "content" in extracted_response:
            content = extracted_response.get("content", [])
            if content and len(content) > 0:
                text_content = content[0].get("text", "{}")
                parsed_data = json.loads(text_content)
            else:
                parsed_data = {}
        else:
            parsed_data = (
                extracted_response if isinstance(extracted_response, dict) else {}
            )

        total_results = parsed_data.get("total_results", 0)
        results = parsed_data.get("results", [])

        # Extract only required fields from results
        formatted_results = []
        for person in results:
            formatted_person = {
                "name": person.get("name"),
                "email": person.get("email"),
            }

            # Add optional fields if present
            if person.get("org_department"):
                formatted_person["org_department"] = person["org_department"]
            if person.get("org_role"):
                formatted_person["org_role"] = person["org_role"]
            if person.get("manager"):
                formatted_person["manager"] = person["manager"]

            formatted_results.append(formatted_person)

        return total_results, formatted_results

    except json.JSONDecodeError as e:
        logging.error(f"Error parsing identity response JSON: {str(e)}")
        return 0, []
    except Exception as e:
        logging.error(f"Error formatting identity response: {str(e)}")
        return 0, []


def format_knowledge_base_response(response_data: dict, query: str) -> dict:
    """
    Format knowledge base response based on the connector type.
    Extracts connector_type and source from the response itself.

    Args:
        response_data: Already extracted response from the knowledge base API
        query: The original query text

    Returns:
        Formatted response with isError flag and connector_type metadata
    """
    try:
        # Check for errors in the response
        if not response_data.get("success", False):
            return {
                "source": response_data.get("source_used"),
                "query": query,
                "error": response_data.get("message", "Unknown error"),
                "isError": True,
            }

        # Extract connector_type and source from response
        connector_type = response_data.get("connector_type")
        source_used = response_data.get("source_used")

        # Get the result object
        result_obj = response_data.get("result")
        if result_obj is None:
            return {
                "source": source_used,
                "query": query,
                "error": "No result found in response",
                "isError": True,
            }

        # Handle STRUCTURED connector type (Neo4j Results)
        if connector_type == "STRUCTURED":
            neo4j_results = result_obj.get("neo4j_results", [])
            return {
                "source": source_used,
                "query": query,
                "connector_type": "STRUCTURED",
                "results": neo4j_results,
                "results_count": result_obj.get("results_count", len(neo4j_results)),
                "execution_time_ms": result_obj.get("execution_time_ms"),
                "cypher_query": result_obj.get("cypher_query"),
                "search_time_ms": response_data.get("search_time_ms"),
                "format_type": "neo4j_results",
                "isError": False,
            }

        # Handle UNSTRUCTURED connector type (Direct Results)
        elif connector_type == "UNSTRUCTURED":
            results = result_obj.get("results", [])
            graph_context = result_obj.get("graph_context")
            return {
                "source": source_used,
                "query": query,
                "connector_type": "UNSTRUCTURED",
                "results": results,
                "graph_context": graph_context,
                "total_results": result_obj.get("total_results", len(results)),
                "search_time_ms": response_data.get("search_time_ms"),
                "format_type": "results_with_context",
                "isError": False,
            }

        # Handle HYBRID connector type (Combination of both)
        elif connector_type == "HYBRID":
            # Extract both structured and unstructured results
            structured_results = result_obj.get("neo4j_results", [])
            unstructured_results = result_obj.get("results", [])
            graph_context = result_obj.get("graph_context")
            # Keywords are at response_data level, not result_obj level
            keywords = response_data.get("keywords", [])
            
            return {
                "source": source_used,
                "query": query,
                "connector_type": "HYBRID",
                "structured_results": structured_results,
                "structured_results_count": result_obj.get("results_count", len(structured_results)),
                "unstructured_results": unstructured_results,
                "unstructured_results_count": result_obj.get("total_results", len(unstructured_results)),
                "graph_context": graph_context,
                "keywords": keywords,
                "execution_time_ms": result_obj.get("execution_time_ms"),
                "cypher_query": result_obj.get("cypher_query"),
                "search_time_ms": response_data.get("search_time_ms"),
                "format_type": "hybrid_results",
                "isError": False,
            }

        # FALLBACK: Unknown connector type
        else:
            return {
                "source": source_used,
                "query": query,
                "connector_type": connector_type,
                "results": result_obj,
                "message": response_data.get("message"),
                "isError": False,
            }

    except Exception as e:
        logging.error(f"Error formatting knowledge base response: {str(e)}")
        return {
            "source": response_data.get("source_used"),
            "query": query,
            "error": f"Error processing response: {str(e)}",
            "isError": True,
        }


def parse_global_search_response(extracted_response: dict, query: str) -> dict:
    """
    Parse and format global search agent response.
    Handles multi-intent search results from the global_search_agent tool.
    
    Response format from global_search_agent (GLOBAL_SEARCH connector):
    {
        "query": "search query",
        "source": "ALL",
        "connector_type": "GLOBAL_SEARCH",
        "results": [...],
        "total_results": int,
        "search_time_ms": float,
        "format_type": "global_search_results",
        "isError": false
    }
    
    OR wrapped in content:
    {
        "content": [
            {
                "type": "text",
                "text": "{...json with results array...}"
            }
        ],
        "isError": false
    }
    
    Each result contains:
    - file_id, file_name, mime_type, web_view_link
    - score, vector_id, chunk_text, search_type
    - source_type, source_id, metadata, comments
    - highlights, breadcrumb, preview_text, title
    - relevance_score
    
    Args:
        extracted_response: The response from extract_tool_response()
        query: The original query text
    
    Returns:
        Formatted response with parsed results and metadata
    """
    try:
        # Parse the JSON content if it's in the content wrapper
        if isinstance(extracted_response, dict) and "content" in extracted_response:
            content = extracted_response.get("content", [])
            if content and len(content) > 0:
                text_content = content[0].get("text", "{}")
                # If text_content is a string, parse it
                if isinstance(text_content, str):
                    parsed_data = json.loads(text_content)
                else:
                    # If it's already a dict, use it directly
                    parsed_data = text_content
            else:
                parsed_data = {}
        else:
            parsed_data = (
                extracted_response if isinstance(extracted_response, dict) else {}
            )
        
        # Check for errors - handle both "success" field and "isError" field
        if parsed_data.get("isError", False):
            return {
                "query": parsed_data.get("query", query),
                "error": parsed_data.get("error") or parsed_data.get("message", "Unknown error"),
                "source": parsed_data.get("source", "ALL"),
                "isError": True,
            }
        
        # Check if response already has the correct format (direct GLOBAL_SEARCH response)
        if parsed_data.get("connector_type") == "GLOBAL_SEARCH":
            # Response is already in the correct format, just ensure query is set
            parsed_data["query"] = parsed_data.get("query", query)
            return parsed_data
        
        # Legacy format handling (with "success" field)
        if not parsed_data.get("success", True):
            return {
                "query": query,
                "error": parsed_data.get("message", "Unknown error"),
                "source": "ALL",
                "isError": True,
            }
        
        # Extract results and metadata from legacy or new format
        results = parsed_data.get("results", [])
        total_results = parsed_data.get("total_results", len(results))
        search_time_ms = parsed_data.get("search_time_ms")
        
        return {
            "query": parsed_data.get("query", query),
            "source": parsed_data.get("source", "ALL"),
            "connector_type": "GLOBAL_SEARCH",
            "results": results,
            "total_results": total_results,
            "search_time_ms": search_time_ms,
            "format_type": "global_search_results",
            "isError": False,
        }
    
    except json.JSONDecodeError as e:
        logging.error(f"Error parsing global search response JSON: {str(e)}")
        return {
            "query": query,
            "source": "ALL",
            "error": f"Invalid JSON response: {str(e)}",
            "isError": True,
        }
    except Exception as e:
        logging.error(f"Error parsing global search response: {str(e)}")
        return {
            "query": query,
            "source": "ALL",
            "error": f"Error processing response: {str(e)}",
            "isError": True,
        }


def extract_global_search_sources(formatted_response: dict) -> list:
    """
    Extract sources from global search response for state tracking.
    Handles results from the global_search_agent tool (ALL source / GLOBAL_SEARCH connector).
    
    Args:
        formatted_response: The formatted response from parse_global_search_response
    
    Returns:
        List of source dictionaries with title, url, and metadata
    """
    sources = []
    
    # Skip if there's an error
    if formatted_response.get("isError"):
        return sources
    
    results = formatted_response.get("results", [])
    
    for result in results:
        source_item = {
            "title": result.get("file_name") or result.get("title", ""),
            "url": result.get("web_view_link", ""),
            "source_type": result.get("source_type") or result.get("source_name", "UNKNOWN"),
            "file_id": result.get("file_id", ""),
        }
        
        # Add core search metadata
        if "score" in result:
            source_item["score"] = result.get("score")
        if "relevance_score" in result:
            source_item["relevance_score"] = result.get("relevance_score")
        if "search_type" in result:
            source_item["search_type"] = result.get("search_type")
        if "vector_id" in result:
            source_item["vector_id"] = result.get("vector_id")
        
        # Add file metadata
        if "mime_type" in result:
            source_item["mime_type"] = result.get("mime_type")
        if "created_time" in result:
            source_item["created_time"] = result.get("created_time")
        if "modified_time" in result:
            source_item["modified_time"] = result.get("modified_time")
        
        # Add source identification
        if "source_id" in result:
            source_item["source_id"] = result.get("source_id")
        
        # Add content fields
        if "chunk_text" in result:
            source_item["chunk_text"] = result.get("chunk_text")
        if "preview_text" in result:
            source_item["preview_text"] = result.get("preview_text")
        if "breadcrumb" in result:
            source_item["breadcrumb"] = result.get("breadcrumb")
        if "highlights" in result and result["highlights"]:
            source_item["highlights"] = result.get("highlights")
        
        # Add thumbnail if available
        if "thumbnail_url" in result and result["thumbnail_url"]:
            source_item["thumbnail_url"] = result.get("thumbnail_url")
        
        # Add metadata object if present
        if "metadata" in result and result["metadata"]:
            source_item["metadata"] = result.get("metadata")
        
        # Add comment information if present
        if "comment_count" in result and result["comment_count"] > 0:
            source_item["comment_count"] = result.get("comment_count")
            if "comments" in result:
                source_item["comments"] = result.get("comments")
            if "latest_comment_date" in result:
                source_item["latest_comment_date"] = result.get("latest_comment_date")
        
        sources.append(source_item)
    
    # Filter out sources without both title and url
    sources = [s for s in sources if s.get("title") and s.get("url")]
    
    return sources


def extract_knowledge_base_sources(formatted_response: dict) -> list:
    """
    Extract sources from knowledge base response for state tracking.
    Handles STRUCTURED, UNSTRUCTURED, and HYBRID connector types.
    
    Args:
        formatted_response: The formatted response from format_knowledge_base_response
    
    Returns:
        List of source dictionaries with title and url/file info
    """
    sources = []
    
    # Skip if there's an error
    if formatted_response.get("isError"):
        return sources
    
    connector_type = formatted_response.get("connector_type")
    source_type = formatted_response.get("source")
    
    # Handle UNSTRUCTURED connector type (GOOGLE_DRIVE, CONFLUENCE)
    if connector_type == "UNSTRUCTURED":
        results = formatted_response.get("results", [])
        for result in results:
            source_item = {
                "title": result.get("file_name", ""),
                "url": result.get("web_view_link", ""),
                "source_type": source_type,
            }
            # Add score if available
            if "score" in result:
                source_item["score"] = result.get("score")
            sources.append(source_item)
    
    # Handle STRUCTURED connector type (JIRA, GITHUB, GOOGLE_CALENDAR)
    elif connector_type == "STRUCTURED":
        results = formatted_response.get("results", [])
        for result in results:
            if "nodes" in result:
                # GITHUB and GOOGLE_CALENDAR use nodes structure
                nodes = result.get("nodes", {})
                # Try to extract meaningful info from nodes
                for node_key, node_value in nodes.items():
                    if isinstance(node_value, dict):
                        properties = node_value.get("properties", {})
                        source_item = {
                            "title": properties.get("title") or properties.get("summary") or properties.get("name", ""),
                            "url": properties.get("url") or properties.get("html_url", ""),
                            "source_type": source_type,
                        }
                        sources.append(source_item)
            else:
                # JIRA might have direct properties
                source_item = {
                    "title": result.get("summary") or result.get("title", ""),
                    "url": result.get("url") or result.get("self", ""),
                    "source_type": source_type,
                }
                sources.append(source_item)
    
    # Handle HYBRID connector type (combination of STRUCTURED and UNSTRUCTURED)
    elif connector_type == "HYBRID":
        # Extract STRUCTURED sources
        structured_results = formatted_response.get("structured_results", [])
        for result in structured_results:
            if "nodes" in result:
                nodes = result.get("nodes", {})
                for node_key, node_value in nodes.items():
                    if isinstance(node_value, dict):
                        properties = node_value.get("properties", {})
                        source_item = {
                            "title": properties.get("title") or properties.get("summary") or properties.get("name", ""),
                            "url": properties.get("url") or properties.get("html_url", ""),
                            "source_type": source_type,
                        }
                        sources.append(source_item)
            else:
                source_item = {
                    "title": result.get("summary") or result.get("title", ""),
                    "url": result.get("url") or result.get("self", ""),
                    "source_type": source_type,
                }
                sources.append(source_item)
        
        # Extract UNSTRUCTURED sources
        unstructured_results = formatted_response.get("unstructured_results", [])
        for result in unstructured_results:
            source_item = {
                "title": result.get("file_name", ""),
                "url": result.get("web_view_link", ""),
                "source_type": source_type,
            }
            if "score" in result:
                source_item["score"] = result.get("score")
            sources.append(source_item)
    
    # Handle legacy format (backward compatibility) when connector_type is not set
    else:
        results = formatted_response.get("results", [])
        
        # Determine type based on source_type (fallback)
        if source_type in ["GOOGLE_DRIVE", "CONFLUENCE"]:
            for result in results:
                source_item = {
                    "title": result.get("file_name", ""),
                    "url": result.get("web_view_link", ""),
                    "source_type": source_type,
                }
                if "score" in result:
                    source_item["score"] = result.get("score")
                sources.append(source_item)
        
        elif source_type in ["JIRA", "GITHUB", "GOOGLE_CALENDAR"]:
            for result in results:
                if "nodes" in result:
                    nodes = result.get("nodes", {})
                    for node_key, node_value in nodes.items():
                        if isinstance(node_value, dict):
                            properties = node_value.get("properties", {})
                            source_item = {
                                "title": properties.get("title") or properties.get("summary") or properties.get("name", ""),
                                "url": properties.get("url") or properties.get("html_url", ""),
                                "source_type": source_type,
                            }
                            sources.append(source_item)
                else:
                    source_item = {
                        "title": result.get("summary") or result.get("title", ""),
                        "url": result.get("url") or result.get("self", ""),
                        "source_type": source_type,
                    }
                    sources.append(source_item)
    
    # Filter out sources without both title and url
    sources = [s for s in sources if s.get("title") and s.get("url")]
    
    return sources

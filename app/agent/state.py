"""State management for global agent with TODO tracking

This module defines the Todo type for task planning and progress tracking.

Note: Previously this module contained GlobalAgentState with sources and approval fields.
These have been moved to Redis storage (see app/utils/agent_redis_store.py) for better
scalability and separation of concerns. The state is now managed by the deepagents library.
"""

from typing import Literal
from typing_extensions import TypedDict


class Todo(TypedDict):
    """A structured task item for tracking progress through complex workflows.

    Attributes:
        content: Short, specific description of the task
        status: Current state - pending, in_progress, or completed
    """

    content: str
    status: Literal["pending", "in_progress", "completed"]

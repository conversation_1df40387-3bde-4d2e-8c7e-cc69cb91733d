import os
from dotenv import load_dotenv
from pydantic import SecretStr
from app.agent.config.chat_openai_with_reasoning import Chat<PERSON>penAIWithReasoning
from app.services.http_client import get_sync_http_client

load_dotenv()


def get_chat_model(
    provider: str = "",
    model_name: str = "",
    user_id: str = "",
    organisation_id: str = "",
    request_type: str = "",
    agent_id: str = "",
    conversation_id: str = "",
    use_thinking: bool = False,
) -> ChatOpenAIWithReasoning:
    """
    Safely create a ChatOpenAIWithReasoning model instance.
    Falls back to defaults if input or environment values are invalid.

    Args:
        provider (str, optional): Provider name (e.g., 'openai', 'anthropic'). Defaults to env var DEFAULT_PROVIDER.
        model_name (str, optional): Model name (e.g., 'gpt-4o-mini'). Defaults to env var DEFAULT_MODEL_NAME.
        user_id (str, optional): User ID to include in request headers.
        organisation_id (str, optional): Organisation ID to include in request headers.
        request_type (str, optional): Type of request. Use RequestTypeEnum values from app.shared.config.constants.
        agent_id (str, optional): Agent ID to include in request headers.
        conversation_id (str, optional): Conversation ID to include in request headers.
        use_thinking (bool, optional): Enable reasoning/thinking mode. Defaults to False.

    Returns:
        ChatOpenAIWithReasoning: Configured chat model instance with optional reasoning_content support.
    """
    try:
        # Read defaults
        default_provider = os.getenv("DEFAULT_PROVIDER", "openai")
        default_model_name = os.getenv("DEFAULT_MODEL_NAME", "gpt-4o-mini")
        default_base_url = os.getenv(
            "AI_GATEWAY_BASE_URL", "https://openrouter.ai/api/v1"
        )

        # Use provided values or fall back to defaults
        provider = provider or default_provider
        model_name = model_name or default_model_name
        api_key = os.getenv("OPENROUTER_API_KEY")  # always from OPENROUTER
        base_url = os.getenv("AI_GATEWAY_BASE_URL", default_base_url)
        ai_gateway_api_key = os.getenv("AI_GATEWAY_API_KEY")

        if not api_key:
            raise ValueError("Missing OPENROUTER_API_KEY")

        # Build model string
        model_str = f"{provider}/{model_name}"

        # Build headers with optional user_id, organisation_id, request_type, agent_id, and conversation_id
        headers = {"X-Source": "agent-platform", "X-Deduct-RCU": "true"}
        if ai_gateway_api_key:
            headers["X-Server-Auth"] = ai_gateway_api_key
        if user_id:
            headers["X-User-Id"] = user_id
        if organisation_id:
            headers["X-Organisation-Id"] = organisation_id
        if request_type:
            headers["X-Request-Type"] = request_type
        if agent_id:
            headers["X-Agent-Id"] = agent_id
        if conversation_id:
            headers["X-Conversation-Id"] = conversation_id

        # Build extra_body with conditional reasoning support
        extra_body = {}
        if use_thinking:
            extra_body = {
                "reasoning": {"type": "enabled", "effort": "minimal"},
                "include_reasoning": True,
            }

        # Get the global HTTP client for connection pooling and faster initialization
        http_client = get_sync_http_client()

        return ChatOpenAIWithReasoning(
            api_key=SecretStr(api_key),
            base_url=base_url,
            model=model_str,
            model_kwargs={
                "parallel_tool_calls": False,
            },
            extra_body=extra_body,
            default_headers=headers,
            http_client=http_client,  # Reuse global HTTP client
        )

    except Exception as e:
        print(f"[WARN] Falling back to defaults due to error: {e}")

        # Safe fallback
        fallback_provider = os.getenv("DEFAULT_PROVIDER", "openai")
        fallback_model_name = os.getenv("DEFAULT_MODEL_NAME", "gpt-4o-mini")
        fallback_api_key = os.getenv("OPENROUTER_API_KEY")
        fallback_base_url = os.getenv(
            "AI_GATEWAY_BASE_URL", "https://openrouter.ai/api/v1"
        )

        # Get the global HTTP client even for fallback
        try:
            http_client = get_sync_http_client()
        except Exception:
            http_client = None  # If HTTP client fails, let ChatOpenAI create its own

        return ChatOpenAIWithReasoning(
            api_key=SecretStr(fallback_api_key) if fallback_api_key else None,
            base_url=fallback_base_url,
            model=f"{fallback_provider}/{fallback_model_name}",
            model_kwargs={"parallel_tool_calls": False},
            http_client=http_client,
        )

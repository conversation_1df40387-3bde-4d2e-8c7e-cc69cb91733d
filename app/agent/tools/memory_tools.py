"""
Mem0 + <PERSON><PERSON><PERSON> + <PERSON>Graph Integration for Long Term Memory
-----------------------------------------------------
Implements background memory storage with asyncio so that the
tool immediately returns "Memory sent to store" while storage runs in background.

This module provides two main tools:
1. store_memory: Save information to long-term memory
2. retrieve_memories: Retrieve relevant information from long-term memory
"""

import asyncio
import logging
import os
import time
from typing import Optional, Dict, Any

from dotenv import load_dotenv
from mem0 import Memory
from langchain_core.tools import tool
from opentelemetry.trace import SpanKind
from app.utils.metrics import get_metrics_manager
from app.utils.exceptions import get_exception_tracker

from app.agent.config.mem0_config import (
    OpenAIConfigWithHeaders,
    OpenAILLMWithHeaders,
    OpenAIEmbeddingConfigWithHeaders,
    OpenAIEmbeddingWithHeaders,
)
from app.shared.config.constants import RequestTypeEnum, AgentToolEnum
from app.utils.tracing import trace_operation

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()

# Tool descriptions
STORE_MEMORY_TOOL_DESCRIPTION = """
- Saves useful and enduring information about the user or conversation in long-term memory
- Use this tool when:
  • The user explicitly provides personal info, preferences, corrections, or goals
  • New insights or facts emerge that could improve future relevance or personalization
  • The user asks you to remember something for later
- Avoid using this tool if:
  • The information is temporary, situational, or sensitive without consent
  • The detail is already stored or doesn't benefit future interactions
"""

RETRIEVE_MEMORY_TOOL_DESCRIPTION = """
- Retrieves relevant information from long-term memory to provide continuity and context
- Use this tool when:
  • The current query references past conversations, preferences, or facts
  • You need to recall user-specific details (e.g., goals, style, name, prior outputs)
  • The user mentions something like "as before," "you remember," or "last time"
- Avoid using this tool if:
  • The query is self-contained and does not rely on prior knowledge
  • The information needed is already present in the current context
"""

# Global singleton for Memory instance
_memory_instance: Optional[Memory] = None
_memory_llm_headers: Optional[Dict[str, str]] = None


def setup_mem0_with_qdrant(
    user_id: Optional[str] = None,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    organisation_id: Optional[str] = None,
) -> Memory:
    """
    Initialize mem0 with Qdrant instance.
    This should be called once during application startup.

    Args:
        user_id: User identifier for headers
        conversation_id: Conversation identifier for headers
        agent_id: Agent identifier for headers
        organisation_id: Organisation identifier for headers
    """

    qdrant_host = os.getenv("QDRANT_HOST", "localhost")
    qdrant_port = int(os.getenv("QDRANT_PORT", "6333"))
    qdrant_api_key = os.getenv("QDRANT_API_KEY")
    qdrant_collection_name = os.getenv(
        "AGENT_MEMORY_COLLECTION_NAME", "langgraph_memory"
    )
    qdrant_vector_size = int(os.getenv("QDRANT_VECTOR_SIZE", "1536"))

    # Configure Mem0 with Qdrant
    qdrant_config = {
        "collection_name": qdrant_collection_name,
        "embedding_model_dims": qdrant_vector_size,
        "api_key": qdrant_api_key,
        "url": f"https://{qdrant_host}:{qdrant_port}",
    }

    # Get OpenRouter configuration from environment
    openrouter_api_key = os.getenv("OPENROUTER_API_KEY")
    openrouter_base_url = os.getenv("AI_GATEWAY_BASE_URL", "")
    ai_gateway_api_key = os.getenv("AI_GATEWAY_API_KEY")

    # Get OpenAI base URL from environment for embeddings
    openai_base_url = os.getenv("AI_GATEWAY_BASE_URL", "")

    # Get model names and provider from environment with defaults
    llm_model = os.getenv("MEM0_LLM_MODEL", "gpt-5-mini")
    embedding_provider = os.getenv("MEM0_EMBEDDING_PROVIDER", "openai")
    embedding_model_name = os.getenv("MEM0_EMBEDDING_MODEL", "text-embedding-3-small")
    embedding_model = f"{embedding_provider}/{embedding_model_name}"

    # Build headers similar to model.py - this is the ONLY place headers are built
    llm_headers = {
        "X-Source": "agent-platform",
        "X-Request-Type": RequestTypeEnum.MEM0_LLM.value,
        "X-Deduct-RCU": "true",
    }
    embedding_headers = {
        "X-Source": "agent-platform",
        "X-Request-Type": RequestTypeEnum.MEM0_EMBEDDING.value,
        "X-Deduct-RCU": "true",
    }

    if ai_gateway_api_key:
        llm_headers["X-Server-Auth"] = ai_gateway_api_key
        embedding_headers["X-Server-Auth"] = ai_gateway_api_key

    if user_id:
        llm_headers["X-User-Id"] = user_id
        embedding_headers["X-User-Id"] = user_id
    if organisation_id:
        llm_headers["X-Organisation-Id"] = organisation_id
        embedding_headers["X-Organisation-Id"] = organisation_id
    if agent_id:
        llm_headers["X-Agent-Id"] = agent_id
        embedding_headers["X-Agent-Id"] = agent_id
    if conversation_id:
        llm_headers["X-Conversation-Id"] = conversation_id
        embedding_headers["X-Conversation-Id"] = conversation_id

    # Create custom LLM config with headers
    llm_config = OpenAIConfigWithHeaders(
        model=llm_model,
        temperature=0.2,
        max_tokens=1500,
        api_key=openrouter_api_key,
        openai_base_url=openrouter_base_url,
        default_headers=llm_headers,
    )

    # Create custom LLM instance with headers
    llm = OpenAILLMWithHeaders(config=llm_config)

    # Create custom embedding config with headers
    embedding_config = OpenAIEmbeddingConfigWithHeaders(
        model=embedding_model,
        embedding_dims=1536,
        api_key=openrouter_api_key,
        openai_base_url=openrouter_base_url,
        default_headers=embedding_headers,
    )

    # Create custom embedding instance with headers
    embedder = OpenAIEmbeddingWithHeaders(config=embedding_config)

    config = {
        "vector_store": {"provider": "qdrant", "config": qdrant_config},
    }

    # Create Memory instance without embedder config (we'll set it manually)
    memory = Memory.from_config(config)
    # Replace the LLM and embedder with our custom ones that have headers
    memory.llm = llm
    memory.embedding_model = embedder

    return memory


def get_memory_instance(
    user_id: Optional[str] = None,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    organisation_id: Optional[str] = None,
) -> Memory:
    """
    Get or initialize the global memory instance.

    Args:
        user_id: User identifier for headers
        conversation_id: Conversation identifier for headers
        agent_id: Agent identifier for headers
        organisation_id: Organisation identifier for headers
    """
    global _memory_instance, _memory_llm_headers

    # Build context dict for comparison
    current_context = {
        "user_id": user_id,
        "conversation_id": conversation_id,
        "agent_id": agent_id,
        "organisation_id": organisation_id,
    }

    # Reinitialize if context has changed
    if _memory_instance is not None and current_context != _memory_llm_headers:
        _memory_instance = None

    if _memory_instance is None:
        _memory_instance = setup_mem0_with_qdrant(
            user_id=user_id,
            conversation_id=conversation_id,
            agent_id=agent_id,
            organisation_id=organisation_id,
        )
        _memory_llm_headers = current_context

    return _memory_instance


def initialize_memory(
    user_id: Optional[str] = None,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    organisation_id: Optional[str] = None,
):
    """
    Initialize the memory instance during startup.

    Args:
        user_id: User identifier for headers
        conversation_id: Conversation identifier for headers
        agent_id: Agent identifier for headers
        organisation_id: Organisation identifier for headers
    """
    global _memory_instance, _memory_llm_headers
    if _memory_instance is None:
        _memory_instance = setup_mem0_with_qdrant(
            user_id=user_id,
            conversation_id=conversation_id,
            agent_id=agent_id,
            organisation_id=organisation_id,
        )
        _memory_llm_headers = {
            "user_id": user_id,
            "conversation_id": conversation_id,
            "agent_id": agent_id,
            "organisation_id": organisation_id,
        }


async def _store_memory_impl_async(
    text: str,
    user_id: str,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    organisation_id: Optional[str] = None,
    additional_metadata: Optional[Dict[str, Any]] = None,
):
    """
    Async implementation of memory storage.
    Runs in background via asyncio.create_task().

    Args:
        text: Text to store in memory
        user_id: User identifier
        conversation_id: Optional conversation identifier
        agent_id: Optional agent identifier
        organisation_id: Optional organisation identifier
        additional_metadata: Optional additional metadata
    """
    with trace_operation(
        "memory.store",
        kind=SpanKind.CLIENT,
        attributes={
            "user_id": user_id,
            "conversation_id": conversation_id or "none",
            "agent_id": agent_id or "none",
            "text_length": len(text) if text else 0,
        },
    ) as span:
        try:
            memory = get_memory_instance(
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id,
                organisation_id=organisation_id,
            )

            # Ensure user_id is string (mem0 requires string for encoding)
            user_id_str = str(user_id)
            conversation_id_str = str(conversation_id) if conversation_id else None
            agent_id_str = str(agent_id) if agent_id else None

            metadata = {"user_id": user_id_str}
            if conversation_id_str:
                metadata["conversation_id"] = conversation_id_str
            if agent_id_str:
                metadata["agent_id"] = agent_id_str
            if additional_metadata:
                metadata.update(additional_metadata)

            # Create external API tracking span for AI Gateway (LLM + Embedding calls)
            with trace_operation(
                "ai_gateway.mem0_store",
                kind=SpanKind.CLIENT,
                attributes={
                    "external_api": "ai_gateway",
                    "api_endpoint": "/mem0/store",
                    "operation": "memory_store",
                    "peer.service": "ai-gateway",
                    "http.method": "POST",
                },
            ) as api_span:
                loop = asyncio.get_running_loop()
                # Run blocking memory.add() in thread executor to avoid blocking event loop
                result = await loop.run_in_executor(
                    None,
                    lambda: memory.add(messages=text, user_id=user_id_str, metadata=metadata),
                )

                api_span.set_attribute("success", True)
                api_span.set_attribute("memory_ids_count", len(result) if result else 0)
                span.set_attribute("success", True)
                span.set_attribute("memory_ids_count", len(result) if result else 0)
                logger.info(
                    f"✅ Memory stored successfully for user {user_id_str}. IDs: {result}"
                )

        except Exception as e:
            span.set_attribute("error", True)
            span.record_exception(e)
            
            # Track exception with Signoz OTEL
            exception_tracker.track_exception(
                e,
                severity="error",
                attributes={
                    "component": "memory_store_tool",
                    "user_id": user_id,
                    "conversation_id": conversation_id,
                    "agent_id": agent_id,
                    "organisation_id": organisation_id,
                },
            )
            
            logger.exception(f"❌ Error storing memory for user {user_id}: {str(e)}")


def _retrieve_memories_impl(
    query: str,
    user_id: str,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    organisation_id: Optional[str] = None,
    limit: int = 5,
):
    """
    Retrieve stored memories synchronously.

    Args:
        query: Search query
        user_id: User identifier
        conversation_id: Optional conversation identifier
        agent_id: Optional agent identifier
        organisation_id: Optional organisation identifier
        limit: Maximum number of results to return
    """
    with trace_operation(
        "memory.retrieve",
        kind=SpanKind.CLIENT,
        attributes={
            "user_id": user_id,
            "conversation_id": conversation_id or "none",
            "agent_id": agent_id or "none",
            "query": query[:100] if query else "",
            "limit": limit,
        },
    ) as span:
        try:
            memory = get_memory_instance(
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id,
                organisation_id=organisation_id,
            )

            # Ensure user_id is string (mem0 requires string for encoding)
            user_id_str = str(user_id)

            filters = {"user_id": user_id_str}

            # Create external API tracking span for AI Gateway (Embedding + LLM calls)
            with trace_operation(
                "ai_gateway.mem0_retrieve",
                kind=SpanKind.CLIENT,
                attributes={
                    "external_api": "ai_gateway",
                    "api_endpoint": "/mem0/retrieve",
                    "operation": "memory_retrieve",
                    "peer.service": "ai-gateway",
                    "http.method": "POST",
                    "query_length": len(query) if query else 0,
                },
            ) as api_span:
                results = memory.search(
                    query=query, user_id=user_id_str, limit=limit, filters=filters
                )

                api_span.set_attribute("results_count", len(results) if results else 0)
                api_span.set_attribute("success", True)
                span.set_attribute("results_count", len(results) if results else 0)
                span.set_attribute("success", True)
                return results

        except Exception as e:
            span.set_attribute("error", True)
            span.record_exception(e)
            
            # Track exception with Signoz OTEL
            exception_tracker.track_exception(
                e,
                severity="error",
                attributes={
                    "component": "memory_retrieve_tool",
                    "user_id": user_id,
                    "query_length": len(query) if query else 0,
                    "agent_id": agent_id,
                    "organisation_id": organisation_id,
                },
            )
            
            logger.exception("Error retrieving memories")
            return [{"error": f"Error retrieving memories: {str(e)}"}]


def get_store_memory_tool(
    user_id: str,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    organisation_id: Optional[str] = None,
):
    """
    Create a LangGraph tool that stores memory via Redis Streams.

    Args:
        user_id: User identifier
        conversation_id: Optional conversation identifier
        agent_id: Optional agent identifier
        organisation_id: Optional organisation identifier
    """

    @tool(
        AgentToolEnum.ADD_MEMORY.value,
        description=STORE_MEMORY_TOOL_DESCRIPTION,
        parse_docstring=True,
    )
    async def store_memory(
        text: str,
        additional_metadata: Optional[Dict[str, Any]] = None,
    ) -> dict:
        """Store information in memory for the current user and conversation.

        Args:
            text: The text content to store in memory.
            additional_metadata: Optional additional metadata to store with the memory.

        Returns:
            dict: A dictionary containing a success message or error information.
        """
        start_time = time.time()
        metrics_manager = get_metrics_manager()
        
        try:
            # Import here to avoid circular dependencies
            from app.services.redis_streams import RedisStreamsManager

            # Create streams_manager instance
            streams_manager = RedisStreamsManager()

            # Send to Redis Stream for async processing
            await streams_manager.producer.send_memory_request(
                text=text,
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id,
                organisation_id=organisation_id,
                additional_metadata=additional_metadata,
            )
            
            # Record successful memory operation
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_memory_operation(
                operation="store",
                duration_ms=duration_ms,
                attributes={"text_length": len(text)},
            )
            
            return {"message": "Memory sent to store ✅", "isError": False}

        except Exception as e:
            # Track exception with Signoz OTEL
            exception_tracker.track_exception(
                e,
                severity="error",
                attributes={
                    "component": "async_memory_store_tool",
                    "user_id": user_id,
                    "conversation_id": conversation_id,
                    "agent_id": agent_id,
                    "organisation_id": organisation_id,
                    "text_length": len(text) if text else 0,
                },
            )
            
            logger.exception("Failed to send memory to storage")
            metrics_manager.record_tool_error(
                tool_name="memory_store",
                error_type=type(e).__name__,
            )
            return {
                "error": f"Error scheduling memory storage: {str(e)}",
                "isError": True,
            }

    return store_memory


def get_retrieve_memories_tool(
    user_id: str,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    organisation_id: Optional[str] = None,
):
    """
    Create a LangGraph tool that retrieves stored memories.

    Args:
        user_id: User identifier
        conversation_id: Optional conversation identifier
        agent_id: Optional agent identifier
        organisation_id: Optional organisation identifier
    """

    @tool(
        AgentToolEnum.GET_MEMORY.value,
        description=RETRIEVE_MEMORY_TOOL_DESCRIPTION,
        parse_docstring=True,
    )
    def retrieve_memories(query: str, limit: int = 5):
        """Retrieve relevant memories for the current user and conversation.

        Args:
            query: The search query to find relevant memories.
            limit: Maximum number of memories to retrieve (default: 5).

        Returns:
            dict: A dictionary containing the retrieved memories or error information.
        """
        start_time = time.time()
        metrics_manager = get_metrics_manager()
        
        try:
            result = _retrieve_memories_impl(
                query=query,
                user_id=user_id,
                conversation_id=conversation_id,
                agent_id=agent_id,
                organisation_id=organisation_id,
                limit=limit,
            )
            
            # Record successful memory retrieval
            duration_ms = (time.time() - start_time) * 1000
            results_count = len(result) if isinstance(result, list) else 0
            metrics_manager.record_memory_operation(
                operation="retrieve",
                duration_ms=duration_ms,
                attributes={"query_length": len(query), "results_count": results_count},
            )
            
            if isinstance(result, list) and len(result) > 0 and "error" in result[0]:
                return {"error": result[0]["error"], "isError": True}
            return {"memories": result, "isError": False}
        except Exception as e:
            logger.error(f"Error in retrieve_memories: {str(e)}")
            metrics_manager.record_tool_error(
                tool_name="memory_retrieve",
                error_type=type(e).__name__,
            )
            return {"error": str(e), "isError": True}

    return retrieve_memories

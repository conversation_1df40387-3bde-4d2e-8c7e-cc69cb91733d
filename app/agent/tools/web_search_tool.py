"""
Web Search tool, powered by Exa's API. 

This tool returns structured search results with title, URL and content.
It allows searching the web and using the results to inform responses with up-to-date information
for current events and recent data beyond the LLM's knowledge cutoff.
"""

import json
import os
import logging
import time
from typing import Any, Dict, Annotated, Optional

import aiohttp
from dotenv import load_dotenv
from langchain_core.tools import tool, InjectedToolCallId
from langchain_core.messages import ToolMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.types import Command
from opentelemetry.trace import SpanKind
from app.shared.config.constants import RequestTypeEnum, AgentToolEnum
from app.utils.tracing import trace_operation
from app.utils.metrics import get_metrics_manager
from app.utils.exceptions import get_exception_tracker
from app.utils.agent_redis_store import store_sources

load_dotenv()

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()

# Tool description
WEB_SEARCH_TOOL_DESCRIPTION = """
- Allows to search the web and use the results to inform responses
- Provides up-to-date information for current events and recent data
- Use this tool for accessing information beyond LLM's knowledge cutoff
"""

EXA_SEARCH_BASE_URL = os.getenv("AI_GATEWAY_BASE_URL", "")
EXA_API_KEY = os.getenv("EXA_API_KEY")
AI_GATEWAY_API_KEY = os.getenv("AI_GATEWAY_API_KEY")


def _format_search_response(data: Dict[str, Any], query: str) -> str:
    """
    Format the search response to structured output with only essential fields.

    Args:
        data: Raw response data from Exa API
        query: Original search query

    Returns:
        JSON string with structured format containing query and results
    """
    if "results" in data:
        structured_response = {"query": query, "results": []}

        for result in data["results"]:
            formatted_result = {
                "id": result.get("id", ""),
                "title": result.get("title", ""),
                "url": result.get("url", ""),
                "publishedDate": result.get("publishedDate", ""),
            }

            if "text" in result:
                formatted_result["text"] = result["text"]

            if "summary" in result:
                formatted_result["summary"] = result["summary"]

            structured_response["results"].append(formatted_result)

        return json.dumps(structured_response, indent=2)
    else:
        return json.dumps(data, indent=2)


def get_web_search_tool(
    user_id: str,
    conversation_id: Optional[str] = None,
    agent_id: Optional[str] = None,
    organisation_id: Optional[str] = None,
):
    """
    Create a web search tool with user context headers.

    Args:
        user_id: User identifier
        conversation_id: Optional conversation identifier
        agent_id: Optional agent identifier
        organisation_id: Optional organisation identifier
    """

    @tool(
        AgentToolEnum.WEB_SEARCH.value,
        description=WEB_SEARCH_TOOL_DESCRIPTION,
        parse_docstring=True,
    )
    async def web_search_tool(
        query: str,
        tool_call_id: Annotated[str, InjectedToolCallId],
        config: Annotated[RunnableConfig, "Configuration object"] = None,
    ) -> Command:
        """Search the web using Exa's API.

        Args:
            query: The search query string.
            tool_call_id: Injected tool call identifier for message tracking.
            config: Configuration object containing conversation_id and other context.

        Returns:
            Command: Command with search results in message.
        """
        start_time = time.time()
        metrics_manager = get_metrics_manager()
        
        # Extract conversation_id from config
        conversation_id = None
        if config and "configurable" in config:
            conversation_id = config["configurable"].get("conversation_id")

        with trace_operation(
            "web_search.search",
            kind=SpanKind.CLIENT,
            attributes={
                "query_length": len(query),
                "user_id": user_id or "unknown",
                "organisation_id": organisation_id or "unknown",
            },
        ) as span:
            try:
                if not query or not query.strip():
                    return Command(
                        update={
                            "messages": [
                                ToolMessage(
                                    json.dumps(
                                        {
                                            "error": "Query parameter is required and cannot be empty",
                                            "isError": True,
                                        }
                                    ),
                                    tool_call_id=tool_call_id,
                                )
                            ]
                        }
                    )

                api_key = EXA_API_KEY

                payload = {
                    "query": query,
                    "numResults": 5,
                    "contents": {"summary": {"query": query}},
                }

                # Build headers with user context similar to model.py and memory_tools.py
                headers = {
                    "x-api-key": api_key,
                    "Content-Type": "application/json",
                    "X-Source": "agent-platform",
                    "X-Request-Type": RequestTypeEnum.WEB_SEARCH.value,
                    "X-Deduct-RCU": "true",
                }
                
                if AI_GATEWAY_API_KEY:
                    headers["X-Server-Auth"] = AI_GATEWAY_API_KEY

                if user_id:
                    headers["X-User-Id"] = user_id
                if organisation_id:
                    headers["X-Organisation-Id"] = organisation_id
                if agent_id:
                    headers["X-Agent-Id"] = agent_id
                if conversation_id:
                    headers["X-Conversation-Id"] = conversation_id

                url = f"{EXA_SEARCH_BASE_URL}/exa/search"

                logger.info(f"Making Exa search request for query: {query}")

                # Create external API tracking span
                with trace_operation(
                    "exa.search",
                    kind=SpanKind.CLIENT,
                    attributes={
                        "external_api": "exa",
                        "api_endpoint": "/exa/search",
                        "operation": "search",
                        "peer.service": "exa-api",
                        "http.method": "POST",
                        "http.url": url,
                    },
                ) as api_span:
                    async with aiohttp.ClientSession() as session:
                        async with session.post(url, json=payload, headers=headers) as response:
                            # Add response status to span
                            api_span.set_attribute("http.status_code", response.status)

                            if response.status != 200:
                                api_span.set_attribute("error", True)
                                error_text = await response.text()

                                # Record error metrics
                                duration_ms = (time.time() - start_time) * 1000
                                metrics_manager.record_external_api_call(
                                    api_name="exa",
                                    endpoint="/exa/search",
                                    attributes={
                                        "status_code": str(response.status),
                                        "error": "true",
                                    },
                                )
                                metrics_manager.record_external_api_duration(
                                    duration_ms=duration_ms,
                                    api_name="exa",
                                    endpoint="/exa/search",
                                    attributes={"status_code": str(response.status)},
                                )

                                return Command(
                                    update={
                                        "messages": [
                                            ToolMessage(
                                                json.dumps(
                                                    {
                                                        "error": f"API request failed with status {response.status}: {error_text}",
                                                        "isError": True,
                                                    }
                                                ),
                                                tool_call_id=tool_call_id,
                                            )
                                        ]
                                    }
                                )

                            try:
                                data = await response.json()
                            except Exception as e:
                                api_span.set_attribute("error", True)
                                api_span.set_attribute("error.message", str(e))

                                # Track exception with Signoz OTEL
                                exception_tracker.track_exception(
                                    e,
                                    severity="error",
                                    attributes={
                                        "component": "web_search_response_parsing",
                                        "query_length": len(query) if query else 0,
                                    },
                                )

                                return Command(
                                    update={
                                        "messages": [
                                            ToolMessage(
                                                json.dumps(
                                                    {
                                                        "error": f"Failed to parse API response: {str(e)}",
                                                        "isError": True,
                                                    }
                                                ),
                                                tool_call_id=tool_call_id,
                                            )
                                        ]
                                    }
                                )

                            formatted_response = _format_search_response(data, query)
                            response_dict = json.loads(formatted_response)
                            response_dict["isError"] = False

                            web_sources = [
                                {"title": result.get("title", ""), "url": result.get("url", "")}
                                for result in response_dict.get("results", [])
                            ]

                            # Add result count to API span
                            api_span.set_attribute("results_count", len(web_sources))
                            span.set_attribute("results_count", len(web_sources))

                            # Record successful external API call metrics
                            duration_ms = (time.time() - start_time) * 1000
                            metrics_manager.record_external_api_call(
                                api_name="exa",
                                endpoint="/exa/search",
                                attributes={
                                    "status_code": str(response.status),
                                    "operation": "search",
                                },
                            )
                            metrics_manager.record_external_api_duration(
                                duration_ms=duration_ms,
                                api_name="exa",
                                endpoint="/exa/search",
                                attributes={"status_code": str(response.status)},
                            )

                            # Record successful web search tool metrics
                            metrics_manager.record_tool_execution(
                                tool_name="web_search",
                                duration_ms=duration_ms,
                                attributes={"results_count": len(web_sources)},
                            )

                            # Store sources in Redis instead of state
                            if conversation_id:
                                await store_sources(conversation_id, "web", web_sources)

                            return Command(
                                update={
                                    "messages": [
                                        ToolMessage(
                                            json.dumps(response_dict), tool_call_id=tool_call_id
                                        )
                                    ],
                                }
                            )

            except aiohttp.ClientError as e:
                logger.error(f"Connection error in web_search: {str(e)}")
                span.set_attribute("error_type", "client_error")

                # Record external API error
                metrics_manager.record_external_api_error(
                    api_name="exa",
                    endpoint="/exa/search",
                    error_type="ClientError",
                )
                metrics_manager.record_tool_error(
                    tool_name="web_search",
                    error_type="ClientError",
                )
                return Command(
                    update={
                        "messages": [
                            ToolMessage(
                                json.dumps(
                                    {
                                        "error": f"Connection error: {str(e)}",
                                        "isError": True,
                                    }
                                ),
                                tool_call_id=tool_call_id,
                            )
                        ]
                    }
                )
            except ValueError as e:
                logger.error(f"Value error in web_search: {str(e)}")
                span.set_attribute("error_type", "value_error")

                # Record external API error
                metrics_manager.record_external_api_error(
                    api_name="exa",
                    endpoint="/exa/search",
                    error_type="ValueError",
                )
                metrics_manager.record_tool_error(
                    tool_name="web_search",
                    error_type="ValueError",
                )
                return Command(
                    update={
                        "messages": [
                            ToolMessage(
                                json.dumps({"error": str(e), "isError": True}),
                                tool_call_id=tool_call_id,
                            )
                        ]
                    }
                )
            except Exception as e:
                # Track exception with Signoz OTEL
                exception_tracker.track_exception(
                    e,
                    severity="error",
                    attributes={
                        "component": "web_search_tool",
                        "query_length": len(query) if query else 0,
                    },
                )

                logger.error(f"Unexpected error in web_search: {str(e)}")

                # Record external API error
                metrics_manager.record_external_api_error(
                    api_name="exa",
                    endpoint="/exa/search",
                    error_type=type(e).__name__,
                )
                metrics_manager.record_tool_error(
                    tool_name="web_search",
                    error_type=type(e).__name__,
                )
                span.set_attribute("error_type", "unexpected_error")
                return Command(
                    update={
                        "messages": [
                            ToolMessage(
                                json.dumps(
                                    {
                                        "error": f"Unexpected error: {str(e)}",
                                        "isError": True,
                                    }
                                ),
                                tool_call_id=tool_call_id,
                            )
                        ]
                    }
                )

    return web_search_tool

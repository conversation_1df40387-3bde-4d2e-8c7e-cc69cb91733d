"""
Read File Tool for downloading and parsing files from URLs.

This tool downloads files from URLs and parses their content based on file type,
supporting PDF, CSV, DOCX, and TXT files. It extracts structured content including
text, tables, and metadata.
"""

import os
import tempfile
import aiohttp
import asyncio
import time
from typing import Annotated
from langchain_core.tools import tool
import logging
from opentelemetry.trace import SpanKind

from app.shared.config.constants import AgentToolEnum
from app.utils.tracing import trace_operation
from app.utils.metrics import get_metrics_manager
from app.utils.exceptions import get_exception_tracker

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()

# Tool description
READ_FILE_TOOL_DESCRIPTION = """
- Downloads and parses files from URLs (PDF, CSV, DOCX, TXT)
- Extracts structured content including text, tables, and metadata
- Use this tool when you need to analyze or reference file content from provided URLs
"""


async def download_file_with_retry(
    file_url: str, max_retries: int = 3, timeout: int = 30
) -> bytes:
    """
    Download file from URL with retry logic.

    Args:
        file_url: URL of the file to download
        max_retries: Number of retry attempts (default: 3)
        timeout: Timeout in seconds for each attempt (default: 30)

    Returns:
        bytes: Downloaded file content

    Raises:
        Exception: If all retry attempts fail
    """
    with trace_operation(
        "file.download",
        attributes={
            "file_url": file_url[:200] if len(file_url) > 200 else file_url,
            "max_retries": max_retries,
            "timeout": timeout,
        },
    ) as span:
        for attempt in range(max_retries):
            try:
                # Create external API tracking span for file download
                with trace_operation(
                    "external.file_download",
                    kind=SpanKind.CLIENT,
                    attributes={
                        "external_api": "file_download",
                        "api_endpoint": file_url[:200] if len(file_url) > 200 else file_url,
                        "operation": "download",
                        "peer.service": "file-server",
                        "http.method": "GET",
                        "http.url": file_url[:200] if len(file_url) > 200 else file_url,
                        "attempt": attempt + 1,
                    },
                ) as api_span:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            file_url, timeout=aiohttp.ClientTimeout(total=timeout)
                        ) as response:
                            api_span.set_attribute("http.status_code", response.status)

                            if response.status == 200:
                                content = await response.read()
                                api_span.set_attribute("success", True)
                                api_span.set_attribute("content_size", len(content))
                                span.set_attribute("success", True)
                                span.set_attribute("attempt", attempt + 1)
                                span.set_attribute("content_size", len(content))
                                logger.info(
                                    f"Successfully downloaded file from {file_url} (attempt {attempt + 1})"
                                )

                                # Record metrics
                                metrics_manager = get_metrics_manager()
                                metrics_manager.record_external_api_call(
                                    api_name="file_download",
                                    endpoint="/download",
                                    attributes={
                                        "status_code": str(response.status),
                                        "operation": "download",
                                    },
                                )

                                return content
                            else:
                                api_span.set_attribute("error", True)
                                raise Exception(
                                    f"HTTP {response.status}: Failed to download file"
                                )
            except asyncio.TimeoutError:
                logger.warning(
                    f"Timeout downloading file from {file_url} (attempt {attempt + 1}/{max_retries})"
                )
                if attempt == max_retries - 1:
                    span.set_attribute("error", True)
                    span.set_attribute("error_type", "timeout")
                    raise Exception(
                        f"Failed to download file after {max_retries} attempts: Timeout"
                    )
            except Exception as e:
                logger.warning(
                    f"Error downloading file from {file_url} (attempt {attempt + 1}/{max_retries}): {e}"
                )
                if attempt == max_retries - 1:
                    # Track exception with Signoz OTEL
                    exception_tracker.track_exception(
                        e,
                        severity="error",
                        attributes={
                            "component": "file_download",
                            "file_url": file_url[:100],
                            "attempt": attempt + 1,
                            "max_retries": max_retries,
                        },
                    )
                    
                    span.set_attribute("error", True)
                    span.record_exception(e)
                    raise Exception(
                        f"Failed to download file after {max_retries} attempts: {str(e)}"
                    )

            if attempt < max_retries - 1:
                await asyncio.sleep(1)


def parse_pdf(file_path: str) -> dict:
    """Parse PDF file and extract text content."""
    try:
        from pypdf import PdfReader

        reader = PdfReader(file_path)
        total_pages = len(reader.pages)

        pages_content = []
        for page_num, page in enumerate(reader.pages, start=1):
            text = page.extract_text()
            if text.strip():
                pages_content.append({"page": page_num, "content": text.strip()})

        full_text = "\n\n".join(
            [f"[Page {p['page']}]\n{p['content']}" for p in pages_content]
        )

        return {
            "type": "pdf",
            "total_pages": total_pages,
            "pages_with_content": len(pages_content),
            "full_text": full_text,
            "pages": pages_content,
        }
    except Exception as e:
        # Track exception with Signoz OTEL
        exception_tracker.track_exception(
            e,
            severity="error",
            attributes={
                "component": "parse_pdf_tool",
                "file_path": file_path,
            },
        )
        raise Exception(f"Failed to parse PDF: {str(e)}")


def parse_csv(file_path: str) -> dict:
    """Parse CSV file and extract data."""
    try:
        import csv

        with open(file_path, "r", encoding="utf-8") as f:
            reader = csv.DictReader(f)
            rows = list(reader)

        headers = list(rows[0].keys()) if rows else []

        text_repr = ""
        if headers:
            text_repr = ", ".join(headers) + "\n"

        for row in rows:
            text_repr += ", ".join([str(row.get(h, "")) for h in headers]) + "\n"

        return {
            "type": "csv",
            "total_rows": len(rows),
            "headers": headers,
            "data": rows,
            "text_representation": text_repr.strip(),
        }
    except Exception as e:
        # Track exception with Signoz OTEL
        exception_tracker.track_exception(
            e,
            severity="error",
            attributes={
                "component": "parse_csv_tool",
                "file_path": file_path,
            },
        )
        raise Exception(f"Failed to parse CSV: {str(e)}")


def parse_docx(file_path: str) -> dict:
    """Parse DOCX file and extract text content."""
    try:
        from docx import Document

        doc = Document(file_path)

        paragraphs = []
        for i, para in enumerate(doc.paragraphs, start=1):
            if para.text.strip():
                paragraphs.append({"paragraph": i, "content": para.text.strip()})

        full_text = "\n\n".join([p["content"] for p in paragraphs])

        tables_data = []
        for table_num, table in enumerate(doc.tables, start=1):
            table_content = []
            for row in table.rows:
                row_data = [cell.text.strip() for cell in row.cells]
                table_content.append(row_data)
            if table_content:
                tables_data.append({"table": table_num, "content": table_content})

        return {
            "type": "docx",
            "total_paragraphs": len(paragraphs),
            "total_tables": len(tables_data),
            "full_text": full_text,
            "paragraphs": paragraphs,
            "tables": tables_data,
        }
    except Exception as e:
        # Track exception with Signoz OTEL
        exception_tracker.track_exception(
            e,
            severity="error",
            attributes={
                "component": "parse_docx_tool",
                "file_path": file_path,
            },
        )
        raise Exception(f"Failed to parse DOCX: {str(e)}")


def parse_txt(file_path: str) -> dict:
    """Parse TXT file and extract text content."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        lines = content.split("\n")
        non_empty_lines = [line for line in lines if line.strip()]

        return {
            "type": "txt",
            "total_lines": len(lines),
            "non_empty_lines": len(non_empty_lines),
            "full_text": content,
        }
    except Exception as e:
        # Track exception with Signoz OTEL
        exception_tracker.track_exception(
            e,
            severity="error",
            attributes={
                "component": "parse_txt_tool",
                "file_path": file_path,
            },
        )
        raise Exception(f"Failed to parse TXT: {str(e)}")


def get_file_extension(file_name: str) -> str:
    """Extract file extension from file name."""
    return os.path.splitext(file_name)[1].lower()


@tool(
    AgentToolEnum.READ_FILE.value,
    description=READ_FILE_TOOL_DESCRIPTION,
    parse_docstring=True,
)
async def read_file(
    file_name: Annotated[str, "Name of the file to read"],
    file_url: Annotated[str, "URL of the file to download and read"],
) -> str:
    """
    Download and parse a file from a URL. Supports PDF, CSV, DOCX, and TXT files.

    This tool downloads the file, parses its content based on the file extension,
    and returns a structured representation of the file content.

    Args:
        file_name: Name of the file (used to determine file type from extension)
        file_url: URL to download the file from

    Returns:
        Structured text representation of the file content
    """
    with trace_operation(
        "tool.read_file",
        attributes={
            "file_name": file_name,
            "file_url": file_url[:200] if len(file_url) > 200 else file_url,
        },
    ) as span:
        start_time = time.time()
        metrics_manager = get_metrics_manager()
        
        try:
            extension = get_file_extension(file_name)
            span.set_attribute("file_extension", extension)

            if extension not in [".pdf", ".csv", ".docx", ".txt"]:
                span.set_attribute("error", True)
                span.set_attribute("error_type", "unsupported_file_type")
                metrics_manager.record_tool_error(
                    tool_name="read_file",
                    error_type="UnsupportedFileType",
                    attributes={"file_extension": extension},
                )
                return f"❌ Error: Unsupported file type '{extension}'. Supported types: PDF, CSV, DOCX, TXT"

            logger.info(f"Downloading file: {file_name} from {file_url}")
            file_content = await download_file_with_retry(file_url)

            with tempfile.NamedTemporaryFile(delete=False, suffix=extension) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                if extension == ".pdf":
                    parsed_data = parse_pdf(temp_file_path)
                elif extension == ".csv":
                    parsed_data = parse_csv(temp_file_path)
                elif extension == ".docx":
                    parsed_data = parse_docx(temp_file_path)
                elif extension == ".txt":
                    parsed_data = parse_txt(temp_file_path)
                else:
                    span.set_attribute("error", True)
                    span.set_attribute("error_type", "unsupported_file_type")
                    return f"❌ Error: Unsupported file type '{extension}'"

                span.set_attribute("file_type", parsed_data["type"])

                output = f"📄 File: {file_name}\n"
                output += f"📋 Type: {parsed_data['type'].upper()}\n\n"

                if parsed_data["type"] == "pdf":
                    span.set_attribute("total_pages", parsed_data['total_pages'])
                    span.set_attribute("pages_with_content", parsed_data['pages_with_content'])
                    output += f"📊 Total Pages: {parsed_data['total_pages']}\n"
                    output += (
                        f"📝 Pages with Content: {parsed_data['pages_with_content']}\n\n"
                    )
                    output += "📖 Content:\n"
                    output += parsed_data["full_text"]

                elif parsed_data["type"] == "csv":
                    span.set_attribute("total_rows", parsed_data['total_rows'])
                    span.set_attribute("column_count", len(parsed_data['headers']))
                    output += f"📊 Total Rows: {parsed_data['total_rows']}\n"
                    output += f"📝 Columns: {', '.join(parsed_data['headers'])}\n\n"
                    output += "📖 Content:\n"
                    output += parsed_data["text_representation"]

                elif parsed_data["type"] == "docx":
                    span.set_attribute("total_paragraphs", parsed_data['total_paragraphs'])
                    span.set_attribute("total_tables", parsed_data['total_tables'])
                    output += f"📊 Total Paragraphs: {parsed_data['total_paragraphs']}\n"
                    output += f"📝 Total Tables: {parsed_data['total_tables']}\n\n"
                    output += "📖 Content:\n"
                    output += parsed_data["full_text"]
                    if parsed_data["tables"]:
                        output += "\n\n📊 Tables:\n"
                        for table in parsed_data["tables"]:
                            output += f"\nTable {table['table']}:\n"
                            for row in table["content"]:
                                output += " | ".join(row) + "\n"

                elif parsed_data["type"] == "txt":
                    span.set_attribute("total_lines", parsed_data['total_lines'])
                    span.set_attribute("non_empty_lines", parsed_data['non_empty_lines'])
                    output += f"📊 Total Lines: {parsed_data['total_lines']}\n"
                    output += f"📝 Non-empty Lines: {parsed_data['non_empty_lines']}\n\n"
                    output += "📖 Content:\n"
                    output += parsed_data["full_text"]

                span.set_attribute("success", True)
                span.set_attribute("output_length", len(output))
                
                # Record successful file read
                duration_ms = (time.time() - start_time) * 1000
                metrics_manager.record_tool_execution(
                    tool_name="read_file",
                    duration_ms=duration_ms,
                    attributes={
                        "file_name": file_name,
                        "file_type": parsed_data["type"],
                        "output_length": len(output),
                    },
                )
                
                return output

            finally:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

        except Exception as e:
            # Track exception with Signoz OTEL
            exception_tracker.track_exception(
                e,
                severity="error",
                attributes={
                    "component": "read_file_tool",
                    "file_name": file_name,
                },
            )
            
            span.set_attribute("error", True)
            span.record_exception(e)
            logger.error(f"Error in read_file tool: {str(e)}")
            metrics_manager.record_tool_error(
                tool_name="read_file",
                error_type=type(e).__name__,
                attributes={"file_name": file_name},
            )
            return f"❌ Error reading file '{file_name}': {str(e)}"

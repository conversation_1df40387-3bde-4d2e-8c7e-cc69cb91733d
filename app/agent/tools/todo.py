"""TODO management tools for task planning and progress tracking.

This module provides tools for creating and managing structured task lists
that enable agents to plan complex workflows and track progress through
multi-step operations.
"""

import json
import logging
from typing import Annotated

from langchain_core.messages import ToolMessage
from langchain_core.tools import InjectedToolCallId, tool
from langgraph.prebuilt import InjectedState
from langgraph.types import Command

from app.agent.state import Todo
from app.agent.system_prompts.usage_instructions.read_todos_tool_description import (
    READ_TODOS_TOOL_DESCRIPTION,
)
from app.agent.system_prompts.usage_instructions.write_todos_tool_description import (
    WRITE_TODOS_TOOL_DESCRIPTION,
)
from app.shared.config.constants import AgentToolEnum
from app.utils.exceptions import get_exception_tracker

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()


@tool(
    AgentToolEnum.WRITE_TODOS.value,
    description=WRITE_TODOS_TOOL_DESCRIPTION,
    parse_docstring=True,
)
def write_todos(
    todos: list[Todo], tool_call_id: Annotated[str, InjectedToolCallId]
) -> Command:
    """Create or update the agent's TODO list for task planning and tracking.

    Args:
        todos: List of Todo items with content and status
        tool_call_id: Tool call identifier for message response

    Returns:
        Command to update agent state with new TODO list
    """
    try:
        # Todos are TypedDict objects, already dict-like
        todos_dict = [dict(todo) for todo in todos]
        
        return Command(
            update={
                "todos": todos,
                "messages": [
                    ToolMessage(
                        json.dumps({"todos": todos_dict, "isError": False}),
                        tool_call_id=tool_call_id,
                    )
                ],
            }
        )
    except Exception as e:
        # Track exception with Signoz OTEL
        exception_tracker.track_exception(
            e,
            severity="error",
            attributes={
                "component": "write_todos_tool",
            },
        )
        
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        json.dumps({"error": str(e), "isError": True}),
                        tool_call_id=tool_call_id,
                    )
                ],
            }
        )


@tool(
    AgentToolEnum.READ_TODOS.value,
    description=READ_TODOS_TOOL_DESCRIPTION,
    parse_docstring=True,
)
def read_todos(
    state: Annotated[dict, InjectedState],
    tool_call_id: Annotated[str, InjectedToolCallId],
) -> str:
    """Read the current TODO list from the agent state.

    This tool allows the agent to retrieve and review the current TODO list
    to stay focused on remaining tasks and track progress through complex workflows.

    Args:
        state: Injected agent state dictionary containing the current TODO list
        tool_call_id: Injected tool call identifier for message tracking

    Returns:
        Formatted string representation of the current TODO list
    """
    try:
        todos = state.get("todos", [])
        
        # Todos are TypedDict objects, already dict-like
        todos_dict = [dict(todo) for todo in todos] if todos else []
        
        return json.dumps({"todos": todos_dict, "isError": False})
    except Exception as e:
        # Track exception with Signoz OTEL
        exception_tracker.track_exception(
            e,
            severity="error",
            attributes={
                "component": "read_todos_tool",
            },
        )
        
        return json.dumps({"error": str(e), "isError": True})

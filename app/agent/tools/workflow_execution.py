"""
Workflow Execution Tool for RUH orchestration.

This tool executes workflows via the RUH orchestration API and streams real-time events
back to the client, allowing the agent to orchestrate complex multi-step automations.
"""

import asyncio
import time
import logging
import json
from typing import Annotated
from langchain_core.tools import tool, InjectedToolCallId
from langchain_core.messages import ToolMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.types import Command
from langgraph.config import get_stream_writer
from app.utils.metrics import get_metrics_manager
from app.utils.exceptions import get_exception_tracker
from app.services.http_client import get_http_client
from app.shared.config.base import get_settings
from app.shared.config.constants import AgentToolEnum
from app.utils.workflow_parser import (
    parse_workflow_event,
    format_workflow_output,
    get_workflow_summary,
)
import httpx

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()

# Stream timeout in seconds (1 hour)
STREAM_TIMEOUT = 3600

# Tool description
WORKFLOW_EXECUTION_TOOL_DESCRIPTION = """
- Execute workflow orchestrations and automations through the RUH orchestration API
- Stream real-time workflow events and progress updates back to the user
- Use this tool when:
  • You need to execute a pre-defined workflow or automation
  • You have the workflow ID and all required input parameters
  • The task requires complex multi-step orchestration
- Avoid using this tool if:
  • The workflow ID is not known
  • Required workflow parameters are missing or unclear
"""


async def _execute_workflow(
    workflow_id: str,
    user_id: str,
    organisation_id: str,
    payload: dict,
) -> str:
    """
    Execute the workflow orchestration by calling the external API.

    Args:
        workflow_id: Workflow ID to execute
        user_id: User ID from runnable config
        organisation_id: Organisation ID from runnable config
        payload: Dictionary containing all workflow input parameters

    Returns:
        correlation_id: The correlation ID for streaming the results
    """
    settings = get_settings()
    http_client = get_http_client()

    url = f"{settings.external_api_gateway.api_url}/workflow-execute/server/execute-ruh-orchestration"
    headers = {
        "accept": "application/json",
        "X-Server-Auth-Key": settings.external_api_gateway.token,
        "Content-Type": "application/json",
    }

    request_payload = {
        "workflow_id": workflow_id,
        "user_id": user_id,
        "organization_id": organisation_id,
        "payload": payload,
        "approval": True,  # Always true as per requirements
    }

    logger.info(
        f"🚀 Executing workflow {workflow_id} for user {user_id}"
    )
    logger.info(f"📦 Request payload: {json.dumps(request_payload, indent=2)}")

    response = await http_client.post(url, json=request_payload, headers=headers, timeout=30.0)
    response.raise_for_status()

    result = response.json()
    correlation_id = result.get("correlationId")

    if not correlation_id:
        raise ValueError("No correlationId received from workflow execution API")

    logger.info(f"Workflow execution initiated with correlation ID: {correlation_id}")
    return correlation_id


async def _stream_workflow_events(
    correlation_id: str, writer, tool_call_id: str, workflow_name: str, workflow_id: str
) -> tuple[dict, list, str]:
    """
    Stream workflow events from the external API using Server-Sent Events (SSE).

    Returns:
        tuple: (Final workflow result or error information, List of parsed events, Workflow status)
    """
    settings = get_settings()
    http_client = get_http_client()

    url = f"{settings.external_api_gateway.api_url}/workflow-execute/stream/server/{correlation_id}"
    headers = {
        "accept": "application/json",
        "X-Server-Auth-Key": settings.external_api_gateway.token,
    }

    logger.info(f"Starting stream for correlation ID: {correlation_id}")

    start_time = time.time()
    workflow_completed = False
    final_result = None
    event_count = 0
    collected_events = []  # Collect all events for final output
    workflow_status = "unknown"  # Track the workflow status

    try:
        async with http_client.stream(
            "GET", url, headers=headers, timeout=STREAM_TIMEOUT
        ) as response:
            response.raise_for_status()

            async for line in response.aiter_lines():
                # Check timeout
                elapsed_time = time.time() - start_time
                if elapsed_time > STREAM_TIMEOUT:
                    logger.warning(f"Stream timeout after {STREAM_TIMEOUT} seconds")
                    raise TimeoutError(
                        f"Stream exceeded timeout of {STREAM_TIMEOUT} seconds"
                    )

                line = line.strip()

                # Skip empty lines
                if not line:
                    continue

                # Parse SSE format
                if line.startswith("event:"):
                    event_type = line[6:].strip()
                elif line.startswith("data:"):
                    data_str = line[5:].strip()

                    # Skip keep-alive events
                    if data_str == "keep-alive":
                        continue

                    try:
                        data = json.loads(data_str)
                        event_count += 1

                        # Stream the event data as-is
                        writer(
                            {
                                "type": "workflow_event",
                                "workflow_name": workflow_name,
                                "workflow_id": workflow_id,
                                "tool_id": tool_call_id,
                                "event_type": data.get("event_type", "unknown"),
                                "event_data": data,
                                "correlation_id": correlation_id,
                            }
                        )

                        # Parse and collect event for final output
                        parsed_event = parse_workflow_event(data)
                        if parsed_event:
                            collected_events.append(parsed_event)

                        # Check if workflow completed
                        if data.get("event_type") == "workflow.completed":
                            workflow_completed = True
                            final_result = data
                            workflow_status = data.get("workflow_status", "completed")
                            logger.info(
                                f"Workflow completed. Total events: {event_count}"
                            )
                            break

                        # Check if workflow failed
                        elif data.get("event_type") == "workflow.failed":
                            workflow_completed = True
                            final_result = data
                            workflow_status = data.get("workflow_status", "failed")
                            logger.error(f"Workflow failed: {data}")
                            break

                    except json.JSONDecodeError as e:
                        logger.warning(f"Failed to parse SSE data: {line}, error: {e}")
                        continue

    except httpx.TimeoutException:
        logger.error(f"Stream timeout after {STREAM_TIMEOUT} seconds")
        raise TimeoutError(
            f"Workflow stream exceeded timeout of {STREAM_TIMEOUT} seconds"
        )
    except Exception as e:
        logger.error(f"Error streaming workflow events: {e}")
        raise

    if not workflow_completed:
        raise ValueError("Stream ended without workflow completion event")

    return final_result, collected_events, workflow_status


@tool(
    AgentToolEnum.WORKFLOW_EXECUTION.value,
    description=WORKFLOW_EXECUTION_TOOL_DESCRIPTION,
)
async def workflow_execution(
    workflow_id: str,
    workflow_name: str,
    organisation_id: str,
    payload: dict,
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: Annotated[RunnableConfig, "Configuration object"] = None,
) -> Command:
    """
    Execute a workflow via the RUH orchestration API.

    This tool initiates a workflow execution and streams real-time events
    back to the client. The workflow is executed through the external API
    gateway which handles the orchestration.

    Args:
        workflow_id: Unique identifier of the workflow to execute
        workflow_name: Display name of the workflow
        organisation_id: Organisation ID for the workflow execution
        payload: Dictionary containing all required workflow input parameters
        tool_call_id: Injected tool call identifier for message tracking
        config: Runnable configuration for accessing user_id and conversation_id

    Returns:
        Command object with workflow execution result
    """
    start_time = time.time()
    metrics_manager = get_metrics_manager()

    # Extract user_id and conversation_id from config
    user_id = None
    conversation_id = None
    if config and "configurable" in config:
        user_id = config["configurable"].get("user_id")
        conversation_id = config["configurable"].get("conversation_id")

    if not user_id:
        metrics_manager.record_workflow_error(
            error_type="MissingUserId",
            attributes={"workflow_name": workflow_name},
        )
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        json.dumps(
                            {
                                "error": "Missing user_id in config. Please provide a valid user_id.",
                                "isError": True,
                            }
                        ),
                        tool_call_id=tool_call_id,
                    )
                ]
            }
        )

    if not conversation_id:
        metrics_manager.record_workflow_error(
            error_type="MissingConversationId",
            attributes={"workflow_name": workflow_name},
        )
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        json.dumps(
                            {
                                "error": "Missing conversation_id in config. Please provide a valid conversation_id.",
                                "isError": True,
                            }
                        ),
                        tool_call_id=tool_call_id,
                    )
                ]
            }
        )

    try:
        writer = get_stream_writer()

        # Send workflow start event
        writer(
            {
                "type": "workflow_start",
                "workflow_name": workflow_name,
                "workflow_id": workflow_id,
                "tool_id": tool_call_id,
            }
        )

        # Step 1: Execute workflow and get correlation ID
        correlation_id = await _execute_workflow(
            workflow_id=workflow_id,
            user_id=user_id,
            organisation_id=organisation_id,
            payload=payload,
        )

        # Step 2: Stream workflow events
        final_result, collected_events, workflow_status = await _stream_workflow_events(
            correlation_id=correlation_id,
            writer=writer,
            tool_call_id=tool_call_id,
            workflow_name=workflow_name,
            workflow_id=workflow_id,
        )

        # Format the collected events
        formatted_events = format_workflow_output(collected_events)

        # Send completion event
        writer(
            {
                "type": "workflow_end",
                "workflow_name": workflow_name,
                "workflow_id": workflow_id,
                "workflow_status": workflow_status,
                "correlation_id": correlation_id,
                "final_result": final_result,
                "db_save": True,
            }
        )

        await asyncio.sleep(0.05)

        # Record successful workflow execution
        duration_ms = (time.time() - start_time) * 1000
        metrics_manager.record_workflow_execution(
            duration_ms=duration_ms,
            attributes={
                "workflow_name": workflow_name,
                "workflow_id": workflow_id,
                "correlation_id": correlation_id,
                "user_id": user_id,
            },
        )

        # Generate summary from events
        summary = get_workflow_summary(formatted_events)

        # Return Command with array of events
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        json.dumps(
                            {
                                "message": summary,
                                "correlation_id": correlation_id,
                                "workflow_id": workflow_id,
                                "workflow_name": workflow_name,
                                "workflow_status": workflow_status,  # Add workflow status from last tool_stream
                                "events": formatted_events,  # Array of formatted events
                                "isError": False,
                            }
                        ),
                        tool_call_id=tool_call_id,
                    )
                ]
            }
        )

    except TimeoutError as e:
        logger.error(f"Workflow execution timeout: {e}")
        exception_tracker.track_exception(
            e,
            severity="error",
            attributes={
                "component": "workflow_execution_tool",
                "workflow_name": workflow_name,
                "workflow_id": workflow_id,
                "error_type": "timeout",
            },
        )

        metrics_manager.record_workflow_error(
            error_type="TimeoutError",
            attributes={
                "workflow_name": workflow_name,
                "workflow_id": workflow_id,
            },
        )
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        json.dumps(
                            {
                                "error": f"Workflow execution timed out after {STREAM_TIMEOUT} seconds",
                                "isError": True,
                            }
                        ),
                        tool_call_id=tool_call_id,
                    )
                ]
            }
        )

    except Exception as e:
        logger.error(f"Workflow execution error: {e}")
        # Track exception with Signoz OTEL
        exception_tracker.track_exception(
            e,
            severity="error",
            attributes={
                "component": "workflow_execution_tool",
                "workflow_name": workflow_name,
                "workflow_id": workflow_id,
                "user_id": user_id if user_id else "unknown",
            },
        )

        metrics_manager.record_workflow_error(
            error_type=type(e).__name__,
            attributes={
                "workflow_name": workflow_name,
                "workflow_id": workflow_id,
            },
        )
        return Command(
            update={
                "messages": [
                    ToolMessage(
                        json.dumps(
                            {
                                "error": str(e),
                                "isError": True,
                            }
                        ),
                        tool_call_id=tool_call_id,
                    )
                ]
            }
        )

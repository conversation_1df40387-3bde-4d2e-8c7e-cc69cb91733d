"""
Knowledge Base Tools for searching organization's knowledge base and resolving user identities.

This module provides two main tools:
1. search_by_source: Search the organization's knowledge base across various sources
2. resolve_identity: Resolve user identity by name with fuzzy matching
"""

import logging
import json
import time
from typing import Annotated, Optional
from langchain_core.tools import tool, InjectedToolCallId
from langchain_core.runnables.config import RunnableConfig
from langchain_core.messages import ToolMessage
from langgraph.types import Command
from app.utils.mcp_client import MCPClient
from app.shared.config.constants import AgentToolEnum, KnowledgeSource
from app.shared.config.base import get_settings
from app.agent.utils.knowledge_base_utils import (
    extract_tool_response,
    format_knowledge_base_response,
    format_identity_response,
    extract_knowledge_base_sources,
    parse_global_search_response,
    extract_global_search_sources,
)
from app.utils.tracing import trace_operation
from app.utils.metrics import get_metrics_manager
from app.utils.exceptions import get_exception_tracker
from app.utils.agent_redis_store import store_sources, store_approval_data

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()

# Tool descriptions
KNOWLEDGE_BASE_TOOL_DESCRIPTION = """
Searches the organization's knowledge base for relevant documents and information across sources like Google Drive, Confluence, Jira, GitHub, and Google Calendar.
"""

KNOWLEDGE_BASE_IDP_TOOL_DESCRIPTION = """
Resolves user identity by name with fuzzy matching to find organization members.
"""


@tool(
    AgentToolEnum.KNOWLEDGE_BASE.value,
    description=KNOWLEDGE_BASE_TOOL_DESCRIPTION,
)
async def search_by_source(
    query_text: str,
    tool_call_id: Annotated[str, InjectedToolCallId],
    agent_id: Optional[str] = None,
    least_score: Optional[float] = None,
    identity_resolve: Optional[str] = None,
    config: Annotated[RunnableConfig, "Configuration object"] = None,
) -> Command:
    """
    Search the knowledge base by specific source (e.g., GOOGLE_DRIVE, JIRA).
    Source and file IDs are automatically taken from the config (passed from gateway).
    Returns top 10 results by default.

    Args:
        query_text: The search query text
        tool_call_id: Injected tool call identifier for message tracking
        agent_id: Optional agent ID if using a specific agent
        least_score: Optional minimum similarity score threshold
        identity_resolve: Optional resolved email/identity for person-specific searches
    """
    with trace_operation(
        "knowledge_base.search_by_source",
        attributes={
            "query_length": len(query_text),
            "agent_id": agent_id or "default",
            "has_least_score": least_score is not None,
            "has_identity_resolve": identity_resolve is not None,
        },
    ) as span:
        start_time = time.time()
        metrics_manager = get_metrics_manager()

        user_id = None
        organisation_id = None
        kb_source = None
        kb_file_ids = None
        timezone = None
        conversation_id = None

        if config and "configurable" in config:
            user_id = config["configurable"].get("user_id")
            organisation_id = config["configurable"].get("organisation_id")
            kb_source = config["configurable"].get("kb_source")
            kb_file_ids = config["configurable"].get("kb_file_ids")
            timezone = config["configurable"].get("timezone", "UTC")
            conversation_id = config["configurable"].get("conversation_id")

        span.set_attribute("user_id", user_id or "unknown")
        span.set_attribute("organisation_id", organisation_id or "unknown")
        span.set_attribute("kb_source", kb_source or "any")

        if not user_id:
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            json.dumps(
                                {
                                    "error": "Missing user_id in config. Please provide a valid user_id.",
                                    "missing_fields": {"user_id": "missing"},
                                    "isError": True,
                                }
                            ),
                            tool_call_id=tool_call_id,
                        )
                    ]
                }
            )

        if not organisation_id:
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            json.dumps(
                                {
                                    "error": "Missing organisation_id in config. Please provide a valid organisation_id.",
                                    "missing_fields": {"organisation_id": "missing"},
                                    "isError": True,
                                }
                            ),
                            tool_call_id=tool_call_id,
                        )
                    ]
                }
            )

        # Validate source if provided
        if kb_source is not None:
            try:
                # Validate that source is a valid KnowledgeSource enum value
                KnowledgeSource(kb_source)
            except ValueError:
                valid_sources = [source.value for source in KnowledgeSource]
                logger.error(
                    f"Invalid knowledge source: {kb_source}. Valid sources: {valid_sources}"
                )
                span.set_attribute("error_type", "invalid_source")
                return Command(
                    update={
                        "messages": [
                            ToolMessage(
                                json.dumps(
                                    {
                                        "error": f"Invalid knowledge source: '{kb_source}'. Valid sources are: {', '.join(valid_sources)}",
                                        "valid_sources": valid_sources,
                                        "isError": True,
                                    }
                                ),
                                tool_call_id=tool_call_id,
                            )
                        ]
                    }
                )

        try:
            client = MCPClient(user_id)

            settings = get_settings()

            # Handle "ALL" source with global_search_agent
            if kb_source == "ALL":
                tool_parameters = {
                    "user_id": user_id,
                    "organisation_id": organisation_id,
                    "query_text": query_text,
                    "top_k": 10,
                    "include_graph_context": True,
                    "timezone": timezone if timezone else "UTC",
                }

                result = await client.execute_tool(
                    mcp_name_slug=settings.kb_mcp,
                    tool_name="global_search_agent",
                    tool_parameters=tool_parameters,
                )

                # Extract actual response from CallToolResult wrapper
                extracted_response = extract_tool_response(result)

                # Parse global search response (specific formatting for this tool)
                formatted_response = parse_global_search_response(
                    extracted_response, query_text
                )

                # Extract sources from global search results
                kb_sources = extract_global_search_sources(formatted_response)
            else:
                # Handle regular source searches
                tool_parameters = {
                    "user_id": user_id,
                    "organisation_id": organisation_id,
                    "query_text": query_text,
                    "top_k": 10,
                    "timezone": timezone if timezone else "UTC",
                }

                # Only add source if it's provided from config
                if kb_source is not None:
                    tool_parameters["source"] = kb_source

                if agent_id is not None:
                    tool_parameters["agent_id"] = agent_id

                if least_score is not None:
                    tool_parameters["least_score"] = least_score

                # Add identity_resolve if provided (resolved email from approval)
                if identity_resolve is not None:
                    tool_parameters["identity_resolve"] = identity_resolve

                # Add file_ids from config, default to empty list if None
                tool_parameters["file_ids"] = (
                    kb_file_ids if kb_file_ids is not None else []
                )

                result = await client.execute_tool(
                    mcp_name_slug=settings.kb_mcp,
                    tool_name="search_by_source",
                    tool_parameters=tool_parameters,
                )

                # Extract actual response from CallToolResult wrapper
                extracted_response = extract_tool_response(result)

                # Format the response (helper function extracts connector_type from response)
                formatted_response = format_knowledge_base_response(
                    extracted_response, query_text
                )

                # Extract sources from the results
                kb_sources = extract_knowledge_base_sources(formatted_response)

            span.set_attribute("results_count", len(kb_sources))

            # Record successful knowledge base search
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_tool_execution(
                tool_name="knowledge_base_search",
                duration_ms=duration_ms,
                attributes={
                    "query_length": len(query_text),
                    "results_count": len(kb_sources),
                    "kb_source": kb_source or "any",
                },
            )

            # Store sources in Redis instead of state
            if conversation_id:
                await store_sources(conversation_id, "knowledge_base", kb_sources)

            # Return Command with message only
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            json.dumps(formatted_response),
                            tool_call_id=tool_call_id,
                        )
                    ],
                }
            )
        except Exception as e:
            # Track exception with Signoz OTEL
            exception_tracker.track_exception(
                e,
                severity="error",
                attributes={
                    "component": "knowledge_base_tool",
                    "kb_source": kb_source or "any",
                    "query_length": len(query_text) if query_text else 0,
                    "user_id": user_id,
                    "organisation_id": organisation_id,
                },
            )

            logger.error(f"Error in search_by_source: {str(e)}")
            span.set_attribute("error_type", "execution_error")
            metrics_manager.record_tool_error(
                tool_name="knowledge_base_search",
                error_type=type(e).__name__,
                attributes={"kb_source": kb_source or "any"},
            )
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            json.dumps({"error": str(e), "isError": True}),
                            tool_call_id=tool_call_id,
                        )
                    ]
                }
            )


@tool(
    AgentToolEnum.KNOWLEDGE_BASE_IDP.value,
    description=KNOWLEDGE_BASE_IDP_TOOL_DESCRIPTION,
)
async def resolve_identity(
    name: str,
    tool_call_id: Annotated[str, InjectedToolCallId],
    threshold: Optional[float] = None,
    config: Annotated[RunnableConfig, "Configuration object"] = None,
) -> Command:
    """
    Resolve user identity by name with fuzzy matching.

    Args:
        name: The user name to search for
        threshold: Optional similarity threshold for matching (if not provided, server default is used)
    """
    with trace_operation(
        "knowledge_base.resolve_identity",
        attributes={
            "name": name,
            "has_threshold": threshold is not None,
            "threshold": threshold or 0,
        },
    ) as span:
        start_time = time.time()
        metrics_manager = get_metrics_manager()

        user_id = None
        organisation_id = None
        conversation_id = None

        if config and "configurable" in config:
            user_id = config["configurable"].get("user_id")
            organisation_id = config["configurable"].get("organisation_id")
            conversation_id = config["configurable"].get("conversation_id")

        span.set_attribute("user_id", user_id or "unknown")
        span.set_attribute("organisation_id", organisation_id or "unknown")

        if not user_id:
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            json.dumps(
                                {
                                    "error": "Missing user_id in config. Please provide a valid user_id.",
                                    "missing_fields": {"user_id": "missing"},
                                    "isError": True,
                                }
                            ),
                            tool_call_id=tool_call_id,
                        )
                    ]
                }
            )

        if not organisation_id:
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            json.dumps(
                                {
                                    "error": "Missing organisation_id in config. Please provide a valid organisation_id.",
                                    "missing_fields": {"organisation_id": "missing"},
                                    "isError": True,
                                }
                            ),
                            tool_call_id=tool_call_id,
                        )
                    ]
                }
            )

        try:
            client = MCPClient(user_id)

            tool_parameters = {
                "user_id": user_id,
                "organisation_id": organisation_id,
                "name": name,
                "enable_access_control": False,
            }

            # Only add threshold if provided
            if threshold is not None:
                tool_parameters["threshold"] = threshold

            settings = get_settings()
            result = await client.execute_tool(
                mcp_name_slug=settings.kb_mcp,
                tool_name="resolve_identity",
                tool_parameters=tool_parameters,
            )

            # Parse and format the response using helper function
            extracted_response = extract_tool_response(result)
            total_results, formatted_results = format_identity_response(
                extracted_response, name
            )

            span.set_attribute("total_results", total_results)

            # Record successful identity resolution
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_tool_execution(
                tool_name="identity_resolve",
                duration_ms=duration_ms,
                attributes={
                    "name_length": len(name),
                    "results_count": total_results,
                },
            )

            # Handle different cases based on total_results
            if total_results == 0:
                span.set_attribute("result_status", "no_match")
                return Command(
                    update={
                        "messages": [
                            ToolMessage(
                                json.dumps(
                                    {
                                        "result": [],
                                        "isError": False,
                                        "message": f"No member found matching '{name}'.",
                                    }
                                ),
                                tool_call_id=tool_call_id,
                            )
                        ],
                    }
                )
            elif total_results == 1:
                span.set_attribute("result_status", "single_match")
                return Command(
                    update={
                        "messages": [
                            ToolMessage(
                                json.dumps(
                                    {
                                        "result": formatted_results[0],
                                        "isError": False,
                                        "message": f"Found member: {formatted_results[0]['name']}",
                                    }
                                ),
                                tool_call_id=tool_call_id,
                            )
                        ],
                    }
                )
            else:  # total_results > 1
                span.set_attribute("result_status", "multiple_matches")
                
                # Store approval data in Redis instead of state
                if conversation_id:
                    await store_approval_data(
                        conversation_id,
                        "resolve_identity",
                        {
                            "members": formatted_results,
                            "query": name,
                            "message": f"Found {total_results} matching members for '{name}'.",
                        }
                    )
                
                return Command(
                    update={
                        "messages": [
                            ToolMessage(
                                json.dumps(
                                    {
                                        "result": formatted_results,
                                        "isError": False,
                                        "message": f"Found {total_results} matching members. Awaiting user approval.",
                                    }
                                ),
                                tool_call_id=tool_call_id,
                            )
                        ],
                    }
                )
        except Exception as e:
            # Track exception with Signoz OTEL
            exception_tracker.track_exception(
                e,
                severity="error",
                attributes={
                    "component": "identity_resolve_tool",
                    "name": name,
                },
            )

            logger.error(f"Error in resolve_identity: {str(e)}")
            span.set_attribute("error_type", "execution_error")
            metrics_manager.record_tool_error(
                tool_name="identity_resolve",
                error_type=type(e).__name__,
            )
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            json.dumps({"error": str(e), "isError": True}),
                            tool_call_id=tool_call_id,
                        )
                    ]
                }
            )

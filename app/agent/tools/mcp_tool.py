"""
MCP Tool for executing Model Context Protocol integrations.

This tool allows the agent to execute specific tools from MCP integrations
with provided parameters, leveraging specialized capabilities from external MCP services.
"""

import logging
import time
from typing import Annotated
from langchain_core.tools import tool
from langchain_core.runnables.config import RunnableConfig
from app.utils.mcp_client import MC<PERSON>lient
from app.shared.config.constants import AgentTool<PERSON><PERSON>
from app.utils.tracing import trace_operation
from app.utils.metrics import get_metrics_manager
from app.utils.exceptions import get_exception_tracker

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()

# Tool description
MCP_TOOL_DESCRIPTION = """
- Executes a specific tool from an MCP (Model Context Protocol) integration with provided parameters
- Allows the agent to leverage specialized capabilities from external MCP services
- Use this tool when:
  • You have already found or identified a specific MCP tool to use
  • You have the MCP name/slug, tool name, and required parameters ready
  • You need to execute an MCP tool to complete a task that current capabilities cannot handle
- Avoid using this tool if:
  • You haven't identified which MCP tool to use (use mcp_search first)
  • The required parameters are missing or unclear
"""


@tool(
    AgentToolEnum.EXECUTE_MCP.value,
    description=MCP_TOOL_DESCRIPTION,
)
async def execute_mcp(
    mcp_name_slug: str,
    tool_name: str,
    tool_parameters: dict,
    mcp_name: str = "",
    mcp_description: str = "",
    mcp_logo: str = "",
    config: Annotated[RunnableConfig, "Configuration object"] = None,
):
    """
    Execute an MCP tool with the given parameters.

    Args:
        mcp_name_slug: The name slug of the MCP to execute
        tool_name: The name of the tool to execute within the MCP
        tool_parameters: Dictionary of parameters required by the tool
        mcp_name: The display name of the MCP (required)
        mcp_description: Description of the MCP (required)
        mcp_logo: Logo URL of the MCP (required)
        config: Runnable configuration for accessing state

    Returns:
        Enhanced result with MCP metadata
    """
    start_time = time.time()
    metrics_manager = get_metrics_manager()
    
    user_id = None
    if config and "configurable" in config:
        user_id = config["configurable"].get("user_id")

    if not user_id:
        metrics_manager.record_tool_error(
            tool_name="execute_mcp",
            error_type="MissingUserId",
        )
        return {
            "error": "Missing user_id in config. Please provide a valid user_id.",
            "missing_fields": {
                "user_id": "missing" if not user_id else "provided",
            },
            "isError": True,
        }

    if not mcp_name:
        metrics_manager.record_tool_error(
            tool_name="execute_mcp",
            error_type="MissingMCPName",
        )
        return {
            "error": "Missing required field: mcp_name",
            "missing_fields": {"mcp_name": "missing"},
            "isError": True,
        }

    if not mcp_description:
        metrics_manager.record_tool_error(
            tool_name="execute_mcp",
            error_type="MissingMCPDescription",
        )
        return {
            "error": "Missing required field: mcp_description",
            "missing_fields": {"mcp_description": "missing"},
            "isError": True,
        }

    try:
        client = MCPClient(user_id)
        result = await client.execute_tool(
            mcp_name_slug=mcp_name_slug,
            tool_name=tool_name,
            tool_parameters=tool_parameters,
        )

        # Record successful MCP execution
        duration_ms = (time.time() - start_time) * 1000
        metrics_manager.record_tool_execution(
            tool_name="execute_mcp",
            duration_ms=duration_ms,
            attributes={
                "mcp_name_slug": mcp_name_slug,
                "mcp_tool_name": tool_name,
            },
        )

        return {
            "result": result,
            "mcp_name": mcp_name,
            "mcp_description": mcp_description,
            "mcp_logo": mcp_logo,
            "isError": False,
        }
    except Exception as e:
        # Track exception with Signoz OTEL
        exception_tracker.track_exception(
            e,
            severity="error",
            attributes={
                "component": "execute_mcp_tool",
                "mcp_name_slug": mcp_name_slug,
                "tool_name": tool_name,
                "user_id": user_id,
            },
        )
        
        logger.error(f"Error in execute_mcp: {str(e)}")
        metrics_manager.record_tool_error(
            tool_name="execute_mcp",
            error_type=type(e).__name__,
            attributes={
                "mcp_name_slug": mcp_name_slug,
                "mcp_tool_name": tool_name,
            },
        )
        return {"error": str(e), "isError": True}

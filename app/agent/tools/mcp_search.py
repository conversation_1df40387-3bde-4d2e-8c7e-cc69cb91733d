"""
MCP Search Tool for discovering MCP tools from the marketplace.

This tool searches the MCP marketplace to find tools that can help complete tasks,
returning the most relevant MCP tools and their capabilities based on the search query.
"""

import time
import json
import logging
from langchain_core.tools import tool, InjectedToolCallId
from langchain_core.messages import ToolMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.types import Command
from typing import Annotated
from app.workflow_chat.tools.RAG import provider
from app.shared.config.constants import AgentToolEnum
from app.utils.tracing import trace_operation
from app.utils.metrics import get_metrics_manager
from app.utils.exceptions import get_exception_tracker
from app.utils.agent_redis_store import store_approval_data

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()

# Tool description
MCP_SEARCH_TOOL_DESCRIPTION = """
- Searches the MCP marketplace to discover tools that can help complete the current task
- Returns the most relevant MCP tools and their capabilities based on your query
- Use this tool when:
  • Current tools and capabilities cannot complete the user's task
  • You need to find specialized MCP tools from the marketplace
  • The user's request requires external integrations beyond built-in tools
  • An MCP or specific tool within the agent doesn't fully support the required functionality
- Avoid using this tool if:
  • Current built-in tools can already handle the task
  • The task doesn't require external MCP integrations
"""


@tool(
    AgentToolEnum.MCP_SEARCH.value,
    description=MCP_SEARCH_TOOL_DESCRIPTION,
)
async def mcp_search(
    query: str,
    tool_call_id: Annotated[str, InjectedToolCallId],
    config: Annotated[RunnableConfig, "Configuration object"] = None,
) -> Command:
    """Search the marketplace for MCP tools semantically similar to the query. Returns top 3 MCP tools."""
    start_time = time.time()
    metrics_manager = get_metrics_manager()
    
    # Extract conversation_id from config
    conversation_id = None
    if config and "configurable" in config:
        conversation_id = config["configurable"].get("conversation_id")
    
    with trace_operation(
        "tool.mcp_search",
        attributes={
            "query": query[:200] if len(query) > 200 else query,
            "query_length": len(query),
            "limit": 3,
        },
    ) as span:
        try:
            result = provider.search_marketplace(
                query=query, limit=3, type_list=["mcp_tool"]
            )

            # Transform results: rename type_id to mcp_id
            if result:
                for item in result:
                    if "type_id" in item:
                        item["mcp_id"] = item.pop("type_id")

            span.set_attribute("results_count", len(result) if result else 0)

            # Record successful MCP search
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_tool_execution(
                tool_name="mcp_search",
                duration_ms=duration_ms,
                attributes={
                    "query_length": len(query),
                    "results_count": len(result) if result else 0,
                },
            )

            if result and len(result) > 0:
                span.set_attribute("requires_approval", True)
                span.set_attribute("success", True)
                
                # Store approval data in Redis instead of state
                if conversation_id:
                    await store_approval_data(
                        conversation_id,
                        "mcp_search",
                        {
                            "tools": result,
                            "query": query,
                            "message": f"Found {len(result)} MCP tools for your request."
                        }
                    )
                
                return Command(
                    update={
                        "messages": [
                            ToolMessage(
                                json.dumps({
                                    "result": result,
                                    "isError": False,
                                    "message": f"Found {len(result)} tools. Awaiting user approval."
                                }),
                                tool_call_id=tool_call_id
                            )
                        ]
                    }
                )
            else:
                span.set_attribute("requires_approval", False)
                span.set_attribute("no_results", True)
                return Command(
                    update={
                        "messages": [
                            ToolMessage(
                                json.dumps({
                                    "result": [],
                                    "isError": False,
                                    "message": "No tools found for this query."
                                }),
                                tool_call_id=tool_call_id
                            )
                        ]
                    }
                )
        except Exception as e:
            # Track exception with Signoz OTEL
            exception_tracker.track_exception(
                e,
                severity="error",
                attributes={
                    "component": "mcp_search_tool",
                    "query_length": len(query) if query else 0,
                },
            )
            
            span.set_attribute("error", True)
            span.record_exception(e)
            metrics_manager.record_tool_error(
                tool_name="mcp_search",
                error_type=type(e).__name__,
            )
            return Command(
                update={
                    "messages": [
                        ToolMessage(
                            json.dumps({"error": str(e), "isError": True}),
                            tool_call_id=tool_call_id
                        )
                    ]
                }
            )

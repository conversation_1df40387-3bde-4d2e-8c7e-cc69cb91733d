"""
Custom ChatOpenAI wrapper that captures reasoning_content from model responses.

This module provides a custom wrapper over langchain_openai.ChatOpenAI that
overrides the _create_chat_result and _convert_chunk_to_generation_chunk methods
to extract and store reasoning_content in the message's additional_kwargs,
similar to how ChatDeepSeek handles it.

Reference: https://github.com/langchain-ai/langchain/issues/31326
Reference: https://python.langchain.com/api_reference/_modules/langchain_deepseek/chat_models.html
"""

from typing import Union, Optional, Dict, Any, Type
import openai
import logging
from langchain_core.outputs import ChatResult, ChatGenerationChunk
from langchain_core.messages import AIMessageChunk
from langchain_openai import ChatOpenAI

logger = logging.getLogger(__name__)


class ChatOpenAIWithReasoning(ChatOpenAI):
    """
    Extended ChatOpenAI class that captures reasoning_content from responses.

    This class overrides the _create_chat_result and _convert_chunk_to_generation_chunk
    methods to extract reasoning_content from the OpenAI API response and store it in
    the message's additional_kwargs. This is useful for models that provide reasoning
    traces (like DeepSeek R1, Qwen, etc.) when accessed through OpenRouter or compatible APIs.

    The implementation is based on langchain-deepseek's ChatDeepSeek class.

    Usage:
        >>> from app.agent.config.chat_openai_with_reasoning import ChatOpenAIWithReasoning
        >>> model = ChatOpenAIWithReasoning(
        ...     api_key="your-api-key",
        ...     base_url="https://openrouter.ai/api/v1",
        ...     model="deepseek/deepseek-r1"
        ... )
        >>> response = model.invoke("What is 2+2?")
        >>> # Access reasoning content if available
        >>> reasoning = response.additional_kwargs.get("reasoning_content")
    """

    def _create_chat_result(
        self,
        response: Union[dict, openai.BaseModel],
        generation_info: Optional[dict] = None,
    ) -> ChatResult:
        """
        Override _create_chat_result to capture reasoning_content.

        This method first calls the parent class's _create_chat_result to get
        the standard ChatResult, then checks if the response contains reasoning_content
        and adds it to the message's additional_kwargs if present.

        Based on ChatDeepSeek implementation.

        Args:
            response: The response from the OpenAI API (dict or BaseModel)
            generation_info: Optional generation information

        Returns:
            ChatResult with reasoning_content in additional_kwargs if available
        """
        # Call parent class method to get standard ChatResult
        rtn = super()._create_chat_result(response, generation_info)

        # Only process BaseModel responses (not dict responses)
        if not isinstance(response, openai.BaseModel):
            return rtn

        # Extract reasoning_content from the response
        choices = getattr(response, "choices", None)
        if choices and hasattr(choices[0].message, "reasoning_content"):
            # Direct reasoning_content attribute (DeepSeek R1, Qwen, etc.)
            rtn.generations[0].message.additional_kwargs["reasoning_content"] = (
                choices[0].message.reasoning_content
            )
            logger.info(f"Found reasoning_content attribute (length: {len(choices[0].message.reasoning_content)})")
        elif choices and hasattr(choices[0].message, "model_extra"):
            # Handle use via OpenRouter or other proxies that use model_extra
            model_extra = choices[0].message.model_extra
            if isinstance(model_extra, dict) and (reasoning := model_extra.get("reasoning")):
                rtn.generations[0].message.additional_kwargs["reasoning_content"] = reasoning
                logger.info(f"Found reasoning in model_extra (length: {len(reasoning)})")

        return rtn

    def _convert_chunk_to_generation_chunk(
        self,
        chunk: dict,
        default_chunk_class: Type,
        base_generation_info: Optional[dict],
    ) -> Optional[ChatGenerationChunk]:
        """
        Override _convert_chunk_to_generation_chunk to capture reasoning_content in streaming.

        This method extracts reasoning_content from the delta field in streaming chunks
        and adds it to the message's additional_kwargs.

        Based on ChatDeepSeek implementation.

        Args:
            chunk: The streaming chunk from the API
            default_chunk_class: The default message chunk class
            base_generation_info: Base generation information

        Returns:
            ChatGenerationChunk with reasoning_content in additional_kwargs if available
        """
        # Call parent class method to get standard ChatGenerationChunk
        generation_chunk = super()._convert_chunk_to_generation_chunk(
            chunk,
            default_chunk_class,
            base_generation_info,
        )

        # Extract reasoning_content from the delta field in streaming chunks
        if (choices := chunk.get("choices")) and generation_chunk:
            top = choices[0]
            if isinstance(generation_chunk.message, AIMessageChunk):
                # Check for reasoning_content in delta (DeepSeek R1, Qwen, etc.)
                if (reasoning_content := top.get("delta", {}).get("reasoning_content")) is not None:
                    generation_chunk.message.additional_kwargs["reasoning_content"] = reasoning_content
                    logger.debug(f"Found reasoning_content in delta (length: {len(reasoning_content)})")
                # Handle use via OpenRouter or other proxies that use "reasoning" field
                elif (reasoning := top.get("delta", {}).get("reasoning")) is not None:
                    generation_chunk.message.additional_kwargs["reasoning_content"] = reasoning
                    logger.debug(f"Found reasoning in delta (length: {len(reasoning)})")

        return generation_chunk


"""
Custom Mem0 Configuration with Headers Support
------------------------------------------------
Extended mem0 classes to support custom headers for OpenAI/OpenRouter requests.
"""

from typing import Optional, Dict, Any, Callable, List

from mem0.configs.llms.base import BaseLlmConfig
from mem0.configs.embeddings.base import BaseEmbedderConfig
from mem0.llms.openai import OpenAILLM
from mem0.embeddings.openai import OpenAIEmbedding
from openai import OpenAI


class OpenAIConfigWithHeaders(BaseLlmConfig):
    """
    Extended OpenAI configuration class that supports custom headers.
    Extends mem0's BaseLlmConfig to add header support.
    """

    def __init__(
        self,
        # Base parameters
        model: Optional[str] = None,
        temperature: float = 0.1,
        api_key: Optional[str] = None,
        max_tokens: int = 2000,
        top_p: float = 0.1,
        top_k: int = 1,
        enable_vision: bool = False,
        vision_details: Optional[str] = "auto",
        http_client_proxies: Optional[dict] = None,
        # OpenAI-specific parameters
        openai_base_url: Optional[str] = None,
        models: Optional[List[str]] = None,
        route: Optional[str] = "fallback",
        openrouter_base_url: Optional[str] = None,
        site_url: Optional[str] = None,
        app_name: Optional[str] = None,
        store: bool = False,
        # Response monitoring callback
        response_callback: Optional[Callable[[Any, dict, dict], None]] = None,
        # Custom headers support
        default_headers: Optional[Dict[str, str]] = None,
    ):
        """
        Initialize OpenAI configuration with custom headers support.

        Args:
            model: OpenAI model to use, defaults to None
            temperature: Controls randomness, defaults to 0.1
            api_key: OpenAI API key, defaults to None
            max_tokens: Maximum tokens to generate, defaults to 2000
            top_p: Nucleus sampling parameter, defaults to 0.1
            top_k: Top-k sampling parameter, defaults to 1
            enable_vision: Enable vision capabilities, defaults to False
            vision_details: Vision detail level, defaults to "auto"
            http_client_proxies: HTTP client proxy settings, defaults to None
            openai_base_url: OpenAI API base URL, defaults to None
            models: List of models for OpenRouter, defaults to None
            route: OpenRouter route strategy, defaults to "fallback"
            openrouter_base_url: OpenRouter base URL, defaults to None
            site_url: Site URL for OpenRouter, defaults to None
            app_name: Application name for OpenRouter, defaults to None
            store: Store conversation history, defaults to False
            response_callback: Optional callback for monitoring LLM responses
            default_headers: Custom headers to send with requests, defaults to None
        """
        # Initialize base parameters
        super().__init__(
            model=model,
            temperature=temperature,
            api_key=api_key,
            max_tokens=max_tokens,
            top_p=top_p,
            top_k=top_k,
            enable_vision=enable_vision,
            vision_details=vision_details,
            http_client_proxies=http_client_proxies,
        )

        # OpenAI-specific parameters
        self.openai_base_url = openai_base_url
        self.models = models
        self.route = route
        self.openrouter_base_url = openrouter_base_url
        self.site_url = site_url
        self.app_name = app_name
        self.store = store

        # Response monitoring
        self.response_callback = response_callback

        # Custom headers
        self.default_headers = default_headers or {}


class OpenAILLMWithHeaders(OpenAILLM):
    """
    Extended OpenAI LLM class that supports custom headers.
    Extends mem0's OpenAILLM to pass custom headers to the OpenAI client.
    """

    def __init__(self, config: OpenAIConfigWithHeaders):
        """
        Initialize OpenAI LLM with custom headers support.

        Args:
            config: OpenAIConfigWithHeaders instance with header configuration
        """
        # Store the config
        self.config = config

        # Initialize the OpenAI client with custom headers
        client_kwargs = {}
        if hasattr(config, 'api_key') and config.api_key:
            client_kwargs["api_key"] = config.api_key
        if hasattr(config, 'openai_base_url') and config.openai_base_url:
            client_kwargs["base_url"] = config.openai_base_url
        if hasattr(config, 'default_headers') and config.default_headers:
            client_kwargs["default_headers"] = config.default_headers
        if hasattr(config, 'http_client_proxies') and config.http_client_proxies:
            import httpx
            client_kwargs["http_client"] = httpx.Client(proxies=config.http_client_proxies)

        self.client = OpenAI(**client_kwargs)

        # Set other attributes that OpenAILLM expects
        self.model = config.model
        self.temperature = config.temperature
        self.max_tokens = config.max_tokens
        self.top_p = config.top_p


class OpenAIEmbeddingConfigWithHeaders(BaseEmbedderConfig):
    """
    Extended OpenAI embedding configuration class that supports custom headers.
    Extends mem0's BaseEmbedderConfig to add header support.
    """

    def __init__(
        self,
        model: Optional[str] = None,
        embedding_dims: Optional[int] = None,
        api_key: Optional[str] = None,
        openai_base_url: Optional[str] = None,
        http_client_proxies: Optional[dict] = None,
        # Custom headers support
        default_headers: Optional[Dict[str, str]] = None,
    ):
        """
        Initialize OpenAI embedding configuration with custom headers support.

        Args:
            model: OpenAI embedding model to use, defaults to None
            embedding_dims: Embedding dimensions, defaults to None
            api_key: OpenAI API key, defaults to None
            openai_base_url: OpenAI API base URL, defaults to None
            http_client_proxies: HTTP client proxy settings, defaults to None
            default_headers: Custom headers to send with requests, defaults to None
        """
        # Initialize base parameters
        super().__init__(
            model=model,
            embedding_dims=embedding_dims,
            api_key=api_key,
        )

        # OpenAI-specific parameters
        self.openai_base_url = openai_base_url
        self.http_client_proxies = http_client_proxies

        # Custom headers
        self.default_headers = default_headers or {}


class OpenAIEmbeddingWithHeaders(OpenAIEmbedding):
    """
    Extended OpenAI Embedding class that supports custom headers.
    Extends mem0's OpenAIEmbedding to pass custom headers to the OpenAI client.
    """

    def __init__(self, config: OpenAIEmbeddingConfigWithHeaders):
        """
        Initialize OpenAI Embedding with custom headers support.

        Args:
            config: OpenAIEmbeddingConfigWithHeaders instance with header configuration
        """
        # Store the config
        self.config = config

        # Set model and embedding dimensions
        self.config.model = self.config.model or "text-embedding-3-small"
        self.config.embedding_dims = self.config.embedding_dims or 1536

        # Initialize the OpenAI client with custom headers
        client_kwargs = {}
        if hasattr(config, 'api_key') and config.api_key:
            client_kwargs["api_key"] = config.api_key
        if hasattr(config, 'openai_base_url') and config.openai_base_url:
            client_kwargs["base_url"] = config.openai_base_url
        if hasattr(config, 'default_headers') and config.default_headers:
            client_kwargs["default_headers"] = config.default_headers
        if hasattr(config, 'http_client_proxies') and config.http_client_proxies:
            import httpx
            client_kwargs["http_client"] = httpx.Client(proxies=config.http_client_proxies)

        self.client = OpenAI(**client_kwargs)


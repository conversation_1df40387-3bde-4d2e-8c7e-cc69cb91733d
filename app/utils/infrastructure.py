import asyncio
import psutil
from opentelemetry import metrics
from typing import Optional


class InfrastructureMonitor:
    """Monitor system infrastructure metrics"""

    def __init__(self):
        self.meter = metrics.get_meter(__name__)
        self._task: Optional[asyncio.Task] = None

        # Create metrics
        self.cpu_usage = self.meter.create_gauge(
            name="system.cpu.usage",
            description="CPU usage percentage",
            unit="%",
        )
        self.memory_usage = self.meter.create_gauge(
            name="system.memory.usage",
            description="Memory usage percentage",
            unit="%",
        )
        self.memory_available = self.meter.create_gauge(
            name="system.memory.available",
            description="Available memory in GB",
            unit="GB",
        )
        self.disk_usage = self.meter.create_gauge(
            name="system.disk.usage",
            description="Disk usage percentage",
            unit="%",
        )

    async def collect_metrics(self):
        """Collect infrastructure metrics"""

        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        self.cpu_usage.set(cpu_percent)

        # Memory usage
        memory = psutil.virtual_memory()
        self.memory_usage.set(memory.percent)
        self.memory_available.set(memory.available / (1024**3))  # Convert bytes → GB

        # Disk usage
        disk = psutil.disk_usage("/")
        self.disk_usage.set(disk.percent)

    async def start_monitoring(self, interval: int = 60):
        """Start background monitoring task"""

        async def monitor_loop():
            while True:
                try:
                    await self.collect_metrics()
                    await asyncio.sleep(interval)
                except Exception as e:
                    print(f"Error collecting infrastructure metrics: {e}")
                    await asyncio.sleep(interval)

        self._task = asyncio.create_task(monitor_loop())

    async def stop_monitoring(self):
        """Stop background monitoring"""
        if self._task:
            self._task.cancel()


# Global instance
infrastructure_monitor: Optional[InfrastructureMonitor] = None


def get_infrastructure_monitor() -> InfrastructureMonitor:
    """Get or create the global infrastructure monitor"""
    global infrastructure_monitor
    if infrastructure_monitor is None:
        infrastructure_monitor = InfrastructureMonitor()
    return infrastructure_monitor

from opentelemetry import trace
from opentelemetry.trace import Span<PERSON><PERSON>
from contextlib import contextmanager
from typing import Dict, Any, Optional


@contextmanager
def trace_operation(
    name: str,
    kind: SpanKind = SpanKind.INTERNAL,
    attributes: Optional[Dict[str, Any]] = None
):
    """
    Context manager for creating a new span with OpenTelemetry.

    Args:
        name: Name of the operation/span
        kind: SpanKind (INTERNAL, CLIENT, SERVER, PRODUCER, CONSUMER)
        attributes: Optional dictionary of attributes to add to the span

    Yields:
        Span: The created span object
    """
    tracer = trace.get_tracer(__name__)

    with tracer.start_as_current_span(name, kind=kind, attributes=attributes) as span:
        try:
            yield span
        except Exception as e:
            span.set_attribute("error", True)
            span.record_exception(e)
            raise

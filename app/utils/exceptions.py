from opentelemetry import trace, metrics
from typing import Optional, Dict, Any
import traceback
import structlog
import os

logger = structlog.get_logger(__name__)


class ExceptionTracker:
    """Track exceptions with OpenTelemetry"""

    def __init__(self):
        self.meter = metrics.get_meter(__name__)
        self.exception_counter = self.meter.create_counter(
            name="exceptions.occurred",
            description="Total exceptions occurred",
            unit="1",
        )
        # Get service name from environment (should match what was set in telemetry setup)
        self.service_name = os.getenv("OTEL_SERVICE_NAME", "my-service")
        # Check if OTEL is enabled
        self.otel_enabled = os.getenv("OTEL_ENABLED", "false").lower() == "true"

    def track_exception(
        self,
        exc: Exception,
        severity: str = "error",
        attributes: Optional[Dict[str, Any]] = None,
    ):
        """Record exception in current span, log it, and update metrics"""
        span = trace.get_current_span()

        # 1. Record in trace span if OTEL is enabled
        if self.otel_enabled and span.is_recording():
            span.set_attribute("error", True)
            span.set_attribute("exception.type", type(exc).__name__)
            span.set_attribute("exception.severity", severity)
            span.set_attribute("exception.message", str(exc))
            span.record_exception(exc)

        # 2. Log exception using structlog
        log_context = {
            "exception_type": type(exc).__name__,
            "severity": severity,
            "traceback": traceback.format_exc(),
        }
        if attributes:
            log_context.update(attributes)
        
        logger.error("Exception occurred", **log_context)

        # 3. Record as metric for observability (only if OTEL is enabled)
        if self.otel_enabled:
            metric_attributes = {
                "exception_type": type(exc).__name__,
                "severity": severity,
                "service.name": self.service_name,
            }

            if attributes:
                metric_attributes.update(attributes)

            try:
                self.exception_counter.add(1, metric_attributes)
            except Exception as metric_error:
                # If metric recording fails, still log it but don't crash
                logger.error(
                    "Failed to record exception metric",
                    metric_error=str(metric_error),
                )


# ------------------------------------------------
# Global Singleton Accessor
# ------------------------------------------------
_exception_tracker: Optional[ExceptionTracker] = None


def get_exception_tracker() -> ExceptionTracker:
    """Get or create a global exception tracker"""
    global _exception_tracker
    if _exception_tracker is None:
        _exception_tracker = ExceptionTracker()
    return _exception_tracker


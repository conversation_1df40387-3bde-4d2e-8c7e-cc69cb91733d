"""
Utility functions for parsing and formatting workflow events.
"""
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


def parse_workflow_event(event_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Parse a single workflow event and extract relevant fields.
    
    Args:
        event_data: The raw event data from the workflow stream
        
    Returns:
        Parsed event dictionary with required fields, or None if db_save is False
    """
    try:
        # Only process events with db_save=True
        if not event_data.get("db_save", False):
            return None
            
        # Extract fields from event_data
        parsed_event = {
            "workflow_id": event_data.get("workflow_id", None),
            "correlation_id": event_data.get("correlation_id", None),
            "workflow_name": event_data.get("workflow_name", None),
            "node_name": event_data.get("node_name", None),
            "node_id": event_data.get("node_id", None),
            "workflow_status": event_data.get("workflow_status", None),
            "node_status": event_data.get("event_type", None),  # event_type mapped to node_status
            "created_at": event_data.get("created_at", None),
            "data": event_data.get("data", {})  # Full data json as is
        }
        
        return parsed_event
        
    except Exception as e:
        logger.error(f"Error parsing workflow event: {e}", exc_info=True)
        return None


def format_workflow_output(events: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Format a list of workflow events into the final output structure.
    
    Args:
        events: List of parsed workflow events
        
    Returns:
        Formatted list of events ready for output
    """
    formatted_events = []
    
    for event in events:
        if event is not None:  # Skip None events (filtered by db_save)
            formatted_events.append(event)
    
    return formatted_events


def get_workflow_summary(events: List[Dict[str, Any]]) -> str:
    """
    Generate a human-readable summary from workflow events.
    
    Args:
        events: List of parsed workflow events
        
    Returns:
        Summary message string
    """
    if not events:
        return "No workflow events recorded"
    
    total_nodes = len(events)
    completed_nodes = sum(1 for e in events if e.get("node_status") == "node.completed")
    failed_nodes = sum(1 for e in events if e.get("node_status") == "node.failed")
    
    # Check final workflow status from last event
    last_event = events[-1]
    workflow_status = last_event.get("workflow_status", "unknown")
    
    if workflow_status == "completed":
        return f"Workflow completed successfully. Processed {total_nodes} nodes ({completed_nodes} completed, {failed_nodes} failed)"
    elif workflow_status == "failed":
        return f"Workflow failed. Processed {total_nodes} nodes ({completed_nodes} completed, {failed_nodes} failed)"
    else:
        return f"Workflow status: {workflow_status}. Processed {total_nodes} nodes"

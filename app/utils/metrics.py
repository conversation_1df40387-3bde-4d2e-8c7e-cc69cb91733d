from opentelemetry import metrics
from typing import Dict, Any, Optional


class MetricsManager:
    """Centralized metrics management for SigNoz OTEL"""

    def __init__(self):
        self.meter = metrics.get_meter(__name__)

        # Agent execution metrics
        self.agent_executions = self.meter.create_counter(
            name="agent.executions",
            description="Total agent executions",
            unit="1",
        )

        self.agent_execution_duration = self.meter.create_histogram(
            name="agent.execution.duration",
            description="Agent execution duration",
            unit="ms",
        )

        self.agent_errors = self.meter.create_counter(
            name="agent.errors",
            description="Total agent errors",
            unit="1",
        )

        # Tool usage metrics
        self.tool_calls = self.meter.create_counter(
            name="tool.calls",
            description="Total tool calls",
            unit="1",
        )

        self.tool_execution_duration = self.meter.create_histogram(
            name="tool.execution.duration",
            description="Tool execution duration",
            unit="ms",
        )

        self.tool_errors = self.meter.create_counter(
            name="tool.errors",
            description="Total tool errors",
            unit="1",
        )

        # API metrics
        self.api_calls = self.meter.create_counter(
            name="external_api.calls",
            description="External API calls",
            unit="1",
        )

        self.api_duration = self.meter.create_histogram(
            name="external_api.duration",
            description="External API call duration",
            unit="ms",
        )

        self.api_errors = self.meter.create_counter(
            name="external_api.errors",
            description="External API call errors",
            unit="1",
        )

        # Memory/Cache metrics
        self.memory_operations = self.meter.create_counter(
            name="memory.operations",
            description="Memory/Cache operations",
            unit="1",
        )

        self.memory_duration = self.meter.create_histogram(
            name="memory.operation.duration",
            description="Memory operation duration",
            unit="ms",
        )

        # Workflow metrics
        self.workflow_executions = self.meter.create_counter(
            name="workflow.executions",
            description="Total workflow executions",
            unit="1",
        )

        self.workflow_execution_duration = self.meter.create_histogram(
            name="workflow.execution.duration",
            description="Workflow execution duration",
            unit="ms",
        )

        self.workflow_errors = self.meter.create_counter(
            name="workflow.errors",
            description="Total workflow errors",
            unit="1",
        )

        # Exception metrics
        self.exceptions = self.meter.create_counter(
            name="exceptions.occurred",
            description="Exceptions occurred",
            unit="1",
        )

    # --------- Agent Metrics ---------
    def record_agent_execution(
        self, duration_ms: float, attributes: Optional[Dict[str, Any]] = None
    ):
        """Record agent execution"""
        attrs = attributes or {}
        self.agent_executions.add(1, attrs)
        self.agent_execution_duration.record(duration_ms, attrs)

    def record_agent_error(
        self,
        error_type: str,
        attributes: Optional[Dict[str, Any]] = None,
    ):
        """Record agent error"""
        attrs = {"error_type": error_type}
        if attributes:
            attrs.update(attributes)
        self.agent_errors.add(1, attrs)

    # --------- Tool Metrics ---------
    def record_tool_call(
        self, tool_name: str, attributes: Optional[Dict[str, Any]] = None
    ):
        """Record tool call"""
        attrs = {"tool_name": tool_name}
        if attributes:
            attrs.update(attributes)
        self.tool_calls.add(1, attrs)

    def record_tool_execution(
        self,
        tool_name: str,
        duration_ms: float,
        attributes: Optional[Dict[str, Any]] = None,
    ):
        """Record tool execution with duration"""
        attrs = {"tool_name": tool_name}
        if attributes:
            attrs.update(attributes)
        self.tool_execution_duration.record(duration_ms, attrs)

    def record_tool_error(
        self,
        tool_name: str,
        error_type: str,
        attributes: Optional[Dict[str, Any]] = None,
    ):
        """Record tool error"""
        attrs = {"tool_name": tool_name, "error_type": error_type}
        if attributes:
            attrs.update(attributes)
        self.tool_errors.add(1, attrs)

    # --------- API Metrics ---------
    def record_external_api_call(
        self,
        api_name: str,
        endpoint: str,
        attributes: Optional[Dict[str, Any]] = None,
    ):
        """Record external API call"""
        attrs = {"api_name": api_name, "endpoint": endpoint}
        if attributes:
            attrs.update(attributes)
        self.api_calls.add(1, attrs)

    def record_external_api_duration(
        self,
        duration_ms: float,
        api_name: str,
        endpoint: str,
        attributes: Optional[Dict[str, Any]] = None,
    ):
        """Record external API call duration"""
        attrs = {"api_name": api_name, "endpoint": endpoint}
        if attributes:
            attrs.update(attributes)
        self.api_duration.record(duration_ms, attrs)

    def record_external_api_error(
        self,
        api_name: str,
        endpoint: str,
        error_type: str,
        attributes: Optional[Dict[str, Any]] = None,
    ):
        """Record external API error"""
        attrs = {"api_name": api_name, "endpoint": endpoint, "error_type": error_type}
        if attributes:
            attrs.update(attributes)
        self.api_errors.add(1, attrs)

    # --------- Memory/Cache Metrics ---------
    def record_memory_operation(
        self,
        operation: str,
        duration_ms: float,
        attributes: Optional[Dict[str, Any]] = None,
    ):
        """Record memory/cache operation"""
        attrs = {"operation": operation}
        if attributes:
            attrs.update(attributes)
        self.memory_operations.add(1, attrs)
        self.memory_duration.record(duration_ms, attrs)

    # --------- Workflow Metrics ---------
    def record_workflow_execution(
        self, duration_ms: float, attributes: Optional[Dict[str, Any]] = None
    ):
        """Record workflow execution"""
        attrs = attributes or {}
        self.workflow_executions.add(1, attrs)
        self.workflow_execution_duration.record(duration_ms, attrs)

    def record_workflow_error(
        self,
        error_type: str,
        attributes: Optional[Dict[str, Any]] = None,
    ):
        """Record workflow error"""
        attrs = {"error_type": error_type}
        if attributes:
            attrs.update(attributes)
        self.workflow_errors.add(1, attrs)

    # --------- Exception Metrics ---------
    def record_exception(
        self,
        exception_type: str,
        severity: str = "error",
        attributes: Optional[Dict[str, Any]] = None,
    ):
        """Record exception occurrence"""
        attrs = {"exception_type": exception_type, "severity": severity}
        if attributes:
            attrs.update(attributes)
        self.exceptions.add(1, attrs)


# ------------------------------------------------
# Global Singleton Accessor
# ------------------------------------------------
_metrics_manager: Optional[MetricsManager] = None


def get_metrics_manager() -> MetricsManager:
    """Get or create a global metrics manager"""
    global _metrics_manager
    if _metrics_manager is None:
        _metrics_manager = MetricsManager()
    return _metrics_manager


def initialize_metrics() -> MetricsManager:
    """Initialize and return metrics manager"""
    return get_metrics_manager()

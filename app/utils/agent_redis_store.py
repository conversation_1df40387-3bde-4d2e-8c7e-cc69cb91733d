"""
Redis utility functions for storing agent runtime data (sources, approvals).

This module provides helper functions to store and retrieve temporary agent data
like sources and approval information in Redis instead of the agent state.
"""

import logging
import json
from typing import Optional, Dict, Any, List
from app.services.redis_streams import get_redis_manager

logger = logging.getLogger(__name__)

# Redis key prefixes
SOURCES_PREFIX = "agent:sources:"
APPROVAL_PREFIX = "agent:approval:"

# TTL for temporary data (30 minutes)
DEFAULT_TTL = 1800


def _is_duplicate_source(source1: Dict[str, Any], source2: Dict[str, Any]) -> bool:
    """
    Check if two sources are duplicates by comparing URLs only.
    
    Args:
        source1: First source dictionary
        source2: Second source dictionary
        
    Returns:
        True if sources are duplicates (same URL), False otherwise
    """
    # Only compare URLs - if both have URLs and they match, it's a duplicate
    if 'url' in source1 and 'url' in source2:
        return source1['url'] == source2['url']
    
    # If either doesn't have a URL, consider them as different sources
    return False


async def store_sources(conversation_id: str, source_type: str, sources: List[Dict[str, Any]]) -> None:
    """
    Store sources in Redis for a conversation.
    
    Args:
        conversation_id: The conversation identifier
        source_type: Type of source (e.g., 'web', 'knowledge_base')
        sources: List of source dictionaries
    """
    try:
        redis_manager = await get_redis_manager()
        redis_client = redis_manager.redis_client
        
        key = f"{SOURCES_PREFIX}{conversation_id}"
        
        # Get existing sources or initialize empty dict
        existing_data = await redis_client.get_value(key) or {}
        
        # Get existing sources for this type
        existing_sources = existing_data.get(source_type, [])
        
        # Filter out duplicates by comparing source content
        new_sources_added = 0
        for source in sources:
            # Check if this source already exists
            is_duplicate = False
            for existing_source in existing_sources:
                if _is_duplicate_source(source, existing_source):
                    is_duplicate = True
                    break
            
            # Only add if not a duplicate
            if not is_duplicate:
                existing_sources.append(source)
                new_sources_added += 1
        
        # Update the sources for this type
        existing_data[source_type] = existing_sources
        
        # Store back in Redis with TTL
        await redis_client.set_value(key, existing_data, ttl=DEFAULT_TTL)
        
        logger.info(
            f"Stored {new_sources_added} new {source_type} sources for conversation {conversation_id} "
            f"({len(sources) - new_sources_added} duplicates skipped)"
        )
    except Exception as e:
        logger.error(f"Failed to store sources in Redis for conversation {conversation_id}: {e}")
        # Don't raise - this is non-critical


async def get_sources(conversation_id: str) -> Dict[str, List[Dict[str, Any]]]:
    """
    Retrieve all sources for a conversation from Redis.
    
    Args:
        conversation_id: The conversation identifier
        
    Returns:
        Dictionary mapping source types to lists of sources
    """
    try:
        redis_manager = await get_redis_manager()
        redis_client = redis_manager.redis_client
        
        key = f"{SOURCES_PREFIX}{conversation_id}"
        sources = await redis_client.get_value(key)
        
        if sources:
            logger.info(f"Retrieved sources for conversation {conversation_id}: {list(sources.keys())}")
            return sources
        
        return {}
    except Exception as e:
        logger.error(f"Failed to get sources from Redis for conversation {conversation_id}: {e}")
        return {}


async def clear_sources(conversation_id: str) -> None:
    """
    Clear all sources for a conversation from Redis.
    
    Args:
        conversation_id: The conversation identifier
    """
    try:
        redis_manager = await get_redis_manager()
        redis_client = redis_manager.redis_client
        
        key = f"{SOURCES_PREFIX}{conversation_id}"
        await redis_client.delete(key)
        
        logger.info(f"Cleared sources for conversation {conversation_id}")
    except Exception as e:
        logger.error(f"Failed to clear sources from Redis for conversation {conversation_id}: {e}")


async def store_approval_data(
    conversation_id: str,
    approval_type: str,
    approval_data: Dict[str, Any]
) -> None:
    """
    Store approval data in Redis for a conversation.
    
    Args:
        conversation_id: The conversation identifier
        approval_type: Type of approval (e.g., 'mcp_search', 'resolve_identity')
        approval_data: Approval data dictionary
    """
    try:
        redis_manager = await get_redis_manager()
        redis_client = redis_manager.redis_client
        
        key = f"{APPROVAL_PREFIX}{conversation_id}"
        
        data = {
            "requires_approval": True,
            "approval_type": approval_type,
            "approval_data": approval_data
        }
        
        # Store in Redis with TTL
        await redis_client.set_value(key, data, ttl=DEFAULT_TTL)
        
        logger.info(f"Stored approval data for conversation {conversation_id}, type: {approval_type}")
    except Exception as e:
        logger.error(f"Failed to store approval data in Redis for conversation {conversation_id}: {e}")
        # Don't raise - this is non-critical


async def get_approval_data(conversation_id: str) -> Optional[Dict[str, Any]]:
    """
    Retrieve approval data for a conversation from Redis.
    
    Args:
        conversation_id: The conversation identifier
        
    Returns:
        Dictionary with approval data or None if not found
    """
    try:
        redis_manager = await get_redis_manager()
        redis_client = redis_manager.redis_client
        
        key = f"{APPROVAL_PREFIX}{conversation_id}"
        approval_data = await redis_client.get_value(key)
        
        if approval_data:
            logger.info(f"Retrieved approval data for conversation {conversation_id}")
            return approval_data
        
        return None
    except Exception as e:
        logger.error(f"Failed to get approval data from Redis for conversation {conversation_id}: {e}")
        return None


async def clear_approval_data(conversation_id: str) -> None:
    """
    Clear approval data for a conversation from Redis.
    
    Args:
        conversation_id: The conversation identifier
    """
    try:
        redis_manager = await get_redis_manager()
        redis_client = redis_manager.redis_client
        
        key = f"{APPROVAL_PREFIX}{conversation_id}"
        await redis_client.delete(key)
        
        logger.info(f"Cleared approval data for conversation {conversation_id}")
    except Exception as e:
        logger.error(f"Failed to clear approval data from Redis for conversation {conversation_id}: {e}")

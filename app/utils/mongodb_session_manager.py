import logging
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional

from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class MongoDBSessionManager:
    """
    A session manager that uses MongoDB to store and retrieve agent sessions.

    Each session contains:
    - session_id: Unique identifier for the session
    - messages: List of message objects (role, content)
    - created_at: Timestamp when session was created
    - updated_at: Timestamp when session was last updated
    - metadata: Additional session metadata
    """

    def __init__(
        self,
        mongo_uri: str,
        db_name: str,
        collection_name: str,
        timeout_ms: int = 5000,
    ):
        """
        Initialize the MongoDB session manager.

        Args:
            mongo_uri: MongoDB connection URI
            db_name: Database name
            collection_name: Collection name for sessions
            timeout_ms: Connection timeout in milliseconds
        """
        self.mongo_uri = mongo_uri
        self.db_name = db_name
        self.collection_name = collection_name
        self.timeout_ms = timeout_ms
        self.client: Optional[MongoClient] = None
        self.db = None
        self.collection = None

        self._connect()
        self._ensure_indexes()

    def _connect(self) -> None:
        """Establish connection to MongoDB."""
        try:
            self.client = MongoClient(
                self.mongo_uri
            )
            # Verify connection
            self.client.admin.command("ping")
            self.db = self.client[self.db_name]
            self.collection = self.db[self.collection_name]
            logger.info(
                f"Successfully connected to MongoDB at {self.mongo_uri}"
            )
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            raise

    def _ensure_indexes(self) -> None:
        """Create necessary indexes for efficient queries."""
        try:
            # Create unique index on session_id
            self.collection.create_index("session_id", unique=True)
            # Create index on created_at for sorting
            self.collection.create_index("created_at")
            # Create index on updated_at for sorting
            self.collection.create_index("updated_at")
            logger.info("Indexes created successfully")
        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
            raise

    def create_session(
        self,
        session_id: str,
        messages: Optional[List[Dict[str, Any]]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Create a new session.

        Args:
            session_id: Unique session identifier
            messages: Initial messages list (default: empty list)
            metadata: Additional session metadata

        Returns:
            The created session document

        Raises:
            ValueError: If session already exists
        """
        if self.get_session(session_id) is not None:
            raise ValueError(f"Session with id '{session_id}' already exists")

        now = datetime.now(timezone.utc)
        session_doc = {
            "session_id": session_id,
            "messages": messages or [],
            "metadata": metadata or {},
            "created_at": now,
            "updated_at": now,
        }

        try:
            result = self.collection.insert_one(session_doc)
            logger.info(f"Session '{session_id}' created successfully")
            return session_doc
        except Exception as e:
            logger.error(f"Failed to create session '{session_id}': {e}")
            raise

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve a session by session_id.

        Args:
            session_id: The session identifier

        Returns:
            Session document or None if not found
        """
        try:
            session = self.collection.find_one({"session_id": session_id})
            if session:
                # Remove MongoDB's internal _id field for cleaner output
                session.pop("_id", None)
            return session
        except Exception as e:
            logger.error(f"Failed to retrieve session '{session_id}': {e}")
            raise

    def update_session(
        self,
        session_id: str,
        messages: Optional[List[Dict[str, Any]]] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        Update an existing session.

        Args:
            session_id: The session identifier
            messages: Updated messages list
            metadata: Updated metadata

        Returns:
            Updated session document or None if not found
        """
        session = self.get_session(session_id)
        if session is None:
            logger.warning(f"Session '{session_id}' not found")
            return None

        update_doc = {"updated_at": datetime.now(timezone.utc)}

        if messages is not None:
            update_doc["messages"] = messages

        if metadata is not None:
            # Merge metadata with existing metadata
            update_doc["metadata"] = {**session.get("metadata", {}), **metadata}

        try:
            self.collection.update_one(
                {"session_id": session_id},
                {"$set": update_doc},
            )
            logger.info(f"Session '{session_id}' updated successfully")
            return self.get_session(session_id)
        except Exception as e:
            logger.error(f"Failed to update session '{session_id}': {e}")
            raise

    def add_message(
        self,
        session_id: str,
        message: Dict[str, Any],
    ) -> Optional[Dict[str, Any]]:
        """
        Add a single message to a session.

        Args:
            session_id: The session identifier
            message: Message object with 'role' and 'content'

        Returns:
            Updated session document or None if not found
        """
        session = self.get_session(session_id)
        if session is None:
            logger.warning(f"Session '{session_id}' not found")
            return None

        try:
            self.collection.update_one(
                {"session_id": session_id},
                {
                    "$push": {"messages": message},
                    "$set": {"updated_at": datetime.now(timezone.utc)},
                },
            )
            logger.info(f"Message added to session '{session_id}'")
            return self.get_session(session_id)
        except Exception as e:
            logger.error(f"Failed to add message to session '{session_id}': {e}")
            raise

    def get_messages(self, session_id: str) -> Optional[List[Dict[str, Any]]]:
        """
        Retrieve all messages from a session.

        Args:
            session_id: The session identifier

        Returns:
            List of messages or None if session not found
        """
        session = self.get_session(session_id)
        if session is None:
            logger.warning(f"Session '{session_id}' not found")
            return None

        return session.get("messages", [])

    def delete_session(self, session_id: str) -> bool:
        """
        Delete a session.

        Args:
            session_id: The session identifier

        Returns:
            True if session was deleted, False if not found
        """
        try:
            result = self.collection.delete_one({"session_id": session_id})
            if result.deleted_count > 0:
                logger.info(f"Session '{session_id}' deleted successfully")
                return True
            else:
                logger.warning(f"Session '{session_id}' not found")
                return False
        except Exception as e:
            logger.error(f"Failed to delete session '{session_id}': {e}")
            raise

    def list_sessions(
        self,
        limit: int = 100,
        skip: int = 0,
    ) -> List[Dict[str, Any]]:
        """
        List all sessions with pagination.

        Args:
            limit: Maximum number of sessions to return
            skip: Number of sessions to skip

        Returns:
            List of session documents
        """
        try:
            sessions = list(
                self.collection.find()
                .sort("updated_at", -1)
                .skip(skip)
                .limit(limit)
            )
            # Remove MongoDB's internal _id field
            for session in sessions:
                session.pop("_id", None)
            return sessions
        except Exception as e:
            logger.error(f"Failed to list sessions: {e}")
            raise

    def clear_all_sessions(self) -> int:
        """
        Delete all sessions (use with caution).

        Returns:
            Number of sessions deleted
        """
        try:
            result = self.collection.delete_many({})
            logger.warning(f"Deleted {result.deleted_count} sessions")
            return result.deleted_count
        except Exception as e:
            logger.error(f"Failed to clear all sessions: {e}")
            raise

    def close(self) -> None:
        """Close the MongoDB connection."""
        if self.client:
            self.client.close()
            logger.info("MongoDB connection closed")

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close()

from dotenv import load_dotenv
from opentelemetry import trace, metrics
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.metrics import MeterProvider
from opentelemetry.sdk.resources import Resource
from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter
from opentelemetry.sdk.trace.export import BatchSpanProcessor
from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
from opentelemetry.trace import Status, StatusCode
from contextlib import contextmanager
from typing import Optional, Dict, Any
import os
import logging

# Import OpenTelemetry logging components
from opentelemetry.sdk._logs import LoggerProvider, LoggingHandler
from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
from opentelemetry.exporter.otlp.proto.grpc._log_exporter import OTLPLogExporter
from opentelemetry._logs import set_logger_provider

# Import HTTP client instrumentation
from opentelemetry.instrumentation.httpx import HTTPXClientInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.instrumentation.pymongo import PymongoInstrumentor

load_dotenv()


def setup_telemetry():
    """
    Initialize OpenTelemetry with SigNoz for traces, metrics, and logs.

    This function sets up:
    1. Traces - Distributed tracing with automatic span creation
    2. Metrics - Performance metrics exported periodically
    3. Logs - Structured logs with trace correlation automatically sent to SigNoz

    All logs will be exported to SigNoz and automatically correlated with traces
    using trace_id and span_id when logging occurs within a traced operation.
    """

    if not os.getenv("OTEL_ENABLED", "false").lower() == "true":
        return

    # Create resource (identifies your service)
    resource = Resource.create(
        {
            "service.name": os.getenv("OTEL_SERVICE_NAME", "agent-platform"),
            "service.version": os.getenv("OTEL_SERVICE_VERSION", "0.1.0"),
            "deployment.environment": os.getenv("OTEL_ENVIRONMENT", "development"),
        }
    )

    endpoint = os.getenv("SIGNOZ_ENDPOINT", "http://localhost:4317")
    insecure = os.getenv("SIGNOZ_INSECURE", "true").lower() == "true"

    # Setup Tracing
    trace_provider = TracerProvider(resource=resource)
    trace_exporter = OTLPSpanExporter(
        endpoint=endpoint,
        insecure=insecure,
    )
    trace_provider.add_span_processor(BatchSpanProcessor(trace_exporter))
    trace.set_tracer_provider(trace_provider)

    # Setup Metrics
    metric_reader = PeriodicExportingMetricReader(
        OTLPMetricExporter(
            endpoint=endpoint,
            insecure=insecure,
        ),
        export_interval_millis=60000,  # Export every 60 seconds
    )

    meter_provider = MeterProvider(
        resource=resource,
        metric_readers=[metric_reader],
    )
    metrics.set_meter_provider(meter_provider)

    # Setup Logging with OTLP export
    logger_provider = LoggerProvider(resource=resource)
    log_exporter = OTLPLogExporter(
        endpoint=endpoint,
        insecure=insecure,
    )
    logger_provider.add_log_record_processor(BatchLogRecordProcessor(log_exporter))
    set_logger_provider(logger_provider)

    # Attach OTEL handler to root logger to capture all logs
    otel_handler = LoggingHandler(
        level=logging.NOTSET,  # Capture all log levels
        logger_provider=logger_provider,
    )

    # Get root logger and add the OTEL handler
    root_logger = logging.getLogger()
    root_logger.addHandler(otel_handler)

    # Setup HTTP client instrumentation for external API tracking
    # This will automatically instrument httpx and requests clients
    # Note: aiohttp sessions created in code will be manually instrumented with trace_operation
    HTTPXClientInstrumentor().instrument()
    RequestsInstrumentor().instrument()

    # Setup MongoDB instrumentation for database tracking
    PymongoInstrumentor().instrument()

    print(f"✅ OpenTelemetry initialized for {resource.attributes['service.name']}")
    print(f"   📊 Traces, Metrics, and Logs exporting to: {endpoint}")
    print(f"   🔗 Logs will be automatically correlated with traces")
    print(
        f"   🌐 HTTP clients (httpx, requests) instrumented for external API tracking"
    )
    print(f"   🗄️  MongoDB client instrumented for database tracking")


class ExceptionTracker:
    """Tracks and records exceptions with context for SigNoz"""

    def __init__(self):
        self.tracer = trace.get_tracer(__name__)
        self.meter = metrics.get_meter(__name__)
        self.exception_counter = self.meter.create_counter(
            "exception.occurred",
            description="Count of exceptions that occurred",
            unit="1",
        )

    def record_exception(
        self,
        exception: Exception,
        context: Optional[Dict[str, Any]] = None,
        severity: str = "error",
        user_id: Optional[str] = None,
        operation: Optional[str] = None,
    ) -> None:
        """
        Record an exception with full context for tracing and metrics.

        Args:
            exception: The exception that occurred
            context: Additional context dictionary
            severity: Severity level (error, warning, critical)
            user_id: User ID associated with the exception
            operation: Operation name where exception occurred
        """
        current_span = trace.get_current_span()

        # Set exception on current span
        if current_span.is_recording():
            current_span.record_exception(exception)
            current_span.set_status(Status(StatusCode.ERROR, str(exception)))

        # Set attributes
        attributes = {
            "exception.type": type(exception).__name__,
            "exception.message": str(exception),
            "severity": severity,
        }

        if user_id:
            attributes["user_id"] = user_id

        if operation:
            attributes["operation"] = operation

        if context:
            for key, value in context.items():
                attributes[f"context.{key}"] = str(value)

        # Record metrics
        self.exception_counter.add(1, attributes=attributes)

        # Set span attributes if recording
        if current_span.is_recording():
            for key, value in attributes.items():
                current_span.set_attribute(key, value)


@contextmanager
def trace_operation(
    operation_name: str,
    attributes: Optional[Dict[str, Any]] = None,
):
    """
    Context manager for tracing an operation with attributes.

    Args:
        operation_name: Name of the operation
        attributes: Optional dictionary of attributes to add to the span
    """
    tracer = trace.get_tracer(__name__)

    with tracer.start_as_current_span(operation_name) as span:
        if attributes:
            for key, value in attributes.items():
                span.set_attribute(key, value)

        yield span


def get_exception_tracker() -> ExceptionTracker:
    """Get or create the exception tracker singleton"""
    if not hasattr(get_exception_tracker, "_instance"):
        get_exception_tracker._instance = ExceptionTracker()
    return get_exception_tracker._instance

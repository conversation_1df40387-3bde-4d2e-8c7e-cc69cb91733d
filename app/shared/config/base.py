import os
from dataclasses import dataclass
from functools import lru_cache

from dotenv import load_dotenv


@dataclass
class APIConfig:
    host: str = "0.0.0.0"
    port: int = 6000


@dataclass
class OpenAIConfig:
    api_key: str
    model: str = "gpt-4o-mini"


@dataclass
class AgentProviderConfig:
    default_agent_provider: str


@dataclass
class RequestyConfig:
    api_key: str
    model: str = "gpt-4o-mini"
    base_url: str = "https://router.requesty.ai/v1"


@dataclass
class OpenRouterConfig:
    api_key: str
    base_url: str = "https://openrouter.ai/api/v1"
    ai_gateway_api_key: str = ""  # Server authentication key for AI Gateway
    site_url: str = ""  # Optional: Site URL for rankings on openrouter.ai
    site_name: str = ""  # Optional: Site name for rankings on openrouter.ai


@dataclass
class ApiGatewayConfig:
    api_url: str
    api_key: str
    organization_key: str


@dataclass
class MCPConfig:
    gateway_url: str
    api_key: str


@dataclass
class WorkflowApiGatewayConfig:
    api_url: str
    api_key: str


@dataclass
class ExternalApiGatewayConfig:
    api_url: str
    token: str


@dataclass
class GitConfig:
    repo_url: str
    token: str
    branch: str


@dataclass
class KafkaConfig:
    kafka_bootstrap_servers: str
    kafka_agent_creation_topic: str
    kafka_agent_chat_topic: str
    kafka_agent_response_topic: str
    kafka_consumer_group: str
    kafka_agent_query_topic: str
    kafka_agent_message_topic: str
    kafka_agent_session_deletion_topic: str
    kafka_orchestration_team_session_topic: str
    kafka_orchestration_team_chat_topic: str
    kafka_human_input_request_topic: str
    kafka_human_input_response_topic: str
    kafka_agent_chat_stop_topic: str
    kafka_token_usage_topic: str


@dataclass
class RedisConfig:
    redis_host: str
    redis_port: int
    redis_db: int
    password: str | None = None
    consumer_batch_size: int = 10


@dataclass
class AgentConfig:
    base_url: str
    auth_key: str
    test_user_id: str


@dataclass
class PineconeConfig:
    api_key: str
    environment: str = "us-east-1"
    index_name: str = "agent-memory"
    dimension: int = 1536  # Default for OpenAI embeddings
    metric: str = "cosine"
    cloud: str = "aws"


@dataclass
class QdrantConfig:
    host: str = "localhost"
    port: int = 6333
    collection_name: str = "agent_memory"
    vector_size: int = 1536  # Default for OpenAI text-embedding-3-small
    distance: str = "Cosine"
    api_key: str | None = None
    timeout: int = 60


@dataclass
class Mem0Config:
    embedding_model: str = "text-embedding-3-small"
    embedding_provider: str = "openai"
    vector_store_type: str = "qdrant"
    llm_provider: str = "openai"
    llm_model: str = "gpt-4o-mini"
    memory_decay_rate: float = 0.01
    max_memories: int = 10000
    min_relevance_score: float = 0.2


@dataclass
class MongoDBConfig:
    url: str


@dataclass
class SummarisationConfig:
    provider: str = "google"
    model: str = "gemini-2.5-flash"


@dataclass
class TelemetryConfig:
    enabled: bool = False
    service_name: str = "agent-platform"
    service_version: str = "1.0.0"
    environment: str = "development"
    signoz_endpoint: str = "http://localhost:4317"
    signoz_insecure: bool = True


@dataclass
class Settings:
    """Main configuration class that holds all config sections"""

    api: APIConfig
    openai: OpenAIConfig
    agent_provider: AgentProviderConfig
    requesty: RequestyConfig
    openrouter: OpenRouterConfig
    git: GitConfig
    agent: AgentConfig
    kafka: KafkaConfig
    redis: RedisConfig
    gateway: ApiGatewayConfig
    workflow_api_gateway: WorkflowApiGatewayConfig
    external_api_gateway: ExternalApiGatewayConfig
    mcp: MCPConfig
    pinecone: PineconeConfig
    qdrant: QdrantConfig
    mem0: Mem0Config
    mongodb: MongoDBConfig
    summarisation: SummarisationConfig
    telemetry: TelemetryConfig
    environment: str = "development"
    ORCHESTRATION_TEAM_CHAT_MODEL_ID: str = ""
    # Model provider selection
    model_provider: str = "requesty"  # Options: "requesty", "openrouter", "openai"
    # Knowledge base configuration
    knowledge_base_top_k: int = 10
    # Knowledge base MCP configuration
    kb_mcp: str = "context_engine_mcp"
    # Agent gateway configuration
    agent_gateway_url: str = ""
    agent_gateway_api_key: str = ""


# Add a function to clear the cache
def clear_settings_cache():
    """Clear the settings cache to force reload from environment variables"""
    get_settings.cache_clear()


@lru_cache()
def get_settings(force_reload=False) -> Settings:
    """
    Creates and returns a cached instance of Settings.
    Uses environment variables with fallbacks to default values.

    Args:
        force_reload: If True, will reload .env file (useful for testing)
    """
    # Force reload is just a dummy parameter to invalidate the cache when needed
    load_dotenv(override=True)  # Add override=True to ensure values are refreshed

    return Settings(
        api=APIConfig(
            host=os.getenv("API_HOST", "0.0.0.0"), port=int(os.getenv("API_PORT", 6000))
        ),
        openai=OpenAIConfig(
            api_key=os.getenv("OPENAI_API_KEY", ""),
            model=os.getenv("LLM_MODEL", "gpt-4o-mini"),
        ),
        requesty=RequestyConfig(
            api_key=os.getenv("REQUESTY_API_KEY", ""),
            model=os.getenv("LLM_MODEL", "gpt-4o-mini"),
            base_url=os.getenv("REQUESTY_BASE_URL", "https://router.requesty.ai/v1"),
        ),
        openrouter=OpenRouterConfig(
            api_key=os.getenv("OPENROUTER_API_KEY", ""),
            base_url=os.getenv("AI_GATEWAY_BASE_URL", "https://openrouter.ai/api/v1"),
            ai_gateway_api_key=os.getenv("AI_GATEWAY_API_KEY", ""),
            site_url=os.getenv("OPENROUTER_SITE_URL", ""),
            site_name=os.getenv("OPENROUTER_SITE_NAME", ""),
        ),
        git=GitConfig(
            repo_url=os.getenv("REPO_URL", ""),
            token=os.getenv("GIT_TOKEN", ""),
            branch=os.getenv("ENV", "development"),
        ),
        agent=AgentConfig(
            base_url=os.getenv("AGENT_API_BASE_URL", ""),
            auth_key=os.getenv("AGENT_API_AUTH_KEY", ""),
            test_user_id=os.getenv("Test_User_Id", ""),
        ),
        agent_provider=AgentProviderConfig(
            default_agent_provider=os.getenv("DEFAULT_AGENT_PROVIDER", "strands"),
        ),
        kafka=KafkaConfig(
            kafka_bootstrap_servers=os.getenv("KAFKA_BOOTSTRAP_SERVERS", ""),
            kafka_agent_creation_topic=os.getenv("KAFKA_AGENT_CREATION_TOPIC", ""),
            kafka_agent_chat_topic=os.getenv("KAFKA_AGENT_CHAT_TOPIC", ""),
            kafka_agent_response_topic=os.getenv("KAFKA_AGENT_RESPONSE_TOPIC", ""),
            kafka_consumer_group=os.getenv("KAFKA_CONSUMER_GROUP", ""),
            kafka_agent_query_topic=os.getenv("KAFKA_AGENT_QUERY_TOPIC", ""),
            kafka_agent_message_topic=os.getenv("KAFKA_AGENT_MESSAGE_TOPIC", ""),
            kafka_agent_session_deletion_topic=os.getenv(
                "KAFKA_AGENT_SESSION_DELETION_TOPIC", ""
            ),
            kafka_orchestration_team_session_topic=os.getenv(
                "KAFKA_ORCHESTRATION_TEAM_SESSION_TOPIC",
                "orchestration_team_session_requests",
            ),
            kafka_orchestration_team_chat_topic=os.getenv(
                "KAFKA_ORCHESTRATION_TEAM_CHAT_TOPIC",
                "orchestration_team_chat_requests",
            ),
            kafka_human_input_request_topic=os.getenv(
                "KAFKA_HUMAN_INPUT_REQUEST_TOPIC", "human_input_requests"
            ),
            kafka_human_input_response_topic=os.getenv(
                "KAFKA_HUMAN_INPUT_RESPONSE_TOPIC", "human_input_responses"
            ),
            kafka_agent_chat_stop_topic=os.getenv(
                "KAFKA_AGENT_CHAT_STOP_TOPIC", "agent_chat_stop_requests"
            ),
            kafka_token_usage_topic=os.getenv(
                "KAFKA_TOKEN_USAGE_TOPIC", "token-usage-events"
            ),
        ),
        redis=RedisConfig(
            redis_host=os.getenv("REDIS_HOST", ""),
            redis_port=int(os.getenv("REDIS_PORT", 6379)),
            redis_db=int(os.getenv("REDIS_DB", 0)),
            password=os.getenv("REDIS_PASSWORD", None),
            consumer_batch_size=int(os.getenv("REDIS_CONSUMER_BATCH_SIZE", 10)),
        ),
        gateway=ApiGatewayConfig(
            api_url=os.getenv("API_GATEWAY_URL", ""),
            api_key=os.getenv("API_GATEWAY_KEY", ""),
            organization_key=os.getenv("API_GATEWAY_ORGANIZATION_KEY", ""),
        ),
        workflow_api_gateway=WorkflowApiGatewayConfig(
            api_url=os.getenv("WORKFLOW_API_GATEWAY_URL", ""),
            api_key=os.getenv("WORKFLOW_API_GATEWAY_KEY", ""),
        ),
        external_api_gateway=ExternalApiGatewayConfig(
            api_url=os.getenv("EXTERNAL_API_GATEWAY", ""),
            token=os.getenv("EXTERNAL_API_GATEWAY_TOKEN", ""),
        ),
        mcp=MCPConfig(
            gateway_url=os.getenv("MCP_GATEWAY_URL", ""),
            api_key=os.getenv("MCP_API_KEY", ""),
        ),
        pinecone=PineconeConfig(
            api_key=os.getenv("PINECONE_API_KEY", ""),
            environment=os.getenv("PINECONE_ENVIRONMENT", "us-east-1"),
            index_name=os.getenv("PINECONE_INDEX_NAME", "agent-memory"),
            dimension=int(os.getenv("PINECONE_DIMENSION", "1536")),
            metric=os.getenv("PINECONE_METRIC", "cosine"),
            cloud=os.getenv("PINECONE_CLOUD", "aws"),
        ),
        qdrant=QdrantConfig(
            host=os.getenv("QDRANT_HOST", "localhost"),
            port=int(os.getenv("QDRANT_PORT", "6333")),
            collection_name=os.getenv("QDRANT_COLLECTION_NAME", "agent_memory"),
            vector_size=int(os.getenv("QDRANT_VECTOR_SIZE", "1536")),
            distance=os.getenv("QDRANT_DISTANCE", "Cosine"),
            api_key=os.getenv("QDRANT_API_KEY", None),
            timeout=int(os.getenv("QDRANT_TIMEOUT", "60")),
        ),
        mem0=Mem0Config(
            embedding_model=os.getenv("MEM0_EMBEDDING_MODEL", "text-embedding-3-small"),
            embedding_provider=os.getenv("MEM0_EMBEDDING_PROVIDER", "openai"),
            vector_store_type=os.getenv("MEM0_VECTOR_STORE_TYPE", "qdrant"),
            llm_provider=os.getenv("MEM0_LLM_PROVIDER", "openai"),
            llm_model=os.getenv("MEM0_LLM_MODEL", "gpt-5-mini"),
            memory_decay_rate=float(os.getenv("MEM0_MEMORY_DECAY_RATE", "0.01")),
            max_memories=int(os.getenv("MEM0_MAX_MEMORIES", "10000")),
            min_relevance_score=float(os.getenv("MEM0_MIN_RELEVANCE_SCORE", "0.2")),
        ),
        mongodb=MongoDBConfig(
            url=os.getenv("MONGO_DB_URL", ""),
        ),
        summarisation=SummarisationConfig(
            provider=os.getenv("SUMMARISATION_PROVIDER", "google"),
            model=os.getenv("SUMMARISATION_MODEL", "gemini-2.5-flash"),
        ),
        telemetry=TelemetryConfig(
            enabled=os.getenv("OTEL_ENABLED", "false").lower() == "true",
            service_name=os.getenv("OTEL_SERVICE_NAME", "agent-platform"),
            service_version=os.getenv("OTEL_SERVICE_VERSION", "1.0.0"),
            environment=os.getenv("OTEL_ENVIRONMENT", "development"),
            signoz_endpoint=os.getenv("SIGNOZ_ENDPOINT", "http://localhost:4317"),
            signoz_insecure=os.getenv("SIGNOZ_INSECURE", "true").lower() == "true",
        ),
        environment=os.getenv("ENV", "dev"),
        ORCHESTRATION_TEAM_CHAT_MODEL_ID=os.getenv(
            "ORCHESTRATION_TEAM_CHAT_MODEL_ID", ""
        ),
        model_provider=os.getenv("MODEL_PROVIDER", "requesty"),
        knowledge_base_top_k=int(os.getenv("KNOWLEDGE_BASE_TOP_K", "10")),
        kb_mcp=os.getenv("KB_MCP", "context_engine_mcp"),
        agent_gateway_url=os.getenv("AGENT_GATEWAY_URL", ""),
        agent_gateway_api_key=os.getenv("AGENT_GATEWAY_API_KEY", ""),
    )

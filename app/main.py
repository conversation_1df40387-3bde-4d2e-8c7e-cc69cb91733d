import asyncio
import atexit
import os

from apscheduler.schedulers.background import BackgroundScheduler

from app.agent.tools.memory_tools import initialize_memory
from app.services.mongodb_client import (
    initialize_mongodb_client,
    close_mongodb_client,
)
from app.services.redis_listener import listen_event_from_redis_pubsub
from app.services.redis_streams import initialize_redis_manager, close_redis_manager
from app.services.http_client import initialize_http_client, close_http_client
from app.services.agent_fetch import AgentFetchService
from app.shared.config.base import get_settings
from app.shared.config.logging_config import get_logger, setup_logging
from app.shared.config.telemetry import setup_telemetry
from app.utils.update_vector_db import main as update_vector_db
from app.utils.infrastructure import get_infrastructure_monitor
from app.utils.metrics import initialize_metrics
from app.utils.exceptions import get_exception_tracker

# Determine if we should use JSON logging format
use_json = os.getenv("LOG_FORMAT", "").lower() == "json"

# Set up logging before anything else
setup_logging(
    default_level=os.getenv("LOG_LEVEL", "DEBUG,INFO,ERROR"),
    logs_dir=os.getenv("LOGS_DIR", "logs"),
    use_json=use_json,
)

background_scheduler = BackgroundScheduler()
background_scheduler.add_job(update_vector_db, "cron", hour=0, minute=0)
background_scheduler.start()
atexit.register(lambda: background_scheduler.shutdown(wait=False))

# Get logger for this module
logger = get_logger(__name__)

# Get configuration
settings = get_settings()

setup_telemetry()

# Initialize metrics manager
logger.info("Initializing metrics manager")
initialize_metrics()
logger.info("Metrics manager initialized successfully")

# Log application startup
logger.info(
    "Application starting",
    extra={"environment": settings.environment},
)

# Initialize memory system at startup
logger.info("Initializing memory system (Mem0 + Qdrant)")
initialize_memory()
logger.info("Memory system initialized successfully")


async def initialize_services():
    """Initialize all services at startup"""
    logger.info("Initializing global HTTP client for LLM API calls")
    initialize_http_client()
    logger.info("HTTP client initialized successfully")

    logger.info("Initializing MongoDB client")
    initialize_mongodb_client()
    logger.info("MongoDB client initialized successfully")

    logger.info("Initializing Redis Streams Manager")
    await initialize_redis_manager()
    logger.info("Redis Streams Manager initialized successfully")

    logger.info("Initializing AgentFetchService singleton")
    AgentFetchService()
    logger.info("AgentFetchService singleton initialized successfully")

    logger.info("Starting infrastructure monitoring")
    monitor = get_infrastructure_monitor()
    await monitor.start_monitoring(interval=60)
    logger.info("✅ Infrastructure monitoring started")


async def shutdown_services():
    """Shutdown all services gracefully"""
    logger.info("Shutting down services...")
    
    logger.info("Stopping infrastructure monitoring")
    monitor = get_infrastructure_monitor()
    await monitor.stop_monitoring()
    logger.info("✅ Infrastructure monitoring stopped")
    
    await close_http_client()
    await close_redis_manager()
    close_mongodb_client()
    logger.info("All services shut down successfully")


def main():
    tracker = get_exception_tracker()
    try:
        logger.info("Starting application services")
        print("Starting background scheduler")

        # Run initialization and main loop
        asyncio.run(run_application())
    except KeyboardInterrupt:
        logger.warning("Shutting down due to keyboard interrupt")
    except Exception as e:
        # Track exception with Signoz OTEL
        tracker.track_exception(
            e,
            severity="error",
            attributes={
                "component": "application_main",
                "stage": "initialization",
            },
        )
        logger.error(
            "Error occurred in application", exc_info=True, extra={"error": str(e)}
        )


async def run_application():
    """Main application runner with proper initialization and cleanup"""
    # Initialize services
    await initialize_services()

    try:
        # Start the main Redis listener
        logger.info("Starting Redis listener")
        await listen_event_from_redis_pubsub()
    finally:
        # Cleanup on exit
        await shutdown_services()


if __name__ == "__main__":
    """
    Usage:
        To run the application:
        poetry run python -m app.main
    """
    main()

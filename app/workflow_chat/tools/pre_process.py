def pre_process(workflow: dict) -> dict:
    output = {"nodes": [], "edges": []}
    for node in workflow["nodes"]:
        node = _pre_process_node(node)
        output["nodes"].append(node)
    for edge in workflow["edges"]:
        edge = _pre_process_edge(edge)
        output["edges"].append(edge)
    if "unconnected_nodes" in workflow:
        for node in workflow["unconnected_nodes"]:
            node = _pre_process_node(node)
            output["unconnected_nodes"].append(node)
    return output


def _pre_process_node(node: dict) -> dict:
    if node["data"]["type"] == "mcp":
        return _pre_process_mcp_node(node)
    elif node["data"]["originalType"].startswith("workflow-"):
        return _pre_process_workflow_node(node)
    else:
        node = _pre_process_component_node(node)
        if node["OriginalType"] == "StartNode":
            node["parameters"] = {}
        return node


def _pre_process_mcp_node(node: dict) -> dict:
    return {
        "node_id": node["id"],
        "label": node["data"]["label"],
        "OriginalType": node["data"]["originalType"],
        "type": "mcp",
        "position": node["position"],
        "dimension": {"width": node["width"], "height": node["height"]},
        "parameters": node["data"]["config"],
        "mcp_id": node["data"]["definition"]["mcp_info"]["server_id"],
        "tool_name": node["data"]["definition"]["mcp_info"]["tool_name"],
    }


def _pre_process_workflow_node(node: dict) -> dict:
    return {
        "node_id": node["id"],
        "label": node["data"]["label"],
        "OriginalType": node["data"]["originalType"],
        "type": "workflow",
        "position": node["position"],
        "dimension": {"width": node["width"], "height": node["height"]},
        "parameters": node["data"]["config"],
        "workflow_id": node["data"]["definition"]["workflow_info"]["id"],
    }


def _pre_process_component_node(node: dict) -> dict:
    return {
        "node_id": node["id"],
        "label": node["data"]["label"],
        "OriginalType": node["data"]["originalType"],
        "type": "component",
        "position": node["position"],
        "dimension": {"width": node["width"], "height": node["height"]},
        "parameters": node["data"]["config"],
    }


def _pre_process_edge(edge: dict) -> dict:
    return {
        "source": edge["source"],
        "sourceHandle": edge["sourceHandle"],
        "target": edge["target"],
        "targetHandle": edge["targetHandle"],
    }

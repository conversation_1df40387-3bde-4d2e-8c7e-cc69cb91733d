import json
import os
import traceback
import uuid
from typing import Any, Optional

import dirtyjson
import json5
import json_repair
from strands.agent import Agent
from strands.hooks import MessageAddedEvent
from strands.models.openai import OpenAIModel
from strands.tools import tool

from app.shared.config.constants import Workflow_SSEEventType
from app.shared.config.logging_config import get_logger, setup_logging
from app.utils.mongodb_session_manager import MongoDBSessionManager
from app.workflow_chat.agent_prompt.main_agent import MAIN_AGENT_PROMPT
from app.workflow_chat.agent_prompt.planner_agent import PLANNER_AGENT_PROMPT
from app.workflow_chat.agent_prompt.workflow_generation_agent import (
    WORKFLOW_GENERATION_AGENT_PROMPT,
)
from app.workflow_chat.tools.context import get_context
from app.workflow_chat.tools.post_process import post_processing
from app.workflow_chat.tools.pre_process import pre_process
from app.workflow_chat.tools.RAG import RAG_search


def to_dict(obj):
    # Base cases for simple types
    if isinstance(obj, (str, int, float, bool, type(None))):
        return obj

    # Lists or tuples
    if isinstance(obj, (list, tuple, set)):
        return [to_dict(item) for item in obj]

    # Dictionaries
    if isinstance(obj, dict):
        return {key: to_dict(value) for key, value in obj.items()}

    # Custom objects
    if hasattr(obj, "__dict__"):
        return {
            key: to_dict(value)
            for key, value in vars(obj).items()
            if not key.startswith("_")
        }

    # Fallback: convert to string (for unsupported types like functions)
    return str(obj)


def create_callback_logger(session_id: str, name: str):
    def function(output):
        output = to_dict(output)
        output = {
            "message": output["message"],
            "session_id": session_id,
            "name": name,
        }
        output = json.dumps(output)
        logger = get_logger("app.workflow_chat.agent")
        logger.info(output)

    return function


def safe_loads(text: Optional[str]) -> Any:
    """
    Safely parse JSON text using multiple parsers with fallback options.

    Args:
        text: JSON string to parse. Can be None.

    Returns:
        Parsed JSON object

    Raises:
        ValueError: If all parsing attempts fail
        TypeError: If text is not a string or None
    """
    if text is None:
        raise ValueError("Cannot parse None as JSON")

    if not isinstance(text, str):
        raise TypeError(f"Expected string or None, got {type(text).__name__}")

    if not text.strip():
        raise ValueError("Cannot parse empty string as JSON")
    try:
        result = json.loads(text)
        return result
    except json.JSONDecodeError as e:
        pass
    try:
        result = json5.loads(text)
        return result
    except Exception as e:
        pass
    try:
        result = dirtyjson.loads(text)
        return result
    except Exception as e:
        pass
    try:
        result = json_repair.repair_json(text, return_objects=True)
        return result
    except Exception as e:
        raise ValueError(f"Could not parse JSON: {e}")


async def workflow_chat(prompt: str, workflow: dict, session_id: str = None):
    try:
        mongo_uri = os.getenv("MONGO_DB_URL")
        db_name = "workflow_chat"
        collection_name = "sessions"
        session_manager = MongoDBSessionManager(
            mongo_uri=mongo_uri,
            db_name=db_name,
            collection_name=collection_name,
        )
        if session_id is None or session_id == "None":
            session_id = str(uuid.uuid4())
            session_manager.create_session(session_id)
        REQUESTY_API_KEY = os.getenv("REQUESTY_API_KEY")
        logger = get_logger("app.workflow_chat.agent")
        user_input = {
            "role": "user",
            "content": {
                "prompt": prompt,
                "workflow": workflow,
                "session_id": session_id,
            },
            "session_id": session_id,
            "agent": "main_user_input",
        }
        logger.info(json.dumps(user_input))
        yield {
            "message": {},
            "error": {},
            "data": {},
            "event": Workflow_SSEEventType.STREAM_START.value,
            "session_id": session_id,
        }

        yield {
            "message": {},
            "error": {},
            "data": {},
            "event": Workflow_SSEEventType.PRE_PROCESSING.value,
            "session_id": session_id,
        }
        workflow = pre_process(workflow)

        model = OpenAIModel(
            client_args={
                "api_key": REQUESTY_API_KEY,
                "base_url": "https://router.requesty.ai/v1",
            },
            model_id="anthropic/claude-sonnet-4",
        )

        planner_agent = Agent(
            model=model,
            system_prompt=PLANNER_AGENT_PROMPT,
            agent_id="planner",
            tools=[RAG_search],
        )
        planner_agent.hooks.add_callback(
            MessageAddedEvent, create_callback_logger("planner", session_id)
        )
        workflow_generation_agent = Agent(
            model=model,
            system_prompt=WORKFLOW_GENERATION_AGENT_PROMPT,
            agent_id="workflow_generation",
            tools=[RAG_search, get_context],
        )
        workflow_generation_agent.hooks.add_callback(
            MessageAddedEvent, create_callback_logger("workflow_generation", session_id)
        )

        @tool(
            name="workflow_generation_agent",
            description="Generates the finalized, executable workflow.",
        )
        async def workflow_generation(prompt: str, plan: str):
            current_workflow = agent.state.get("current_workflow")
            input_prompt = {
                "prompt": prompt,
                "plan": plan,
                "current_workflow": current_workflow,
            }
            input_prompt = json.dumps(input_prompt)
            async for output in workflow_generation_agent.stream_async(input_prompt):
                if "result" in output:
                    output_ = output["result"].message["content"][0]["text"]
                    if "```json" in output_:
                        output_ = output_.split("```json")[1].split("```")[0]
                    output_ = safe_loads(output_)
                    workflow = output_["workflow"]
                    agent.state.set("workflow", workflow)
                    agent.state.set("workflow_generated", True)
                    output["result"].message["content"][0]["text"] = output_["message"]
                    yield output
                else:
                    yield output

        @tool(
            name="planner_agent",
            description="Generates the workflow plan and identifies tool availability or alternatives.",
        )
        async def planner(
            prompt: str,
            agent: Agent,
            previous_plan: str = None,
            feedback: str = None,
        ):
            input_prompt = {
                "prompt": prompt,
                "previous_plan": previous_plan,
                "feedback": feedback,
                "workflow": agent.state.get("current_workflow"),
            }
            input_prompt = json.dumps(input_prompt)
            async for output in planner_agent.stream_async(input_prompt):
                yield output

        @tool(
            name="get_current_workflow",
            description="Returns the current workflow.",
        )
        def get_current_workflow(agent: Agent):
            return agent.state.get("current_workflow")
        previous_message = session_manager.get_messages(session_id)
        agent = Agent(
            model=model,
            system_prompt=MAIN_AGENT_PROMPT,
            messages=previous_message,
            agent_id="main",
            tools=[workflow_generation, planner, get_current_workflow],
        )
        agent.hooks.add_callback(
            MessageAddedEvent, create_callback_logger("main", session_id)
        )
        agent.state.set("current_workflow", workflow)
        agent.state.set("workflow_generated", False)
        agent.state.delete("workflow")
        current_sub_agent = None
        current_sub_agent_id = None
        async for output in agent.stream_async(prompt):
            if "current_tool_use" in output:
                data = output["current_tool_use"]
                current_sub_agent_id = data["toolUseId"]
                current_sub_agent = data["name"]
                output = {
                    "current_sub_agent": data["name"],
                    "current_sub_agent_id": data["toolUseId"],
                    "input": data["input"],
                }
                yield {
                    "message": {},
                    "error": {},
                    "data": output,
                    "event": Workflow_SSEEventType.SUB_AGENT_CALLED.value,
                    "session_id": session_id,
                }
            if "tool_stream_event" in output:
                data = output["tool_stream_event"]["data"]
                if "current_tool_use" in data:
                    output = {
                        "current_sub_agent": current_sub_agent,
                        "current_sub_agent_id": current_sub_agent_id,
                        "current_tool": data["current_tool_use"]["name"],
                        "current_tool_id": data["current_tool_use"]["toolUseId"],
                        "input": data["current_tool_use"]["input"],
                    }
                    yield {
                        "message": {},
                        "error": {},
                        "data": output,
                        "event": Workflow_SSEEventType.TOOL_CALLED.value,
                        "session_id": session_id,
                    }
            if "delta" in output and "text" in output["delta"]:
                output = {"delta": output["delta"]}
                yield {
                    "message": output["delta"],
                    "error": {},
                    "data": {},
                    "event": Workflow_SSEEventType.MESSAGE.value,
                    "session_id": session_id,
                }
        session_manager.update_session(session_id, to_dict(agent.messages))
        session_manager.close()
        if agent.state.get("workflow_generated"):
            workflow = agent.state.get("workflow")
            yield {
                "message": {},
                "error": {},
                "data": {},
                "event": Workflow_SSEEventType.POST_PROCESSING.value,
                "session_id": session_id,
            }
            workflow = post_processing(workflow)
            yield {
                "message": {},
                "error": {},
                "data": {},
                "workflow": workflow,
                "session_id": session_id,
                "event": Workflow_SSEEventType.WORKFLOW_GENERATED.value,
            }
    except Exception as e:
        logger = get_logger("app.workflow_chat.agent")
        error_str = traceback.format_exc()
        logger.error(f"Error in workflow_chat: {e}\n{error_str}")
        yield {
            "message": {},
            "error": {"message": str(e), "stack_trace": error_str},
            "data": {},
            "event": Workflow_SSEEventType.ERROR.value,
            "session_id": session_id,
        }
    yield {
        "message": {},
        "error": {},
        "data": {},
        "event": Workflow_SSEEventType.STREAM_END.value,
        "session_id": session_id,
    }


if __name__ == "__main__":
    setup_logging()
    prompt = "Generate a workflow which generate a short mystery story and send via gmail. make it generic so it can be send to anyone"
    workflow = {"nodes": [], "edges": []}
    workflow_chat(prompt, workflow)

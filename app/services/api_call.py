import requests
from typing import Dict, Any, Optional, Union, Callable
from enum import Enum
import json
import logging
import time
from urllib.parse import urljoin
from app.utils.metrics import get_metrics_manager
from app.utils.exceptions import get_exception_tracker

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()


class HttpMethods(Enum):
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    PATCH = "PATCH"
    DELETE = "DELETE"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class AuthType(Enum):
    BEARER = "bearer"
    BASIC = "basic"
    API_KEY = "api_key"
    CUSTOM = "custom"
    NORMAL = "normal"
    NONE = "none"


class ContentType(Enum):
    JSON = "application/json"
    FORM = "application/x-www-form-urlencoded"
    MULTIPART = "multipart/form-data"
    XML = "application/xml"
    TEXT = "text/plain"


class HttpRequestHelper:
    def __init__(
        self,
        base_url: str,
        auth_token: Optional[str] = None,
        auth_type: Union[AuthType, str] = AuthType.BEARER,
        content_type: Union[ContentType, str] = ContentType.JSON,
        timeout: int = 30,
        headers: Optional[Dict[str, str]] = None,
        logger: Optional[logging.Logger] = None,
        verify_ssl: bool = True,
        auth_header_name: str = "Authorization",
        api_key_name: str = "api_key",
        custom_auth_function: Optional[Callable] = None,
    ):
        """
        Initialize the HTTP Request Helper with flexible configuration options

        Args:
            base_url: Base URL for all API requests
            auth_token: Authentication token or credentials
            auth_type: Authentication type (bearer, basic, api_key, custom, none)
            content_type: Default content type for requests
            timeout: Request timeout in seconds
            headers: Additional headers to include in all requests
            logger: Custom logger instance
            verify_ssl: Whether to verify SSL certificates
            auth_header_name: Custom name for auth header (default: Authorization)
            api_key_name: Name to use for API key if auth_type is api_key
            custom_auth_function: Custom function to generate auth headers
        """
        self.base_url = base_url.rstrip("/") + "/"
        self.auth_token = auth_token
        self.auth_type = (
            auth_type if isinstance(auth_type, AuthType) else AuthType(auth_type)
        )
        self.content_type = (
            content_type
            if isinstance(content_type, ContentType)
            else ContentType(content_type)
        )
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        self.auth_header_name = auth_header_name
        self.api_key_name = api_key_name
        self.custom_auth_function = custom_auth_function

        # Set up logging
        self.logger = logger or logging.getLogger(__name__)

        # Initialize headers
        self.headers = self._get_default_headers()
        if headers:
            self.headers.update(headers)

    def _get_default_headers(self) -> Dict[str, str]:
        """Generate default headers including content type and authentication"""
        headers = {"Content-Type": self.content_type.value}

        # Add authentication headers if applicable
        if self.auth_token:
            auth_headers = self._get_auth_headers()
            if auth_headers:
                headers.update(auth_headers)

        return headers

    def _get_auth_headers(self) -> Dict[str, str]:
        """Generate authentication headers based on the auth type"""
        if not self.auth_token or self.auth_type == AuthType.NONE:
            return {}

        if self.auth_type == AuthType.BEARER:
            return {self.auth_header_name: f"Bearer {self.auth_token}"}

        elif self.auth_type == AuthType.BASIC:
            return {self.auth_header_name: f"Basic {self.auth_token}"}

        elif self.auth_type == AuthType.NORMAL:
            return {self.auth_header_name: f"{self.auth_token}"}

        elif self.auth_type == AuthType.API_KEY:
            return {self.api_key_name: self.auth_token}

        elif self.auth_type == AuthType.CUSTOM and self.custom_auth_function:
            return self.custom_auth_function(self.auth_token)

        return {}

    def make_request(
        self,
        method: Union[HttpMethods, str],
        endpoint: str,
        data: Optional[Any] = None,
        params: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        files: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        timeout: Optional[int] = None,
        verify_ssl: Optional[bool] = None,
        return_raw_response: bool = False,
        expected_status_codes: Optional[list] = None,
    ) -> Union[Dict[str, Any], str, bytes, requests.Response, None]:
        """
        Make an HTTP request with flexible options

        Args:
            method: HTTP method to use
            endpoint: API endpoint (will be joined with base_url)
            data: Request data (for POST/PUT/PATCH)
            params: URL parameters
            headers: Additional headers for this request
            files: Files to upload
            json_data: JSON data to send (alternative to data)
            timeout: Custom timeout for this request
            verify_ssl: Whether to verify SSL for this request
            return_raw_response: If True, return the raw Response object
            expected_status_codes: List of expected HTTP status codes

        Returns:
            Processed response data, raw response, or None on error
        """
        url = urljoin(self.base_url, endpoint.lstrip("/"))
        method_value = method.value if isinstance(method, HttpMethods) else method
        merged_headers = {**self.headers, **(headers or {})}
        req_timeout = timeout if timeout is not None else self.timeout
        req_verify = verify_ssl if verify_ssl is not None else self.verify_ssl
        expected_codes = expected_status_codes or [200, 201, 202, 204]

        self.logger.info(
            f"headers: {merged_headers}, url: {url}, method: {method_value}, "
        )

        request_kwargs = {
            "method": method_value,
            "url": url,
            "headers": merged_headers,
            "params": params,
            "timeout": req_timeout,
            "verify": req_verify,
        }

        # Handle different types of request data
        if json_data is not None:
            request_kwargs["json"] = json_data
        elif data is not None:
            if self.content_type == ContentType.JSON and isinstance(data, (dict, list)):
                request_kwargs["json"] = data
            else:
                request_kwargs["data"] = data

        if files:
            request_kwargs["files"] = files
            # Remove content-type header if sending files to let requests set it with boundary
            if (
                "Content-Type" in request_kwargs["headers"]
                and request_kwargs["headers"]["Content-Type"] == ContentType.JSON.value
            ):
                del request_kwargs["headers"]["Content-Type"]

        start_time = time.time()
        metrics_manager = get_metrics_manager()

        try:
            self.logger.debug(f"Making {method_value} request to {url}")
            response = requests.request(**request_kwargs)

            # Record metrics
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_external_api_call(
                api_name="http_request",
                endpoint=endpoint,
                attributes={
                    "method": method_value,
                    "status_code": response.status_code,
                },
            )
            metrics_manager.record_external_api_duration(
                duration_ms=duration_ms,
                api_name="http_request",
                endpoint=endpoint,
                attributes={
                    "method": method_value,
                    "status_code": response.status_code,
                },
            )

            if return_raw_response:
                return response

            if response.status_code not in expected_codes:
                self.logger.error(
                    f"Request failed with status {response.status_code}: {response.text}"
                )
                response.raise_for_status()

            return self._process_response(response)

        except requests.exceptions.HTTPError as e:
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_external_api_error(
                api_name="http_request",
                endpoint=endpoint,
                error_type="HTTPError",
            )
            self.logger.error(f"HTTP error occurred: {e}")
            raise requests.exceptions.HTTPError(f"HTTP error occurred: {e}")
        except requests.exceptions.ConnectionError as e:
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_external_api_error(
                api_name="http_request",
                endpoint=endpoint,
                error_type="ConnectionError",
            )
            self.logger.error(f"Connection error: {e}")
            raise requests.exceptions.ConnectionError(f"Connection error: {e}")
        except requests.exceptions.Timeout as e:
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_external_api_error(
                api_name="http_request",
                endpoint=endpoint,
                error_type="Timeout",
            )
            self.logger.error(f"Request timed out: {e}")
            raise requests.exceptions.Timeout(f"Request timed out: {e}")
        except requests.exceptions.RequestException as e:
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_external_api_error(
                api_name="http_request",
                endpoint=endpoint,
                error_type="RequestException",
            )
            self.logger.error(f"Request exception: {e}")
            raise requests.exceptions.RequestException(f"Request exception: {e}")
        except Exception as e:
            # Track exception with Signoz OTEL
            exception_tracker.track_exception(
                e,
                severity="error",
                attributes={
                    "component": "http_request",
                    "endpoint": endpoint,
                    "method": method,
                },
            )
            
            duration_ms = (time.time() - start_time) * 1000
            metrics_manager.record_external_api_error(
                api_name="http_request",
                endpoint=endpoint,
                error_type=type(e).__name__,
            )
            self.logger.error(f"Unexpected error: {e}")
            raise Exception(f"Unexpected error: {e}")
        return None

    def _process_response(
        self, response: requests.Response
    ) -> Union[Dict[str, Any], str, bytes]:
        """Process the response based on content type"""
        content_type = response.headers.get("Content-Type", "")

        if not response.content:
            return {}

        if "application/json" in content_type:
            try:
                return response.json()
            except json.JSONDecodeError:
                self.logger.warning("Failed to parse JSON response")
                return response.text
        elif "text/" in content_type:
            return response.text
        else:
            return response.content

    # HTTP method specific convenience methods
    def get(self, endpoint: str, **kwargs) -> Any:
        """Make a GET request"""
        return self.make_request(HttpMethods.GET, endpoint, **kwargs)

    def post(self, endpoint: str, data=None, json_data=None, **kwargs) -> Any:
        """Make a POST request"""
        return self.make_request(
            HttpMethods.POST, endpoint, data=data, json_data=json_data, **kwargs
        )

    def put(self, endpoint: str, data=None, json_data=None, **kwargs) -> Any:
        """Make a PUT request"""
        return self.make_request(
            HttpMethods.PUT, endpoint, data=data, json_data=json_data, **kwargs
        )

    def patch(self, endpoint: str, data=None, json_data=None, **kwargs) -> Any:
        """Make a PATCH request"""
        return self.make_request(
            HttpMethods.PATCH, endpoint, data=data, json_data=json_data, **kwargs
        )

    def delete(self, endpoint: str, **kwargs) -> Any:
        """Make a DELETE request"""
        return self.make_request(HttpMethods.DELETE, endpoint, **kwargs)

    def head(self, endpoint: str, **kwargs) -> Any:
        """Make a HEAD request"""
        return self.make_request(HttpMethods.HEAD, endpoint, **kwargs)

    def options(self, endpoint: str, **kwargs) -> Any:
        """Make a OPTIONS request"""
        return self.make_request(HttpMethods.OPTIONS, endpoint, **kwargs)

    def update_auth(
        self, auth_token: str, auth_type: Optional[AuthType] = None
    ) -> None:
        """Update authentication credentials"""
        self.auth_token = auth_token
        if auth_type:
            self.auth_type = auth_type
        # Update headers with new auth
        auth_headers = self._get_auth_headers()
        if auth_headers:
            self.headers.update(auth_headers)

    def set_header(self, key: str, value: str) -> None:
        """Set a specific header value"""
        self.headers[key] = value

    def set_content_type(self, content_type: Union[ContentType, str]) -> None:
        """Update the content type"""
        self.content_type = (
            content_type
            if isinstance(content_type, ContentType)
            else ContentType(content_type)
        )
        self.headers["Content-Type"] = self.content_type.value

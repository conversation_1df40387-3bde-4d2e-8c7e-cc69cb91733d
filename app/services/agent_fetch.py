import logging
from opentelemetry.trace import SpanKind
from .api_call import AuthType, HttpRequestHelper
from ..shared.config.base import get_settings
from app.utils.tracing import trace_operation
from app.utils.exceptions import get_exception_tracker

logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()


class AgentFetchService:
    """Singleton service for fetching agent configurations and MCPs."""
    
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(AgentFetchService, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        # Only initialize once
        if not AgentFetchService._initialized:
            self.settings = get_settings()
            self.http_request_service = HttpRequestHelper(
                self.settings.gateway.api_url,
                auth_token=self.settings.gateway.api_key,
                auth_type=AuthType.API_KEY,
                api_key_name="X-Agent-Platform-Auth-Key",
            )
            AgentFetchService._initialized = True

    async def fetch_agent_config(self, agent_id):
        """
        Fetches an agent configuration from the database.

        Args:
            agent_id (str): The ID of the agent to fetch.

        Returns:
            dict: The agent configuration.
        """
        with trace_operation(
            "agent_fetch.config",
            kind=SpanKind.CLIENT,
            attributes={
                "agent_id": agent_id,
                "endpoint": f"agents/agent-platform/{agent_id}",
                "external_api": "api_gateway",
                "api_endpoint": f"/agents/agent-platform/{agent_id}",
                "operation": "fetch_agent_config",
                "peer.service": "api-gateway",
                "http.method": "GET",
            },
        ) as span:
            try:
                response = self.http_request_service.get(
                    endpoint=f"agents/agent-platform/{agent_id}"
                )
                agent_data = response.get("agent")
                span.set_attribute("response_size", len(str(agent_data)))
                span.set_attribute("success", True)
                return agent_data
            except Exception as e:
                # Track exception with Signoz OTEL
                exception_tracker.track_exception(
                    e,
                    severity="error",
                    attributes={
                        "component": "agent_fetch_config",
                        "agent_id": agent_id,
                    },
                )

                span.set_attribute("error", True)
                span.record_exception(e)
                raise

    async def fetch_mcps_by_ids(self, ids):
        """
        Fetches MCPs by their IDs using the agent-platform API.

        Args:
            ids (list): List of MCP IDs to fetch.

        Returns:
            dict: The API response containing the MCPs.
        """
        with trace_operation(
            "agent_fetch.mcps",
            kind=SpanKind.CLIENT,
            attributes={
                "mcp_ids_count": len(ids),
                "mcp_ids": ids,
                "endpoint": "mcps/agent-platform/by-ids",
                "external_api": "api_gateway",
                "api_endpoint": "/mcps/agent-platform/by-ids",
                "operation": "fetch_mcps",
                "peer.service": "api-gateway",
                "http.method": "POST",
            },
        ) as span:
            try:
                payload = {"ids": ids}
                response = self.http_request_service.post(
                    endpoint="mcps/agent-platform/by-ids", data=payload
                )
                mcps_data = response.get("mcps")
                span.set_attribute("response_size", len(str(mcps_data)))
                span.set_attribute("mcps_count", len(mcps_data) if mcps_data else 0)
                span.set_attribute("success", True)
                return mcps_data
            except Exception as e:
                # Track exception with Signoz OTEL
                exception_tracker.track_exception(
                    e,
                    severity="error",
                    attributes={
                        "component": "agent_fetch_mcps",
                        "mcp_ids_count": len(ids),
                    },
                )

                span.set_attribute("error", True)
                span.record_exception(e)
                raise

    async def fetch_workflows_by_ids(self, ids):
        """
        Fetches workflows by their IDs using the agent-platform API.

        Args:
            ids (list): List of workflow IDs to fetch.

        Returns:
            dict: The API response containing the workflows.
        """
        with trace_operation(
            "agent_fetch.workflows",
            kind=SpanKind.CLIENT,
            attributes={
                "workflow_ids_count": len(ids),
                "workflow_ids": ids,
                "endpoint": "workflows/agent-platform/by-ids",
                "external_api": "api_gateway",
                "api_endpoint": "/workflows/agent-platform/by-ids",
                "operation": "fetch_workflows",
                "peer.service": "api-gateway",
                "http.method": "POST",
            },
        ) as span:
            try:
                payload = {"ids": ids}
                response = self.http_request_service.post(
                    endpoint="workflows/agent-platform/by-ids", data=payload
                )
                workflows_data = response.get("workflows")
                span.set_attribute("response_size", len(str(workflows_data)))
                span.set_attribute("workflows_count", len(workflows_data) if workflows_data else 0)
                span.set_attribute("success", True)
                return workflows_data
            except Exception as e:
                # Track exception with Signoz OTEL
                exception_tracker.track_exception(
                    e,
                    severity="error",
                    attributes={
                        "component": "agent_fetch_workflows",
                        "workflow_ids_count": len(ids),
                    },
                )

                span.set_attribute("error", True)
                span.record_exception(e)
                raise


def get_agent_fetch_service() -> AgentFetchService:
    """
    Get the singleton instance of AgentFetchService.
    
    Returns:
        AgentFetchService: The singleton instance.
    """
    return AgentFetchService()

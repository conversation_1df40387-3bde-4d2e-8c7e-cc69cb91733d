import json
import logging
import time
import uuid


from app.agent.run_agent import run_agent_stream
from app.services.redis_streams import get_redis_manager
from app.shared.config.base import get_settings
from app.shared.config.constants import (
    RedisConsumerGroupEnum,
    RedisStreamEnum,
    RedisWorkflowStreamEnum,
    RequestPlatforms,
)
from app.utils.exceptions import get_exception_tracker
from app.utils.metrics import get_metrics_manager

from app.workflow_chat.agent import workflow_chat
from app.workflow_generation_graph.graph import generate_workflow

settings = get_settings()
logger = logging.getLogger(__name__)
exception_tracker = get_exception_tracker()
metrics_manager = get_metrics_manager()


async def listen_event_from_redis_streams():
    """Listen for events from Redis Streams using consumer groups"""
    
    # Get the global Redis Streams manager
    streams_manager = await get_redis_manager()

    # Create a unique consumer name for this instance
    consumer_name = f"agent-worker-{uuid.uuid4().hex[:8]}"

    # Get consumer for agent requests
    consumer = streams_manager.get_consumer(
        group=RedisConsumerGroupEnum.AGENT_PROCESSORS.value, consumer_name=consumer_name
    )

    try:
        # Handle any pending messages first
        await streams_manager.handle_pending_messages(
            group=RedisConsumerGroupEnum.AGENT_PROCESSORS.value,
            consumer_name=consumer_name,
            stream=RedisStreamEnum.AGENT_REQUESTS.value.format(
                env=settings.environment
            ),
        )

        # Consume agent request messages
        async for message in consumer.consume_messages(
            [
                RedisStreamEnum.AGENT_REQUESTS.value.format(env=settings.environment),
                RedisWorkflowStreamEnum.WORKFLOW_REQUESTS.value.format(
                    env=settings.environment
                ),
                RedisWorkflowStreamEnum.WORKFLOW_CHAT_REQUESTS.value.format(
                    env=settings.environment
                ),
                RedisStreamEnum.MEMORY_REQUESTS.value.format(env=settings.environment),
                RedisStreamEnum.STOP_REQUESTS.value.format(env=settings.environment),
            ],
            count=settings.redis.consumer_batch_size,
        ):
            processing_successful = False
            retry_needed = True
            if message.stream == RedisStreamEnum.AGENT_REQUESTS.value.format(
                env=settings.environment
            ):
                while retry_needed:
                    try:
                        # Extract message data
                        conversation_id = message.fields.get("conversation_id")
                        request_id = message.fields.get("request_id")
                        raw_message = message.fields.get("message")
                        platform = message.fields.get("platform")
                        user_id = message.fields.get("user_id")
                        organisation_id = message.fields.get("organisation_id")
                        user_phone_number = message.fields.get("user_phone_number")
                        user_name = message.fields.get("user_name")
                        user_email = message.fields.get("user_email")
                        model_data = message.fields.get("model", {})
                        attachments = message.fields.get("attachments", [])
                        mcp_ids = message.fields.get("mcp_ids", [])

                        # Parse boolean fields - Redis may send them as strings
                        def parse_bool(value, default=False):
                            """Convert Redis string booleans to actual booleans"""
                            if isinstance(value, bool):
                                return value
                            if isinstance(value, str):
                                return value.lower() in ("true", "1", "yes")
                            return bool(value) if value is not None else default

                        use_knowledge = parse_bool(
                            message.fields.get("use_knowledge"), False
                        )
                        use_search = parse_bool(message.fields.get("use_search"), False)
                        use_thinking = parse_bool(
                            message.fields.get("use_thinking"), False
                        )
                        use_memory = parse_bool(message.fields.get("use_memory"), True)
                        is_global = parse_bool(message.fields.get("is_global"), True)

                        agent_id = message.fields.get("agent_id")
                        kb_source = message.fields.get("kb_source")
                        kb_file_ids = message.fields.get("kb_file_ids", [])
                        timezone = message.fields.get("timezone", "UTC")
                        
                        # Apply timezone defaults - if not provided or empty string or None, use UTC
                        if not timezone or timezone == "":
                            timezone = "UTC"

                        # Extract provider and model from model_data dict
                        provider = (
                            model_data.get("provider")
                            if isinstance(model_data, dict)
                            else None
                        )
                        model_name = (
                            model_data.get("name")
                            if isinstance(model_data, dict)
                            else None
                        )

                        # Validate conversation_id
                        if not conversation_id:
                            logging.warning(f"Invalid message format - missing conversation_id: {message.fields}")
                            await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            processing_successful = True
                            break
                        
                        # Handle empty message with attachments
                        if raw_message is None or str(raw_message).strip() == "":
                            if attachments:
                                # If message is empty but attachments are present, use default message
                                user_message = "Refer to this"
                                logging.info(f"Empty message with attachments detected, using default message: '{user_message}'")
                            else:
                                # If message is empty and no attachments, invalid message
                                logging.warning(f"Invalid message format - empty message with no attachments: {message.fields}")
                                await consumer.acknowledge_message(
                                    message.stream, message.id
                                )
                                processing_successful = True
                                break
                        else:
                            # Ensure user_message is always a string
                            user_message = str(raw_message)



                        # TODO: Add thirdy party SMS integration

                        # if platform == RequestPlatforms.SMS.value:
                        #     # Process the user message with the agent via sms
                        #     response = run_agent(user_message, provider, model_name)
                        #
                        #     inputTokens = response.metrics.accumulated_usage[
                        #         "inputTokens"
                        #     ]
                        #     outputTokens = response.metrics.accumulated_usage[
                        #         "outputTokens"
                        #     ]
                        #     totalTokens = response.metrics.accumulated_usage[
                        #         "totalTokens"
                        #     ]
                        #
                        #     usage_info = {
                        #         "inputTokens": inputTokens,
                        #         "outputTokens": outputTokens,
                        #         "totalTokens": totalTokens,
                        #     }
                        #
                        #     final_payload = {
                        #         "message": response.message["content"][-1]["text"],
                        #         "conversation_id": conversation_id,
                        #         "platform": platform,
                        #         "user_id": user_id,
                        #         "user_phone_number": user_phone_number,
                        #         "usage_info": usage_info,
                        #         "model": {provider: provider, model_name: model_name},
                        #     }
                        #
                        #     # Send final payload to gateway
                        #     async with aiohttp.ClientSession() as session:
                        #         headers = {"X-API-Key": settings.agent_gateway_api_key}
                        #         async with session.post(
                        #             f"{settings.agent_gateway_url}/agents/response",
                        #             json=final_payload,
                        #             headers=headers,
                        #         ) as resp:
                        #             if resp.status == 200:
                        #                 logging.info(
                        #                     "Successfully sent payload to gateway"
                        #                 )
                        #             else:
                        #                 logging.error(
                        #                     f"Failed to send payload to gateway: {resp.status}"
                        #                 )
                        #     processing_successful = True
                        #     retry_needed = False

                        if platform == RequestPlatforms.WEB.value:
                            async for formatted_chunk in run_agent_stream(
                                user_message=user_message,
                                provider=provider,
                                model=model_name,
                                use_knowledge=use_knowledge,
                                use_search=use_search,
                                user_id=user_id,
                                organisation_id=organisation_id,
                                mcp_ids=mcp_ids,
                                conversation_id=conversation_id,
                                agent_id=agent_id,
                                use_memory=use_memory,
                                attachments=attachments,
                                use_thinking=use_thinking,
                                kb_source=kb_source,
                                kb_file_ids=kb_file_ids,
                                is_global=is_global,
                                timezone=timezone,
                                user_name=user_name,
                                user_email=user_email,
                            ):
                                await streams_manager.producer.send_agent_response(
                                    conversation_id=conversation_id,
                                    response_data=formatted_chunk,
                                    request_id=request_id,
                                )
                            ack_success = await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            if ack_success:
                                print(f"✅ COMPLETED | Conversation: {conversation_id} | Status: Success\n")
                                processing_successful = True
                                retry_needed = False
                            else:
                                print(f"❌ FAILED | Conversation: {conversation_id} | Status: Error (ACK failed)\n")
                                retry_needed = False

                        else:
                            logging.error(f"Unsupported platform: {platform}")
                            processing_successful = True
                            retry_needed = False

                    except Exception as e:
                        import traceback

                        logging.error(
                            f"Error processing message {message.id}: {traceback.format_exc()}"
                        )
                        logging.error(f"Error details: {e}")

                        # Track exception with Signoz OTEL
                        exception_tracker.track_exception(
                            e,
                            severity="error",
                            attributes={
                                "component": "redis_listener_agent_request",
                                "message_id": message.id,
                                "conversation_id": conversation_id,
                                "request_id": request_id,
                                "user_id": user_id,
                                "organisation_id": organisation_id,
                            },
                        )

                        # Record error metrics
                        metrics_manager.record_agent_error(
                            error_type=type(e).__name__,
                            attributes={
                                "component": "redis_listener_agent_request",
                                "message_id": message.id,
                            },
                        )

                        # Use enhanced error handling
                        retry_needed = await consumer.handle_processing_error(
                            message, e, streams_manager
                        )

                        # Acknowledge the message after successful processing
                        ack_success = await consumer.acknowledge_message(
                            message.stream, message.id
                        )
                        if ack_success:
                            logging.info(
                                f"Successfully processed and acknowledged message {message.id}"
                            )
                            processing_successful = True
                            retry_needed = False
                        else:
                            logging.warning(
                                f"Failed to acknowledge message {message.id}"
                            )
                            retry_needed = False  # Don't retry ACK failures

            elif (
                message.stream
                == RedisWorkflowStreamEnum.WORKFLOW_REQUESTS.value.format(
                    env=settings.environment
                )
            ):
                while retry_needed:
                    try:
                        # Extract message data
                        request_id = message.fields.get("request_id")
                        prompt = message.fields.get("prompt")
                        if not prompt:
                            await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            processing_successful = True
                            break

                        # Process the user message with the agent
                        # async for formatted_chunk in run_agent_stream(
                        #     user_message, provider, model_name, attachments
                        # ):
                        #     # Send the formatted chunk to the Redis Streams response stream
                        #     await streams_manager.producer.send_agent_response(
                        #         conversation_id=conversation_id,
                        #         response_data=formatted_chunk,
                        #         request_id=request_id,
                        #     )
                        async for formatted_chunk in generate_workflow(
                            prompt, request_id
                        ):
                            await streams_manager.workflow_producer.send_agent_response(
                                response_data=formatted_chunk,
                                request_id=request_id,
                            )
                        # Acknowledge the message after successful processing
                        ack_success = await consumer.acknowledge_message(
                            message.stream, message.id
                        )
                        if ack_success:
                            processing_successful = True
                            retry_needed = False
                        else:
                            retry_needed = False  # Don't retry ACK failures

                    except Exception as e:
                        import traceback

                        logging.error(
                            f"Error processing message {message.id}: {traceback.format_exc()}"
                        )
                        logging.error(f"Error details: {e}")

                        # Track exception with Signoz OTEL
                        exception_tracker.track_exception(
                            e,
                            severity="error",
                            attributes={
                                "component": "redis_listener_workflow_request",
                                "message_id": message.id,
                                "request_id": str(request_id) if request_id else None,
                            },
                        )

                        # Record error metrics
                        metrics_manager.record_agent_error(
                            error_type=type(e).__name__,
                            attributes={
                                "component": "redis_listener_workflow_request",
                                "message_id": message.id,
                            },
                        )

                        # Use enhanced error handling
                        retry_needed = await consumer.handle_processing_error(
                            message, e, streams_manager
                        )

                        if not retry_needed:
                            processing_successful = False
                            break
            elif (
                message.stream
                == RedisWorkflowStreamEnum.WORKFLOW_CHAT_REQUESTS.value.format(
                    env=settings.environment
                )
            ):
                while retry_needed:
                    try:
                        # Extract message data
                        request_id = message.fields.get("request_id")
                        prompt = message.fields.get("prompt")
                        workflow = message.fields.get("workflow")
                        session_id = message.fields.get("session_id")
                        if not prompt:
                            await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            processing_successful = True
                            break

                        # Process the user message with the agent
                        # async for formatted_chunk in run_agent_stream(
                        #     user_message, provider, model_name, attachments
                        # ):
                        #     # Send the formatted chunk to the Redis Streams response stream
                        #     await streams_manager.producer.send_agent_response(
                        #         conversation_id=conversation_id,
                        #         response_data=formatted_chunk,
                        #         request_id=request_id,
                        #     )
                        async for formatted_chunk in workflow_chat(
                            prompt, workflow, session_id
                        ):
                            await streams_manager.workflow_producer.send_agent_response(
                                response_data=formatted_chunk,
                                request_id=request_id,
                                chat=True,
                            )
                        # Acknowledge the message after successful processing
                        ack_success = await consumer.acknowledge_message(
                            message.stream, message.id
                        )
                        if ack_success:
                            processing_successful = True
                            retry_needed = False
                        else:
                            retry_needed = False  # Don't retry ACK failures

                    except Exception as e:
                        import traceback

                        logging.error(
                            f"Error processing message {message.id}: {traceback.format_exc()}"
                        )
                        logging.error(f"Error details: {e}")

                        # Track exception with Signoz OTEL
                        exception_tracker.track_exception(
                            e,
                            severity="error",
                            attributes={
                                "component": "redis_listener_workflow_chat_request",
                                "message_id": message.id,
                                "request_id": str(request_id) if request_id else None,
                                "session_id": str(session_id) if session_id else None,
                            },
                        )

                        # Record error metrics
                        metrics_manager.record_agent_error(
                            error_type=type(e).__name__,
                            attributes={
                                "component": "redis_listener_workflow_chat_request",
                                "message_id": message.id,
                            },
                        )

                        # Use enhanced error handling
                        retry_needed = await consumer.handle_processing_error(
                            message, e, streams_manager
                        )

                        if not retry_needed:
                            processing_successful = False
                            break
            elif message.stream == RedisStreamEnum.MEMORY_REQUESTS.value.format(
                env=settings.environment
            ):
                while retry_needed:
                    try:
                        # Extract memory data
                        text = message.fields.get("text")
                        user_id = message.fields.get("user_id")
                        conversation_id = message.fields.get("conversation_id")
                        agent_id = message.fields.get("agent_id")
                        organisation_id = message.fields.get("organisation_id")
                        additional_metadata_str = message.fields.get(
                            "additional_metadata", "{}"
                        )
                        additional_metadata = (
                            json.loads(additional_metadata_str)
                            if additional_metadata_str
                            else {}
                        )

                        # Ensure user_id is string (mem0 requires string)
                        if not text or not user_id:
                            logging.warning(
                                f"Invalid memory message format: {message.fields}"
                            )
                            await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            processing_successful = True
                            break

                        user_id = str(user_id)
                        conversation_id = (
                            str(conversation_id) if conversation_id else None
                        )
                        agent_id = str(agent_id) if agent_id else None
                        organisation_id = (
                            str(organisation_id) if organisation_id else None
                        )

                        logging.info(
                            f"Processing memory storage for user {user_id}: {message.id}"
                        )

                        # Store memory using existing implementation
                        from app.agent.tools.memory_tools import (
                            _store_memory_impl_async,
                        )

                        await _store_memory_impl_async(
                            text=text,
                            user_id=user_id,
                            conversation_id=conversation_id,
                            agent_id=agent_id,
                            organisation_id=organisation_id,
                            additional_metadata=additional_metadata,
                        )

                        # Acknowledge success
                        ack_success = await consumer.acknowledge_message(
                            message.stream, message.id
                        )
                        if ack_success:
                            logging.info(
                                f"Successfully stored memory and acknowledged message {message.id}"
                            )
                            processing_successful = True
                            retry_needed = False
                        else:
                            logging.warning(
                                f"Failed to acknowledge memory message {message.id}"
                            )
                            retry_needed = False

                    except Exception as e:
                        import traceback

                        logging.error(
                            f"Error processing message {message.id}: {traceback.format_exc()}"
                        )
                        logging.error(f"Error details: {e}")

                        # Track exception with Signoz OTEL
                        exception_tracker.track_exception(
                            e,
                            severity="error",
                            attributes={
                                "component": "redis_listener_memory_request",
                                "message_id": message.id,
                                "user_id": str(user_id) if user_id else None,
                                "organisation_id": str(organisation_id) if organisation_id else None,
                                "conversation_id": str(conversation_id) if conversation_id else None,
                            },
                        )

                        # Record error metrics
                        metrics_manager.record_agent_error(
                            error_type=type(e).__name__,
                            attributes={
                                "component": "redis_listener_memory_request",
                                "message_id": message.id,
                            },
                        )

                        # Use enhanced error handling
                        retry_needed = await consumer.handle_processing_error(
                            message, e, streams_manager
                        )

                        if not retry_needed:
                            processing_successful = False
                            break
            elif message.stream == RedisStreamEnum.STOP_REQUESTS.value.format(
                env=settings.environment
            ):
                while retry_needed:
                    try:
                        # Extract stop request data
                        conversation_id = message.fields.get("conversation_id")

                        if not conversation_id:
                            logging.warning(
                                f"Invalid stop request format: {message.fields}"
                            )
                            await consumer.acknowledge_message(
                                message.stream, message.id
                            )
                            processing_successful = True
                            break

                        logging.info(
                            f"Processing stop request for conversation {conversation_id}: {message.id}"
                        )

                        # Set stop signal in Redis using existing redis client from streams_manager
                        stop_key = f"stop_signal:{conversation_id}"
                        await streams_manager.redis_client.set_value(
                            stop_key, "true", ttl=60
                        )  # 60 seconds TTL
                        logging.info(
                            f"Set stop signal for conversation {conversation_id}"
                        )

                        # Acknowledge success
                        ack_success = await consumer.acknowledge_message(
                            message.stream, message.id
                        )
                        if ack_success:
                            logging.info(
                                f"Successfully processed stop request and acknowledged message {message.id}"
                            )
                            processing_successful = True
                            retry_needed = False
                        else:
                            logging.warning(
                                f"Failed to acknowledge stop request {message.id}"
                            )
                            retry_needed = False

                    except Exception as e:
                        import traceback

                        logging.error(
                            f"Error processing stop request {message.id}: {traceback.format_exc()}"
                        )
                        logging.error(f"Error details: {e}")

                        # Track exception with Signoz OTEL
                        exception_tracker.track_exception(
                            e,
                            severity="error",
                            attributes={
                                "component": "redis_listener_stop_request",
                                "message_id": message.id,
                                "conversation_id": str(conversation_id) if conversation_id else None,
                            },
                        )

                        # Record error metrics
                        metrics_manager.record_agent_error(
                            error_type=type(e).__name__,
                            attributes={
                                "component": "redis_listener_stop_request",
                                "message_id": message.id,
                            },
                        )

                        # Use enhanced error handling
                        retry_needed = await consumer.handle_processing_error(
                            message, e, streams_manager
                        )

                        if not retry_needed:
                            processing_successful = False
                            break
            if not processing_successful:
                logging.error(
                    f"Failed to process message {message.id} after all retries"
                )

    except Exception as e:
        logging.error(f"Fatal error in Redis Streams listener: {e}")
        raise
    finally:
        logging.info(f"Stopping Redis Streams consumer {consumer_name}")
        consumer.stop()


async def listen_event_from_redis_pubsub():
    """Listen for events from Redis Streams"""
    logging.info("Using Redis Streams implementation")
    await listen_event_from_redis_streams()

"""
MongoDB Client Manager

This module provides a global MongoDB client instance that is initialized
once at application startup and reused across all requests.
"""

from typing import Optional

from pymongo import MongoClient
from langgraph.checkpoint.mongodb import MongoDBSaver

from app.shared.config.base import get_settings
from app.shared.config.logging_config import get_logger

logger = get_logger(__name__)

_mongodb_client: Optional[MongoClient] = None
_mongodb_saver: Optional[MongoDBSaver] = None


def initialize_mongodb_client() -> None:
    """
    Initialize the global MongoDB client.
    Should be called once during application startup.
    """
    global _mongodb_client
    if _mongodb_client is None:
        settings = get_settings()
        logger.info(
            "Initializing MongoDB client",
            extra={"mongodb_url": settings.mongodb.url.split("@")[-1]},  # Log without credentials
        )
        _mongodb_client = MongoClient(settings.mongodb.url)
        logger.info("MongoDB client initialized successfully")
    else:
        logger.warning("MongoDB client already initialized")


def get_mongodb_client() -> MongoClient:
    """
    Get the global MongoDB client instance.
    If not initialized, it will be lazily initialized with a warning.
    
    Returns:
        MongoClient: The global MongoDB client instance
    """
    global _mongodb_client
    if _mongodb_client is None:
        logger.warning(
            "MongoDB client was not initialized at startup. "
            "Performing lazy initialization. "
            "Consider calling initialize_mongodb_client() during application startup."
        )
        initialize_mongodb_client()
    return _mongodb_client


def get_mongodb_saver() -> MongoDBSaver:
    """
    Get the global MongoDBSaver instance.
    Creates it if not already initialized.
    
    Returns:
        MongoDBSaver: The global MongoDBSaver instance
    """
    global _mongodb_saver
    if _mongodb_saver is None:
        client = get_mongodb_client()
        _mongodb_saver = MongoDBSaver(client)
        logger.info("MongoDBSaver initialized")
    return _mongodb_saver


def close_mongodb_client() -> None:
    """
    Close the global MongoDB client connection.
    Should be called during application shutdown.
    """
    global _mongodb_client, _mongodb_saver
    if _mongodb_client:
        logger.info("Closing MongoDB client")
        _mongodb_client.close()
        _mongodb_client = None
        _mongodb_saver = None
        logger.info("MongoDB client closed successfully")
    else:
        logger.warning("MongoDB client already closed or never initialized")

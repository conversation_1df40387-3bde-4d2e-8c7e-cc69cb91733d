import ast
import json
import time
import uuid
from contextvars import <PERSON><PERSON><PERSON><PERSON>

from app.shared.config.constants import EventType, AgentToolEnum

# Context-local storage for pending tool use (per request)
_pending_tool_use: ContextVar = ContextVar("_pending_tool_use", default=None)  # type: ignore

# Context-local storage for tracking reasoning state per source_agent (per request)
# Using None as default to ensure each context gets its own dict instance
_reasoning_active: ContextVar = ContextVar("_reasoning_active", default=None)  # type: ignore

# Context-local storage for tracking task tool execution depth and collecting events
_task_execution_stack: ContextVar = ContextVar("_task_execution_stack", default=None)  # type: ignore

# Timeout for task execution tracking (in seconds) - if a task doesn't complete within this time, clear its stack
TASK_STACK_TIMEOUT = 600  # 10 minutes


def _is_inside_task():
    """Check if we're currently inside a task tool execution."""
    task_stack = _task_execution_stack.get()
    return task_stack and len(task_stack) > 0


def _get_current_delegation_id():
    """Get the delegation_id of the current task, or empty string if not in a task."""
    task_stack = _task_execution_stack.get()
    if task_stack and len(task_stack) > 0:
        return task_stack[-1].get("delegation_id", "")
    return ""


def _clean_expired_stacks():
    """
    Remove expired task stacks that have been active longer than the timeout.
    This prevents memory leaks from tasks that never complete.
    """
    task_stack = _task_execution_stack.get()
    if not task_stack:
        return
    
    current_time = time.time()
    # Filter out expired stacks
    cleaned_stack = [
        stack_entry for stack_entry in task_stack
        if current_time - stack_entry.get("start_time", current_time) < TASK_STACK_TIMEOUT
    ]
    
    # Update the stack if we removed any entries
    if len(cleaned_stack) != len(task_stack):
        _task_execution_stack.set(cleaned_stack if cleaned_stack else None)


def _add_delegation_id(event_data):
    """
    Add delegation_id to event data. If inside a task, use the task's delegation_id,
    otherwise use empty string. Does not overwrite if delegation_id already exists.
    
    Args:
        event_data: The event data to add delegation_id to
        
    Returns:
        The event_data with delegation_id added
    """
    if event_data is None:
        return None
    
    delegation_id = _get_current_delegation_id()
    
    if isinstance(event_data, list):
        for event in event_data:
            if isinstance(event, dict) and "delegation_id" not in event:
                event["delegation_id"] = delegation_id
    elif isinstance(event_data, dict) and "delegation_id" not in event_data:
        event_data["delegation_id"] = delegation_id
    
    return event_data


def _collect_and_return(event_data):
    """
    Helper function to collect events that occur within a task tool execution
    and then return the event data with modified db_save flag and delegation_id.
    
    Args:
        event_data: The formatted event data to potentially collect and return
        
    Returns:
        The same event_data passed in, with db_save set to False if inside a task
        and delegation_id added
    """
    if event_data is None:
        return None
    
    # Clean expired stacks before collecting
    _clean_expired_stacks()
    
    # Add delegation_id to all events
    _add_delegation_id(event_data)
    
    task_stack = _task_execution_stack.get()
    if task_stack and len(task_stack) > 0:
        # We're inside a task execution, collect this event
        # Handle both single events and lists of events
        if isinstance(event_data, list):
            # For lists, collect each event separately and set db_save to False
            for event in event_data:
                if isinstance(event, dict):
                    event["db_save"] = False
                task_stack[-1]["events"].append(event)
        else:
            # Set db_save to False for events inside task
            if isinstance(event_data, dict):
                event_data["db_save"] = False
            task_stack[-1]["events"].append(event_data)
    
    return event_data


def stream_formatter(chunk):
    """
    Formats a LangGraph streaming chunk into a simplified output format.

    Args:
        chunk (dict): The raw event chunk from LangGraph stream

    Returns:
        dict, list of dicts, or None: Formatted output with type, delta/source_agent,
        list of multiple events, or None to ignore
    """
    # Handle dict-based events
    if isinstance(chunk, dict) and "event" in chunk:
        event = chunk["event"]

        # Ignore on_chain_start and on_chain_end events
        if event in ("on_chain_start", "on_chain_end"):
            return None

        # Handle on_chain_stream
        if event == "on_chain_stream":
            if "data" not in chunk or "chunk" not in chunk["data"]:
                return None

            data_chunk = chunk["data"]["chunk"]

            if isinstance(data_chunk, (list, tuple)) and len(data_chunk) >= 3:
                event_type = data_chunk[1]

                if event_type == "custom":
                    custom_data = data_chunk[2]

                    source_agent = "unknown"
                    if len(data_chunk) >= 1 and isinstance(
                        data_chunk[0], (list, tuple)
                    ):
                        namespace = data_chunk[0]
                        if len(namespace) > 0 and isinstance(namespace[0], str):
                            if ":" in namespace[0]:
                                source_agent = namespace[0].split(":")[0]

                    result = {
                        "type": EventType.TOOL_STREAM.value,
                        "delta": custom_data,
                        "source_agent": source_agent,
                    }

                    if (
                        isinstance(custom_data, dict)
                        and custom_data.get("type") == "workflow_complete"
                    ):
                        if custom_data.get("db_save"):
                            result["db_save"] = True

                    return _collect_and_return(result)

            return None

        # Handle on_chat_model_start
        if event == "on_chat_model_start":
            source_agent = "unknown"
            if "metadata" in chunk and "langgraph_checkpoint_ns" in chunk["metadata"]:
                checkpoint_ns = chunk["metadata"]["langgraph_checkpoint_ns"]
                if ":" in checkpoint_ns:
                    source_agent = checkpoint_ns.split(":")[0]
            result = {"type": EventType.MESSAGE_START.value, "source_agent": source_agent}
            # Don't collect message_start events but add delegation_id
            _add_delegation_id(result)
            return result

        # Handle on_chat_model_stream
        if event == "on_chat_model_stream":
            if "data" in chunk and "chunk" in chunk["data"]:
                message_chunk = chunk["data"]["chunk"]

                # Ignore chunks with tool_calls in additional_kwargs
                if (
                    hasattr(message_chunk, "additional_kwargs")
                    and "tool_calls" in message_chunk.additional_kwargs
                ):
                    return None

                # Extract source_agent
                source_agent = "unknown"
                if (
                    "metadata" in chunk
                    and "langgraph_checkpoint_ns" in chunk["metadata"]
                ):
                    checkpoint_ns = chunk["metadata"]["langgraph_checkpoint_ns"]
                    if ":" in checkpoint_ns:
                        source_agent = checkpoint_ns.split(":")[0]

                # Check for reasoning_content in additional_kwargs
                reasoning_content = ""
                if hasattr(message_chunk, "additional_kwargs"):
                    reasoning_content = message_chunk.additional_kwargs.get(
                        "reasoning_content", ""
                    )

                # Handle reasoning content
                if reasoning_content:
                    reasoning_state = _reasoning_active.get()
                    if reasoning_state is None:
                        reasoning_state = {}
                    # If reasoning wasn't active, send both thinking_start and thinking delta
                    if not reasoning_state.get(source_agent, False):
                        reasoning_state[source_agent] = True
                        _reasoning_active.set(reasoning_state)
                        # Return both events as a list
                        result = [
                            {
                                "type": EventType.THINKING_START.value,
                                "source_agent": source_agent,
                            },
                            {
                                "type": EventType.THINKING.value,
                                "delta": reasoning_content,
                                "source_agent": source_agent,
                            },
                        ]
                        # Don't collect thinking events but add delegation_id
                        _add_delegation_id(result)
                        return result

                    # Send thinking delta
                    result = {
                        "type": EventType.THINKING.value,
                        "delta": reasoning_content,
                        "source_agent": source_agent,
                    }
                    # Don't collect thinking events but add delegation_id
                    _add_delegation_id(result)
                    return result

                # If no reasoning_content and reasoning was active, send thinking_end
                reasoning_state = _reasoning_active.get()
                if (
                    reasoning_state is not None
                    and not reasoning_content
                    and reasoning_state.get(source_agent, False)
                ):
                    reasoning_state[source_agent] = False
                    _reasoning_active.set(reasoning_state)
                    result = {
                        "type": EventType.THINKING_END.value,
                        "source_agent": source_agent,
                    }
                    # Don't collect thinking_end events but add delegation_id
                    _add_delegation_id(result)
                    return result

                # Extract delta from content
                delta = ""
                if hasattr(message_chunk, "content"):
                    delta = message_chunk.content if message_chunk.content else ""

                # Don't send MESSAGE event if delta is empty
                if not delta:
                    return None

                result = {
                    "type": EventType.MESSAGE.value,
                    "delta": delta,
                    "source_agent": source_agent,
                }
                # Don't collect message delta events but add delegation_id
                _add_delegation_id(result)
                return result

        # Handle on_tool_start
        if event == "on_tool_start":
            # Return None if no tool name
            if "name" not in chunk:
                return None

            tool_name = chunk["name"]

            # Ignore tools that start with transfer_to
            if tool_name.startswith("transfer_to"):
                return None

            # # Ignore task tool
            # if tool_name == "task":
            #     return None

            tool_input = chunk.get("data", {}).get("input", {})

            # Remove state from tool_input to avoid sending it in the event
            if isinstance(tool_input, dict) and "state" in tool_input:
                tool_input = {k: v for k, v in tool_input.items() if k != "state"}

            # Send empty string input for read_todos
            if tool_name == AgentToolEnum.READ_TODOS.value:
                tool_input = ""

            source_agent = "unknown"
            if "metadata" in chunk and "langgraph_checkpoint_ns" in chunk["metadata"]:
                checkpoint_ns = chunk["metadata"]["langgraph_checkpoint_ns"]
                if ":" in checkpoint_ns:
                    source_agent = checkpoint_ns.split(":")[0]

            # If this is a task tool, initialize tracking
            if tool_name == "task":
                # Clean expired stacks before creating a new one
                _clean_expired_stacks()
                
                # Generate a unique delegation_id
                delegation_id = str(uuid.uuid4())
                
                task_stack = _task_execution_stack.get()
                if task_stack is None:
                    task_stack = []
                # Push a new tracking context for this task execution with start time and delegation_id
                task_stack.append({
                    "events": [], 
                    "tool_id": None,
                    "start_time": time.time(),
                    "delegation_id": delegation_id
                })
                _task_execution_stack.set(task_stack)

            # Change tool_name from "task" to "delegate"
            display_tool_name = "delegate" if tool_name == "task" else tool_name

            result = {
                "type": EventType.TOOL_START.value,
                "tool_name": display_tool_name,
                "input": tool_input,
                "source_agent": source_agent,
            }
            # Add delegation_id
            _add_delegation_id(result)
            return result

        # Handle on_tool_end
        if event == "on_tool_end":
            if "name" not in chunk:
                return None

            tool_name = chunk["name"]

            # Ignore tools that start with transfer_to
            if tool_name.startswith("transfer_to"):
                return None

            # # Ignore task tool
            # if tool_name == "task":
            #     return None

            output = ""
            tool_id = ""
            is_error = False
            tool_input = ""

            # Extract output and tool_call_id from data.output
            if "data" in chunk and "output" in chunk["data"]:
                data_output = chunk["data"]["output"]

                # Case 1: output is a ToolMessage directly
                if hasattr(data_output, "content"):
                    output = data_output.content
                    if hasattr(data_output, "tool_call_id"):
                        tool_id = data_output.tool_call_id
                # Case 2: output is a Command with update containing messages
                elif (
                    hasattr(data_output, "update")
                    and data_output.update is not None
                    and "messages" in data_output.update
                ):
                    messages = data_output.update["messages"]
                    if isinstance(messages, list) and len(messages) > 0:
                        # Get the first ToolMessage content and tool_call_id
                        if hasattr(messages[0], "content"):
                            output = messages[0].content
                        if hasattr(messages[0], "tool_call_id"):
                            tool_id = messages[0].tool_call_id

            # Extract input from data.input
            if "data" in chunk and "input" in chunk["data"]:
                tool_input = chunk["data"]["input"]

                # Don't include input for read_todos
                if tool_name == AgentToolEnum.READ_TODOS.value:
                    tool_input = ""
            else:
                tool_input = ""

            # Parse output to check for isError field and convert to dict if possible
            try:
                if isinstance(output, str):
                    # Try to parse as JSON
                    try:
                        output_dict = json.loads(output)
                        if isinstance(output_dict, dict):
                            # Successfully parsed JSON string to dict
                            if "isError" in output_dict:
                                is_error = output_dict.get("isError", False)
                            # Replace the string output with the parsed dict
                            output = output_dict
                    except (json.JSONDecodeError, ValueError):
                        # Try to evaluate as Python dict string
                        try:
                            output_dict = ast.literal_eval(output)
                            if isinstance(output_dict, dict):
                                if "isError" in output_dict:
                                    is_error = output_dict.get("isError", False)
                                # Replace the string output with the parsed dict
                                output = output_dict
                        except (ValueError, SyntaxError):
                            pass
                elif isinstance(output, dict) and "isError" in output:
                    is_error = output.get("isError", False)
            except Exception:
                pass

            source_agent = "unknown"
            if "metadata" in chunk and "langgraph_checkpoint_ns" in chunk["metadata"]:
                checkpoint_ns = chunk["metadata"]["langgraph_checkpoint_ns"]
                if ":" in checkpoint_ns:
                    source_agent = checkpoint_ns.split(":")[0]

            result = {
                "type": EventType.TOOL_END.value,
                "tool_name": tool_name,
                "output": output,
                "tool_id": tool_id,
                "source_agent": source_agent,
                "isError": is_error,
                "db_save": True,
            }

            # Always add input field
            result["input"] = tool_input

            # If this is a task tool end, collect all events and pop from stack
            if tool_name == "task":
                task_stack = _task_execution_stack.get()
                if task_stack and len(task_stack) > 0:
                    task_context = task_stack[-1]  # Get the context without popping yet
                    delegation_id = task_context.get("delegation_id", "")
                    
                    # Transform collected events into text and tool objects
                    formatted_output = []
                    
                    for event in task_context["events"]:
                        if not isinstance(event, dict):
                            continue
                            
                        event_type = event.get("type")
                        
                        # Handle message_end events -> convert to text type
                        if event_type == EventType.MESSAGE_END.value:
                            text_content = event.get("text_content", "")
                            if text_content:
                                formatted_output.append({
                                    "content": text_content,
                                    "type": "text",
                                })
                        
                        # Handle tool_end events -> convert to tool type
                        elif event_type == EventType.TOOL_END.value:
                            formatted_output.append({
                                "output": event.get("output", ""),
                                "input": event.get("input", ""),
                                "type": "tool",
                                "isError": event.get("isError", False),
                                "tool_name": event.get("tool_name", ""),
                            })
                    
                    # Replace output with the formatted events list
                    result["output"] = formatted_output
                    
                    # Manually add delegation_id before popping the stack
                    result["delegation_id"] = delegation_id
                    
                    # Now pop the stack
                    task_stack.pop()
                    _task_execution_stack.set(task_stack)

            # Change tool_name from "task" to "delegate"
            if result.get("tool_name") == "task":
                result["tool_name"] = "delegate"
            
            return _collect_and_return(result)

        # Handle on_chat_model_end
        if event == "on_chat_model_end":
            if "data" not in chunk or "output" not in chunk["data"]:
                return None

            ai_message = chunk["data"]["output"]

            # Extract text content
            text_content = ""
            if hasattr(ai_message, "content"):
                text_content = ai_message.content if ai_message.content else ""

            # Extract tool_call data
            tool_call = {}
            if hasattr(ai_message, "tool_calls") and ai_message.tool_calls:
                first_tool_call = ai_message.tool_calls[0]
                tool_name_in_call = first_tool_call.get("name", "")
                # Change "task" to "delegate"
                if tool_name_in_call == "task":
                    tool_name_in_call = "delegate"
                tool_call = {
                    "tool_name": tool_name_in_call,
                    "tool_id": first_tool_call.get("id", ""),
                    "tool_input": first_tool_call.get("args", {}),
                }

            # Extract usage data
            usage_data = {}
            if hasattr(ai_message, "usage_metadata"):
                usage_metadata = ai_message.usage_metadata
                usage_data = {
                    "input_tokens": usage_metadata.get("input_tokens", 0),
                    "output_tokens": usage_metadata.get("output_tokens", 0),
                    "total_tokens": usage_metadata.get("total_tokens", 0),
                }

            # Extract source_agent
            source_agent = "unknown"
            if "metadata" in chunk and "langgraph_checkpoint_ns" in chunk["metadata"]:
                checkpoint_ns = chunk["metadata"]["langgraph_checkpoint_ns"]
                if ":" in checkpoint_ns:
                    source_agent = checkpoint_ns.split(":")[0]

            # Extract reasoning_content if available
            # Set db_save to False if text_content is empty
            db_save = True if text_content else False

            result = {
                "type": EventType.MESSAGE_END.value,
                "text_content": text_content,
                "tool_call": tool_call,
                "usage_data": usage_data,
                "source_agent": source_agent,
                "db_save": db_save,
            }

            # Add thinking_content if available in additional_kwargs
            if hasattr(ai_message, "additional_kwargs"):
                reasoning_content = ai_message.additional_kwargs.get(
                    "reasoning_content", ""
                )
                if reasoning_content:
                    result["thinking_content"] = reasoning_content

            # Reset reasoning state for this agent
            reasoning_state = _reasoning_active.get()
            if reasoning_state is not None and source_agent in reasoning_state:
                reasoning_state[source_agent] = False
                _reasoning_active.set(reasoning_state)

            return _collect_and_return(result)

    # Legacy tuple-based format handling
    # Default values
    delta = ""
    source_agent = "unknown"

    # Check if chunk is a tuple with at least 3 elements
    if not isinstance(chunk, tuple) or len(chunk) < 3:
        # Don't send MESSAGE event if delta is empty
        if not delta:
            return None
        result = {
            "type": EventType.MESSAGE.value,
            "delta": delta,
            "source_agent": source_agent,
        }
        # Don't collect legacy message events but add delegation_id
        _add_delegation_id(result)
        return result

    namespace_tuple, event_type, data = chunk

    # Extract delta from 'messages' events
    if event_type == "messages" and isinstance(data, tuple) and len(data) >= 1:
        message_chunk = data[0]
        # Check if it's an AIMessageChunk with content
        if hasattr(message_chunk, "content"):
            delta = message_chunk.content if message_chunk.content else ""

        # Extract source_agent from metadata (second element of data tuple)
        if len(data) >= 2 and isinstance(data[1], dict):
            metadata = data[1]
            if "langgraph_checkpoint_ns" in metadata:
                checkpoint_ns = metadata["langgraph_checkpoint_ns"]
                # Extract the first agent name before the colon
                if ":" in checkpoint_ns:
                    source_agent = checkpoint_ns.split(":")[0]

    # Also try to extract from namespace_tuple
    if source_agent == "unknown" and namespace_tuple:
        if isinstance(namespace_tuple, tuple) and len(namespace_tuple) > 0:
            first_ns = namespace_tuple[0]
            if isinstance(first_ns, str) and ":" in first_ns:
                source_agent = first_ns.split(":")[0]

    # Don't send MESSAGE event if delta is empty
    if not delta:
        return None

    result = {
        "type": EventType.MESSAGE.value,
        "delta": delta,
        "source_agent": source_agent,
    }
    # Don't collect legacy message events but add delegation_id
    _add_delegation_id(result)
    return result

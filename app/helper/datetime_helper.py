"""Helper functions for date and time formatting with timezone support."""

from datetime import datetime
from zoneinfo import ZoneInfo, ZoneInfoNotFoundError

# Timezone aliases for backward compatibility with legacy names
TIMEZONE_ALIASES = {
    "Asia/Calcutta": "Asia/Kolkata",
    "Calcutta": "Asia/Kolkata",
    "Kolkata": "Asia/Kolkata",
    # Add more aliases as needed
}


def normalize_timezone(timezone: str) -> str:
    """
    Normalize timezone string by resolving aliases.
    
    Args:
        timezone: Original timezone string
        
    Returns:
        Normalized timezone string
    """
    return TIMEZONE_ALIASES.get(timezone, timezone)


def get_current_datetime(timezone: str = "UTC") -> str:
    """
    Get current date and time in a user-friendly format according to the user's timezone.
    
    Args:
        timezone: User's timezone (e.g., "America/New_York", "Asia/Kolkata", "UTC")
                 Defaults to "UTC" if not provided or invalid.
    
    Returns:
        Formatted datetime string in format: "DayName, YYYY-MM-DD HH:MM (Timezone)"
        Example: "Monday, 2025-12-02 14:30 (America/New_York)"
    
    Examples:
        >>> get_current_datetime("America/New_York")
        "Monday, 2025-12-02 14:30 (America/New_York)"
        
        >>> get_current_datetime("Asia/Kolkata")
        "Monday, 2025-12-02 20:00 (Asia/Kolkata)"
        
        >>> get_current_datetime()  # defaults to UTC
        "Monday, 2025-12-02 19:30 (UTC)"
    """
    # Normalize timezone to handle legacy names
    normalized_tz = normalize_timezone(timezone)
    
    try:
        # Try to create ZoneInfo object with the normalized timezone
        tz = ZoneInfo(normalized_tz)
    except (ZoneInfoNotFoundError, KeyError):
        # Fallback to UTC if timezone is invalid
        tz = ZoneInfo("UTC")
        normalized_tz = "UTC"
    
    # Get current time in the specified timezone
    now = datetime.now(tz)
    
    # Format: "DayName, YYYY-MM-DD HH:MM (Timezone)"
    return now.strftime(f"%A, %Y-%m-%d %H:%M ({normalized_tz})")


def get_current_date(timezone: str = "UTC") -> str:
    """
    Get current date in YYYY-MM-DD format according to the user's timezone.
    
    Args:
        timezone: User's timezone (e.g., "America/New_York", "Asia/Kolkata", "UTC")
                 Defaults to "UTC" if not provided or invalid.
    
    Returns:
        Formatted date string in format: "YYYY-MM-DD"
        Example: "2025-12-02"
    
    Examples:
        >>> get_current_date("America/New_York")
        "2025-12-02"
        
        >>> get_current_date()
        "2025-12-02"
    """
    # Normalize timezone to handle legacy names
    normalized_tz = normalize_timezone(timezone)
    
    try:
        tz = ZoneInfo(normalized_tz)
    except (ZoneInfoNotFoundError, KeyError):
        tz = ZoneInfo("UTC")
    
    now = datetime.now(tz)
    return now.strftime("%Y-%m-%d")


def get_current_time(timezone: str = "UTC") -> str:
    """
    Get current time in HH:MM format according to the user's timezone.
    
    Args:
        timezone: User's timezone (e.g., "America/New_York", "Asia/Kolkata", "UTC")
                 Defaults to "UTC" if not provided or invalid.
    
    Returns:
        Formatted time string in format: "HH:MM"
        Example: "14:30"
    
    Examples:
        >>> get_current_time("America/New_York")
        "14:30"
        
        >>> get_current_time()
        "19:30"
    """
    # Normalize timezone to handle legacy names
    normalized_tz = normalize_timezone(timezone)
    
    try:
        tz = ZoneInfo(normalized_tz)
    except (ZoneInfoNotFoundError, KeyError):
        tz = ZoneInfo("UTC")
    
    now = datetime.now(tz)
    return now.strftime("%H:%M")

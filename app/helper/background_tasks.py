from typing import Optional
import logging

from app.agent.utils.summarization_utils import check_and_summarize
from app.utils.tracing import trace_operation

logger = logging.getLogger(__name__)


async def background_summarize(
    supervisor,
    conversation_id: str,
    user_id: str,
    organisation_id: str,
    agent_id: Optional[str],
    total_tokens: int,
):
    """
    Background task to check and summarize conversation.
    Runs without blocking the main stream and handles errors silently.
    """
    with trace_operation(
        "background.summarize",
        attributes={
            "conversation_id": conversation_id,
            "user_id": user_id,
            "agent_id": agent_id or "none",
            "total_tokens": total_tokens,
        },
    ) as span:
        try:
            await check_and_summarize(
                supervisor=supervisor,
                conversation_id=conversation_id,
                user_id=user_id,
                organisation_id=organisation_id,
                agent_id=agent_id,
                total_tokens=total_tokens,
            )
            span.set_attribute("success", True)
        except Exception as e:
            # Log error but don't raise to avoid affecting the main stream
            span.set_attribute("error", True)
            span.record_exception(e)
            logger.error(
                f"Background summarization failed for conversation {conversation_id}: {str(e)}"
            )

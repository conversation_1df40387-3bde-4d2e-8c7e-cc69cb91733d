"""
Helper functions for managing stop signals in Redis.

All state is stored in Redis to ensure horizontal scalability.
No local state is maintained.
"""
import logging

from app.services.redis_client import RedisClient

logger = logging.getLogger(__name__)


async def check_stop_signal(conversation_id: str, redis_client: RedisClient) -> bool:
    """
    Check if a stop signal has been issued for this conversation.
    
    Args:
        conversation_id: The conversation identifier
        redis_client: Shared Redis client instance
        
    Returns:
        True if stop signal exists, False otherwise
    """
    try:
        stop_key = f"stop_signal:{conversation_id}"
        await redis_client._ensure_connection()
        stop_signal = await redis_client.get_value(stop_key)
        
        # Check if signal exists and is truthy (not None, not empty string)
        if stop_signal:
            logger.warning(f"🛑 Stop signal detected for conversation {conversation_id}")
            return True
        
        return False
    except Exception as e:
        logger.error(f"Error checking stop signal for {conversation_id}: {e}")
        return False


async def clear_stop_signal(conversation_id: str, redis_client: RedisClient) -> None:
    """
    Clear the stop signal for a conversation.
    
    Args:
        conversation_id: The conversation identifier
        redis_client: Shared Redis client instance
    """
    try:
        stop_key = f"stop_signal:{conversation_id}"
        await redis_client._ensure_connection()
        await redis_client.delete(stop_key)
    except Exception:
        # Non-fatal: cleanup failure is not critical
        pass

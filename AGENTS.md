# Agent Platform Development Guide

## Build/Test Commands

**Dependencies:** `poetry install`

**Run single test:** `poetry run pytest tests/test_file.py::test_function -v`

**Run all tests:** `poetry run pytest --cov=app --cov-report=html --cov-report=term-missing`

**Test with asyncio:** `poetry run pytest -v` (asyncio_mode=auto configured)

**Quick test script:** `python scripts/test_global_agent.py`

## Code Style Guidelines

**Imports:** Use absolute imports from `app.` prefix, group stdlib, third-party, then local imports

**Formatting:** Follow PEP 8, use type hints consistently, max line length 88 characters

**Naming:** snake_case for variables/functions, PascalCase for classes, UPPER_CASE for constants

**Error Handling:** Use specific exceptions, include context in error messages, use async/await consistently

**Async Patterns:** All I/O operations should be async, use proper async context managers

**Logging:** Use structured logging from `app.shared.config.logging_config`, include context in logs

**Models:** Use Pydantic for data validation, define clear schemas with proper field types

**Tools:** Follow existing tool patterns in `app.agent.tools`, include proper error handling and type hints

NEVER EVER RUN GIT COMMANDS UNTIL USER TELLS TO


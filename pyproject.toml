[tool.poetry]
name = "agent-platform"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <a<PERSON><PERSON><PERSON><PERSON>@rapidinnovation.dev>"]
readme = "README.md"
packages = [{ include = "app" }]

[tool.poetry.dependencies]
python = "^3.11"
tiktoken = "^0.9.0"
openai = "^1.76.0"
python-dotenv = "1.0.0"
autogen-agentchat = "0.6.4"
mcp = "^1.6.0"
json-schema-to-pydantic = "^0.2.4"
aiohttp = "^3.11.18"
pydantic = "^2.11.3"
autogen-ext = {extras = ["mcp"], version = "^0.6.4"}
aiokafka = "^0.12.0"
redis = "^5.2.1"
# Remove the kafka package as we'll use aiokafka exclusively
asgiref = "^3.8.1"
anthropic = "^0.69.0"
python-slugify = "^8.0.4"
pinecone = "^7.0.1"
chromadb = "^1.0.12"
aiofiles = "^24.1.0"
pypdf = "^5.6.0"
python-docx = "^1.1.2"
docx2txt = "^0.9"
certifi = "^2025.6.15"
# textract = "^1.6.5"  # Commented out due to six version conflict
ollama = "^0.5.1"
six = ">=1.15.0,<2.0.0"
# Memory dependencies
mem0ai = "^0.1.30"
qdrant-client = "^1.12.1"
strands-agents-tools = "^0.2.5"
strands-agents-builder = "^0.1.8"
strands-agents = "1.10.0"
dirtyjson = "^1.0.8"
json5 = "^0.12.1"
json-repair = "^0.51.0"
apscheduler = "^3.11.0"
langgraph = "^1.0.0"
langchain-tavily = "^0.2.12"
langchain-openai = "^0.3.1"
langgraph-checkpoint-mongodb = "^0.2.1"
deepagents = "^0.2.7"
opentelemetry-distro = "^0.59b0"
opentelemetry-exporter-otlp = "^1.38.0"
opentelemetry-instrumentation-fastapi = "^0.59b0"
opentelemetry-instrumentation-pymongo = "^0.59b0"
opentelemetry-instrumentation-httpx = "^0.59b0"
opentelemetry-instrumentation-requests = "^0.59b0"
opentelemetry-instrumentation-logging = "^0.59b0"
psutil = "^7.1.3"
structlog = "^25.5.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
pytest-asyncio = "^0.23.5"
pytest-cov = "^4.1.0"
httpx = "^0.28.0"
coverage = "^7.4.1"

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
addopts = "-v --cov=app --cov-report=html --cov-report=term-missing"
pythonpath = ["."]
norecursedirs = ["app/workflow_chat", "app/workflow_generation_graph", ".git", ".tox", "dist", "build", "*.egg"]

[tool.coverage.run]
source = ["app"]
omit = [
    "app/workflow_chat/*",
    "app/workflow_generation_graph/*",
    "app/utils/update_vector_db.py",
    "app/utils/infrastructure.py",
    "app/services/redis_listener.py",
    "app/services/redis_listener.py.bak",
    "app/agent/prompts.py",
    "tests/*",
    "*/__pycache__/*",
    "*.pyc",
    # Infrastructure files that are difficult to unit test
    "app/utils/mongodb_session_manager.py",
    "app/shared/config/telemetry.py",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "@(abc\\.)?abstractmethod",
    "if TYPE_CHECKING:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

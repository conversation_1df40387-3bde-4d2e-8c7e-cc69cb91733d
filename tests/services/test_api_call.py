"""Tests for api_call.py"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import requests
from app.services.api_call import (
    HttpRequestHelper,
    HttpMethods,
    AuthType,
    ContentType,
)


class TestHttpRequestHelperInit:
    """Test HttpRequestHelper initialization"""

    def test_init_with_bearer_auth(self):
        """Test initialization with bearer authentication"""
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            auth_token="test-token",
            auth_type=AuthType.BEARER
        )
        
        assert helper.base_url == "https://api.example.com/"
        assert helper.auth_token == "test-token"
        assert helper.auth_type == AuthType.BEARER
        assert "Authorization" in helper.headers
        assert helper.headers["Authorization"] == "Bearer test-token"

    def test_init_with_api_key_auth(self):
        """Test initialization with API key authentication"""
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            auth_token="test-api-key",
            auth_type=AuthType.API_KEY,
            api_key_name="X-API-Key"
        )
        
        assert "X-API-Key" in helper.headers
        assert helper.headers["X-API-Key"] == "test-api-key"

    def test_init_with_basic_auth(self):
        """Test initialization with basic authentication"""
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            auth_token="encoded-credentials",
            auth_type=AuthType.BASIC
        )
        
        assert helper.headers["Authorization"] == "Basic encoded-credentials"

    def test_init_with_normal_auth(self):
        """Test initialization with normal authentication"""
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            auth_token="test-token",
            auth_type=AuthType.NORMAL
        )
        
        assert helper.headers["Authorization"] == "test-token"

    def test_init_with_custom_auth(self):
        """Test initialization with custom authentication"""
        def custom_auth(token):
            return {"X-Custom-Auth": f"Custom {token}"}
        
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            auth_token="test-token",
            auth_type=AuthType.CUSTOM,
            custom_auth_function=custom_auth
        )
        
        assert "X-Custom-Auth" in helper.headers
        assert helper.headers["X-Custom-Auth"] == "Custom test-token"

    def test_init_with_no_auth(self):
        """Test initialization without authentication"""
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            auth_type=AuthType.NONE
        )
        
        assert "Authorization" not in helper.headers

    def test_init_with_custom_headers(self):
        """Test initialization with custom headers"""
        custom_headers = {"X-Custom-Header": "custom-value"}
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            headers=custom_headers
        )
        
        assert "X-Custom-Header" in helper.headers
        assert helper.headers["X-Custom-Header"] == "custom-value"

    def test_init_with_string_auth_type(self):
        """Test initialization with string auth type"""
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            auth_token="test-token",
            auth_type="bearer"
        )
        
        assert helper.auth_type == AuthType.BEARER

    def test_init_with_string_content_type(self):
        """Test initialization with string content type"""
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            content_type="application/json"
        )
        
        assert helper.content_type == ContentType.JSON

    def test_base_url_normalization(self):
        """Test base URL normalization"""
        helper1 = HttpRequestHelper(base_url="https://api.example.com")
        helper2 = HttpRequestHelper(base_url="https://api.example.com/")
        
        assert helper1.base_url == "https://api.example.com/"
        assert helper2.base_url == "https://api.example.com/"

    def test_init_with_custom_timeout(self):
        """Test initialization with custom timeout"""
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            timeout=60
        )
        
        assert helper.timeout == 60

    def test_init_with_verify_ssl_false(self):
        """Test initialization with SSL verification disabled"""
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            verify_ssl=False
        )
        
        assert helper.verify_ssl is False


class TestHttpRequestHelperMakeRequest:
    """Test HttpRequestHelper make_request method"""

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_get_success(self, mock_request, mock_metrics):
        """Test successful GET request"""
        # Setup
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.json.return_value = {"data": "test"}
        mock_response.content = b'{"data": "test"}'
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute
        result = helper.get("/users")

        # Verify
        assert result == {"data": "test"}
        mock_request.assert_called_once()
        mock_metrics_manager.record_external_api_call.assert_called_once()

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_post_with_json(self, mock_request, mock_metrics):
        """Test POST request with JSON data"""
        # Setup
        mock_response = Mock()
        mock_response.status_code = 201
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.json.return_value = {"id": "123"}
        mock_response.content = b'{"id": "123"}'
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute
        result = helper.post("/users", json_data={"name": "John"})

        # Verify
        assert result == {"id": "123"}
        call_kwargs = mock_request.call_args[1]
        assert call_kwargs["json"] == {"name": "John"}

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_with_params(self, mock_request, mock_metrics):
        """Test request with URL parameters"""
        # Setup
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.json.return_value = {"results": []}
        mock_response.content = b'{"results": []}'
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute
        result = helper.get("/users", params={"page": 1, "limit": 10})

        # Verify
        call_kwargs = mock_request.call_args[1]
        assert call_kwargs["params"] == {"page": 1, "limit": 10}

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_with_files(self, mock_request, mock_metrics):
        """Test request with file upload"""
        # Setup
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.json.return_value = {"uploaded": True}
        mock_response.content = b'{"uploaded": True}'
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute
        files = {"file": ("test.txt", b"file content")}
        result = helper.post("/upload", files=files)

        # Verify
        call_kwargs = mock_request.call_args[1]
        assert call_kwargs["files"] == files
        # Content-Type should be removed for file uploads
        assert "Content-Type" not in call_kwargs["headers"] or \
               call_kwargs["headers"]["Content-Type"] != ContentType.JSON.value

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_return_raw_response(self, mock_request, mock_metrics):
        """Test returning raw response object"""
        # Setup
        mock_response = Mock()
        mock_response.status_code = 200
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute
        result = helper.get("/users", return_raw_response=True)

        # Verify
        assert result == mock_response

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_http_error(self, mock_request, mock_metrics):
        """Test handling HTTP error"""
        # Setup
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.text = "Not found"
        mock_response.raise_for_status.side_effect = requests.exceptions.HTTPError("404 Error")
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute & Verify
        with pytest.raises(requests.exceptions.HTTPError):
            helper.get("/users")

        mock_metrics_manager.record_external_api_error.assert_called_once()

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_connection_error(self, mock_request, mock_metrics):
        """Test handling connection error"""
        # Setup
        mock_request.side_effect = requests.exceptions.ConnectionError("Connection failed")

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute & Verify
        with pytest.raises(requests.exceptions.ConnectionError):
            helper.get("/users")

        mock_metrics_manager.record_external_api_error.assert_called_once()

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_timeout_error(self, mock_request, mock_metrics):
        """Test handling timeout error"""
        # Setup
        mock_request.side_effect = requests.exceptions.Timeout("Request timed out")

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute & Verify
        with pytest.raises(requests.exceptions.Timeout):
            helper.get("/users")

        mock_metrics_manager.record_external_api_error.assert_called_once()

    @patch("app.services.api_call.exception_tracker")
    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_generic_exception(self, mock_request, mock_metrics, mock_tracker):
        """Test handling generic exception"""
        # Setup
        mock_request.side_effect = Exception("Unexpected error")

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute & Verify
        with pytest.raises(Exception):
            helper.get("/users")

        mock_tracker.track_exception.assert_called_once()
        mock_metrics_manager.record_external_api_error.assert_called_once()

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_text_response(self, mock_request, mock_metrics):
        """Test processing text response"""
        # Setup
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "text/plain"}
        mock_response.text = "Plain text response"
        mock_response.content = b"Plain text response"
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute
        result = helper.get("/text")

        # Verify
        assert result == "Plain text response"

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_binary_response(self, mock_request, mock_metrics):
        """Test processing binary response"""
        # Setup
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "application/octet-stream"}
        mock_response.content = b"binary data"
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute
        result = helper.get("/binary")

        # Verify
        assert result == b"binary data"

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_make_request_empty_response(self, mock_request, mock_metrics):
        """Test processing empty response"""
        # Setup
        mock_response = Mock()
        mock_response.status_code = 204
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.content = b""
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")

        # Execute
        result = helper.delete("/users/123", expected_status_codes=[204])

        # Verify
        assert result == {}


class TestHttpRequestHelperConvenienceMethods:
    """Test HTTP method convenience methods"""

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_put_method(self, mock_request, mock_metrics):
        """Test PUT method"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.json.return_value = {"updated": True}
        mock_response.content = b'{"updated": True}'
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")
        result = helper.put("/users/123", json_data={"name": "Updated"})

        assert result == {"updated": True}

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_patch_method(self, mock_request, mock_metrics):
        """Test PATCH method"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Type": "application/json"}
        mock_response.json.return_value = {"patched": True}
        mock_response.content = b'{"patched": True}'
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")
        result = helper.patch("/users/123", json_data={"status": "active"})

        assert result == {"patched": True}

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_delete_method(self, mock_request, mock_metrics):
        """Test DELETE method"""
        mock_response = Mock()
        mock_response.status_code = 204
        mock_response.headers = {}
        mock_response.content = b""
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")
        result = helper.delete("/users/123", expected_status_codes=[204])

        assert result == {}

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_head_method(self, mock_request, mock_metrics):
        """Test HEAD method"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"Content-Length": "1234"}
        mock_response.content = b""
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")
        result = helper.head("/users")

        assert result == {}

    @patch("app.services.api_call.get_metrics_manager")
    @patch("app.services.api_call.requests.request")
    def test_options_method(self, mock_request, mock_metrics):
        """Test OPTIONS method"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.headers = {"Allow": "GET, POST, PUT, DELETE"}
        mock_response.content = b""
        mock_request.return_value = mock_response

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        helper = HttpRequestHelper(base_url="https://api.example.com")
        result = helper.options("/users")

        assert result == {}


class TestHttpRequestHelperUtilityMethods:
    """Test utility methods"""

    def test_update_auth(self):
        """Test updating authentication"""
        helper = HttpRequestHelper(
            base_url="https://api.example.com",
            auth_token="old-token",
            auth_type=AuthType.BEARER
        )

        helper.update_auth("new-token", AuthType.BEARER)

        assert helper.auth_token == "new-token"
        assert helper.headers["Authorization"] == "Bearer new-token"

    def test_set_header(self):
        """Test setting a header"""
        helper = HttpRequestHelper(base_url="https://api.example.com")

        helper.set_header("X-Custom", "value")

        assert helper.headers["X-Custom"] == "value"

    def test_set_content_type(self):
        """Test setting content type"""
        helper = HttpRequestHelper(base_url="https://api.example.com")

        helper.set_content_type(ContentType.XML)

        assert helper.content_type == ContentType.XML
        assert helper.headers["Content-Type"] == ContentType.XML.value


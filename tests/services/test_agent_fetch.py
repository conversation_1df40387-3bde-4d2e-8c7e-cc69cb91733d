"""Tests for agent_fetch.py"""
import pytest
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from app.services.agent_fetch import AgentFetchService, get_agent_fetch_service


class TestAgentFetchService:
    """Test suite for AgentFetchService"""

    def setup_method(self):
        """Reset singleton before each test"""
        AgentFetchService._instance = None
        AgentFetchService._initialized = False

    @patch("app.services.agent_fetch.get_settings")
    @patch("app.services.agent_fetch.HttpRequestHelper")
    def test_singleton_pattern(self, mock_http_helper, mock_get_settings):
        """Test that AgentFetchService is a singleton"""
        mock_settings = Mock()
        mock_settings.gateway.api_url = "https://api.example.com"
        mock_settings.gateway.api_key = "test-key"
        mock_get_settings.return_value = mock_settings
        
        service1 = AgentFetchService()
        service2 = AgentFetchService()
        
        assert service1 is service2
        assert mock_http_helper.call_count == 1  # Only initialized once

    @patch("app.services.agent_fetch.get_settings")
    @patch("app.services.agent_fetch.HttpRequestHelper")
    def test_initialization(self, mock_http_helper, mock_get_settings):
        """Test service initialization"""
        mock_settings = Mock()
        mock_settings.gateway.api_url = "https://api.example.com"
        mock_settings.gateway.api_key = "test-key"
        mock_get_settings.return_value = mock_settings
        
        service = AgentFetchService()
        
        assert service.settings == mock_settings
        mock_http_helper.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.agent_fetch.exception_tracker")
    @patch("app.services.agent_fetch.trace_operation")
    @patch("app.services.agent_fetch.get_settings")
    @patch("app.services.agent_fetch.HttpRequestHelper")
    async def test_fetch_agent_config_success(
        self, mock_http_helper, mock_get_settings, mock_trace, mock_exception_tracker
    ):
        """Test successful agent config fetch"""
        # Setup
        mock_settings = Mock()
        mock_settings.gateway.api_url = "https://api.example.com"
        mock_settings.gateway.api_key = "test-key"
        mock_get_settings.return_value = mock_settings
        
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span
        
        mock_http_instance = Mock()
        mock_http_instance.get.return_value = {"agent": {"id": "agent-123", "name": "Test Agent"}}
        mock_http_helper.return_value = mock_http_instance
        
        service = AgentFetchService()
        
        # Execute
        result = await service.fetch_agent_config("agent-123")
        
        # Verify
        assert result == {"id": "agent-123", "name": "Test Agent"}
        mock_http_instance.get.assert_called_once_with(endpoint="agents/agent-platform/agent-123")
        mock_span.set_attribute.assert_any_call("success", True)

    @pytest.mark.asyncio
    @patch("app.services.agent_fetch.exception_tracker")
    @patch("app.services.agent_fetch.trace_operation")
    @patch("app.services.agent_fetch.get_settings")
    @patch("app.services.agent_fetch.HttpRequestHelper")
    async def test_fetch_agent_config_error(
        self, mock_http_helper, mock_get_settings, mock_trace, mock_exception_tracker
    ):
        """Test agent config fetch with error"""
        # Setup
        mock_settings = Mock()
        mock_settings.gateway.api_url = "https://api.example.com"
        mock_settings.gateway.api_key = "test-key"
        mock_get_settings.return_value = mock_settings
        
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span
        
        mock_http_instance = Mock()
        mock_http_instance.get.side_effect = Exception("API Error")
        mock_http_helper.return_value = mock_http_instance
        
        service = AgentFetchService()
        
        # Execute & Verify
        with pytest.raises(Exception, match="API Error"):
            await service.fetch_agent_config("agent-123")
        
        mock_span.set_attribute.assert_called_with("error", True)
        mock_span.record_exception.assert_called_once()
        mock_exception_tracker.track_exception.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.agent_fetch.exception_tracker")
    @patch("app.services.agent_fetch.trace_operation")
    @patch("app.services.agent_fetch.get_settings")
    @patch("app.services.agent_fetch.HttpRequestHelper")
    async def test_fetch_mcps_by_ids_success(
        self, mock_http_helper, mock_get_settings, mock_trace, mock_exception_tracker
    ):
        """Test successful MCPs fetch"""
        # Setup
        mock_settings = Mock()
        mock_settings.gateway.api_url = "https://api.example.com"
        mock_settings.gateway.api_key = "test-key"
        mock_get_settings.return_value = mock_settings
        
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span
        
        mock_http_instance = Mock()
        mock_http_instance.post.return_value = {"mcps": [{"id": "mcp-1"}, {"id": "mcp-2"}]}
        mock_http_helper.return_value = mock_http_instance
        
        service = AgentFetchService()
        
        # Execute
        result = await service.fetch_mcps_by_ids(["mcp-1", "mcp-2"])
        
        # Verify
        assert result == [{"id": "mcp-1"}, {"id": "mcp-2"}]
        mock_http_instance.post.assert_called_once_with(
            endpoint="mcps/agent-platform/by-ids",
            data={"ids": ["mcp-1", "mcp-2"]}
        )
        mock_span.set_attribute.assert_any_call("mcps_count", 2)
        mock_span.set_attribute.assert_any_call("success", True)

    @pytest.mark.asyncio
    @patch("app.services.agent_fetch.exception_tracker")
    @patch("app.services.agent_fetch.trace_operation")
    @patch("app.services.agent_fetch.get_settings")
    @patch("app.services.agent_fetch.HttpRequestHelper")
    async def test_fetch_mcps_by_ids_error(
        self, mock_http_helper, mock_get_settings, mock_trace, mock_exception_tracker
    ):
        """Test MCPs fetch with error"""
        # Setup
        mock_settings = Mock()
        mock_settings.gateway.api_url = "https://api.example.com"
        mock_settings.gateway.api_key = "test-key"
        mock_get_settings.return_value = mock_settings
        
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span
        
        mock_http_instance = Mock()
        mock_http_instance.post.side_effect = Exception("API Error")
        mock_http_helper.return_value = mock_http_instance
        
        service = AgentFetchService()
        
        # Execute & Verify
        with pytest.raises(Exception, match="API Error"):
            await service.fetch_mcps_by_ids(["mcp-1"])
        
        mock_span.set_attribute.assert_called_with("error", True)
        mock_exception_tracker.track_exception.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.agent_fetch.exception_tracker")
    @patch("app.services.agent_fetch.trace_operation")
    @patch("app.services.agent_fetch.get_settings")
    @patch("app.services.agent_fetch.HttpRequestHelper")
    async def test_fetch_workflows_by_ids_success(
        self, mock_http_helper, mock_get_settings, mock_trace, mock_exception_tracker
    ):
        """Test successful workflows fetch"""
        # Setup
        mock_settings = Mock()
        mock_settings.gateway.api_url = "https://api.example.com"
        mock_settings.gateway.api_key = "test-key"
        mock_get_settings.return_value = mock_settings

        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_http_instance = Mock()
        mock_http_instance.post.return_value = {"workflows": [{"id": "wf-1"}, {"id": "wf-2"}]}
        mock_http_helper.return_value = mock_http_instance

        service = AgentFetchService()

        # Execute
        result = await service.fetch_workflows_by_ids(["wf-1", "wf-2"])

        # Verify
        assert result == [{"id": "wf-1"}, {"id": "wf-2"}]
        mock_http_instance.post.assert_called_once_with(
            endpoint="workflows/agent-platform/by-ids",
            data={"ids": ["wf-1", "wf-2"]}
        )
        mock_span.set_attribute.assert_any_call("workflows_count", 2)
        mock_span.set_attribute.assert_any_call("success", True)

    @pytest.mark.asyncio
    @patch("app.services.agent_fetch.exception_tracker")
    @patch("app.services.agent_fetch.trace_operation")
    @patch("app.services.agent_fetch.get_settings")
    @patch("app.services.agent_fetch.HttpRequestHelper")
    async def test_fetch_workflows_by_ids_error(
        self, mock_http_helper, mock_get_settings, mock_trace, mock_exception_tracker
    ):
        """Test workflows fetch with error"""
        # Setup
        mock_settings = Mock()
        mock_settings.gateway.api_url = "https://api.example.com"
        mock_settings.gateway.api_key = "test-key"
        mock_get_settings.return_value = mock_settings

        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_http_instance = Mock()
        mock_http_instance.post.side_effect = Exception("API Error")
        mock_http_helper.return_value = mock_http_instance

        service = AgentFetchService()

        # Execute & Verify
        with pytest.raises(Exception, match="API Error"):
            await service.fetch_workflows_by_ids(["wf-1"])

        mock_span.set_attribute.assert_called_with("error", True)
        mock_exception_tracker.track_exception.assert_called_once()

    @patch("app.services.agent_fetch.get_settings")
    @patch("app.services.agent_fetch.HttpRequestHelper")
    def test_get_agent_fetch_service(self, mock_http_helper, mock_get_settings):
        """Test get_agent_fetch_service function"""
        mock_settings = Mock()
        mock_settings.gateway.api_url = "https://api.example.com"
        mock_settings.gateway.api_key = "test-key"
        mock_get_settings.return_value = mock_settings

        service = get_agent_fetch_service()

        assert isinstance(service, AgentFetchService)


"""Tests for redis_streams.py"""
import pytest
import json
import time
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from app.services.redis_streams import (
    StreamMessage,
    RedisStreamsProducer,
    RedisStreamsWorkflowProducer,
    RedisStreamsConsumer,
    RedisStreamsManager,
    RedisStreamsResponseReader,
)


class TestStreamMessage:
    """Test suite for StreamMessage dataclass"""

    def test_stream_message_creation(self):
        """Test StreamMessage creation"""
        msg = StreamMessage(
            id="123-0",
            fields={"data": "test"},
            stream="test-stream"
        )
        assert msg.id == "123-0"
        assert msg.fields == {"data": "test"}
        assert msg.stream == "test-stream"
        assert msg.timestamp is not None

    def test_stream_message_with_timestamp(self):
        """Test StreamMessage with explicit timestamp"""
        ts = time.time()
        msg = StreamMessage(
            id="123-0",
            fields={"data": "test"},
            stream="test-stream",
            timestamp=ts
        )
        assert msg.timestamp == ts


class TestRedisStreamsProducer:
    """Test suite for RedisStreamsProducer"""

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_send_message_success(self, mock_redis_client, mock_get_settings):
        """Test successful message send"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings
        
        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="123-0")
        mock_redis_client.return_value = mock_client_instance
        
        producer = RedisStreamsProducer(redis_client=mock_client_instance)
        
        result = await producer.send_message(
            stream="test-stream",
            fields={"data": "test"}
        )
        
        assert result == "123-0"
        mock_client_instance.xadd.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_send_message_adds_timestamp(self, mock_redis_client, mock_get_settings):
        """Test that send_message adds timestamp if not present"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings
        
        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="123-0")
        mock_redis_client.return_value = mock_client_instance
        
        producer = RedisStreamsProducer(redis_client=mock_client_instance)
        
        fields = {"data": "test"}
        await producer.send_message(stream="test-stream", fields=fields)
        
        # Check that timestamp was added
        assert "timestamp" in fields

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_send_message_error(self, mock_redis_client, mock_get_settings):
        """Test send_message with error"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings
        
        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(side_effect=Exception("Redis error"))
        mock_redis_client.return_value = mock_client_instance
        
        producer = RedisStreamsProducer(redis_client=mock_client_instance)
        
        with pytest.raises(Exception, match="Redis error"):
            await producer.send_message(stream="test-stream", fields={"data": "test"})

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_send_agent_request(self, mock_redis_client, mock_get_settings):
        """Test send_agent_request"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings
        
        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="123-0")
        mock_redis_client.return_value = mock_client_instance
        
        producer = RedisStreamsProducer(redis_client=mock_client_instance)
        
        result = await producer.send_agent_request(
            conversation_id="conv-123",
            message="Hello",
            user_id="user-123"
        )
        
        assert result == "123-0"
        mock_client_instance.xadd.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_send_agent_response(self, mock_redis_client, mock_get_settings):
        """Test send_agent_response"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings
        
        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="123-0")
        mock_redis_client.return_value = mock_client_instance
        
        producer = RedisStreamsProducer(redis_client=mock_client_instance)
        
        result = await producer.send_agent_response(
            conversation_id="conv-123",
            response_data={"message": "Response"},
            request_id="req-123"
        )
        
        assert result == "123-0"

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_send_stop_request(self, mock_redis_client, mock_get_settings):
        """Test send_stop_request"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="123-0")
        mock_redis_client.return_value = mock_client_instance

        producer = RedisStreamsProducer(redis_client=mock_client_instance)

        result = await producer.send_stop_request(conversation_id="conv-123")

        assert result == "123-0"
        mock_client_instance.xadd.assert_called_once()


class TestRedisStreamsWorkflowProducer:
    """Test suite for RedisStreamsWorkflowProducer"""

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_send_message_success(self, mock_redis_client, mock_get_settings):
        """Test successful message send"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="123-0")
        mock_redis_client.return_value = mock_client_instance

        producer = RedisStreamsWorkflowProducer(redis_client=mock_client_instance)

        result = await producer.send_message(
            stream="test-stream",
            fields={"data": "test"}
        )

        assert result == "123-0"

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_send_agent_request(self, mock_redis_client, mock_get_settings):
        """Test send_agent_request"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="123-0")
        mock_redis_client.return_value = mock_client_instance

        producer = RedisStreamsWorkflowProducer(redis_client=mock_client_instance)

        result = await producer.send_agent_request(
            message="Hello",
            user_id="user-123"
        )

        assert result == "123-0"

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_send_agent_response(self, mock_redis_client, mock_get_settings):
        """Test send_agent_response"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="123-0")
        mock_redis_client.return_value = mock_client_instance

        producer = RedisStreamsWorkflowProducer(redis_client=mock_client_instance)

        result = await producer.send_agent_response(
            response_data={"message": "Response"},
            request_id="req-123",
            chat=True
        )

        assert result == "123-0"
        mock_client_instance.xadd.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_send_memory_request(self, mock_redis_client, mock_get_settings):
        """Test send_memory_request"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings
        
        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="123-0")
        mock_redis_client.return_value = mock_client_instance
        
        producer = RedisStreamsProducer(redis_client=mock_client_instance)
        
        result = await producer.send_memory_request(
            text="Remember this",
            user_id="user-123",
            conversation_id="conv-123",
            agent_id="agent-123",
            organisation_id="org-123",
            additional_metadata={"key": "value"}
        )
        
        assert result == "123-0"


class TestRedisStreamsConsumer:
    """Test suite for RedisStreamsConsumer"""

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_ensure_consumer_group_success(self, mock_redis_client, mock_get_settings):
        """Test ensuring consumer group exists"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xgroup_create = AsyncMock()
        mock_redis_client.return_value = mock_client_instance

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_client_instance
        )

        await consumer.ensure_consumer_group("test-stream")

        mock_client_instance.xgroup_create.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_ensure_consumer_group_already_exists(self, mock_redis_client, mock_get_settings):
        """Test ensuring consumer group when it already exists"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xgroup_create = AsyncMock(
            side_effect=Exception("BUSYGROUP Consumer Group name already exists")
        )
        mock_redis_client.return_value = mock_client_instance

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_client_instance
        )

        # Should not raise exception
        await consumer.ensure_consumer_group("test-stream")

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_handle_processing_error_retry(self, mock_redis_client, mock_get_settings):
        """Test error handling with retry logic"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_client_instance

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_client_instance
        )

        message = StreamMessage(
            id="123-0",
            fields={"data": "test"},
            stream="test-stream"
        )

        # First retry should return True
        result = await consumer.handle_processing_error(message, Exception("Test error"))
        assert result is True

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_handle_processing_error_max_retries(self, mock_redis_client, mock_get_settings):
        """Test error handling when max retries exceeded"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xack = AsyncMock()
        mock_redis_client.return_value = mock_client_instance

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_client_instance
        )

        message = StreamMessage(
            id="123-0",
            fields={"data": "test"},
            stream="test-stream"
        )

        # Simulate max retries
        consumer._retry_count[f"{message.stream}:{message.id}"] = 3

        result = await consumer.handle_processing_error(message, Exception("Test error"))
        assert result is False

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_acknowledge_message(self, mock_redis_client, mock_get_settings):
        """Test acknowledging a message"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xack = AsyncMock(return_value=1)
        mock_redis_client.return_value = mock_client_instance

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_client_instance
        )

        result = await consumer.acknowledge_message("test-stream", "123-0")
        assert result == 1

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_stop_consumer(self, mock_redis_client, mock_get_settings):
        """Test stopping the consumer"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_client_instance

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_client_instance
        )

        consumer._running = True
        consumer.stop()
        assert consumer._running is False


class TestRedisStreamsManager:
    """Test suite for RedisStreamsManager"""

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_manager_initialization(self, mock_redis_client, mock_get_settings):
        """Test manager initialization"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_client_instance

        manager = RedisStreamsManager(redis_client=mock_client_instance)

        assert manager.producer is not None
        assert manager.workflow_producer is not None
        assert manager.get_consumer("test-group", "consumer-1") is not None

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_move_to_dead_letter(self, mock_redis_client, mock_get_settings):
        """Test moving message to dead letter queue"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="123-0")
        mock_redis_client.return_value = mock_client_instance

        manager = RedisStreamsManager(redis_client=mock_client_instance)

        message = StreamMessage(
            id="123-0",
            fields={"data": "test"},
            stream="test-stream"
        )

        await manager.move_to_dead_letter("test-stream", message, "Test error")

        # Should be called twice: once for initialization, once for the actual message
        assert mock_client_instance.xadd.call_count == 2

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_manager_initialize(self, mock_redis_client, mock_get_settings):
        """Test manager initialize method"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.initialize = AsyncMock()
        mock_redis_client.return_value = mock_client_instance

        manager = RedisStreamsManager(redis_client=mock_client_instance)

        await manager.initialize()

        assert manager._initialized is True
        mock_client_instance.initialize.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_manager_close(self, mock_redis_client, mock_get_settings):
        """Test manager close method"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.close = AsyncMock()
        mock_redis_client.return_value = mock_client_instance

        manager = RedisStreamsManager(redis_client=mock_client_instance)
        manager._initialized = True

        await manager.close()

        assert manager._initialized is False
        mock_client_instance.close.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_get_stream_info(self, mock_redis_client, mock_get_settings):
        """Test getting stream info"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xinfo_stream = AsyncMock(return_value={"length": 100})
        mock_redis_client.return_value = mock_client_instance

        manager = RedisStreamsManager(redis_client=mock_client_instance)

        result = await manager.get_stream_info("test-stream")

        assert result["length"] == 100

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_cleanup_old_streams(self, mock_redis_client, mock_get_settings):
        """Test cleanup old streams"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_client_instance

        manager = RedisStreamsManager(redis_client=mock_client_instance)

        # Should not raise exception
        await manager.cleanup_old_streams(max_age_hours=24)

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_consumer_consume_messages_with_results(self, mock_redis_client, mock_get_settings):
        """Test consumer consume_messages with actual results"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xgroup_create = AsyncMock()

        # First call returns messages, second call returns empty to stop
        call_count = 0
        async def xreadgroup_side_effect(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:
                return {
                    "test-stream": [
                        ("msg-1", {"data": "test1", "timestamp": "123"})
                    ]
                }
            return {}

        mock_client_instance.xreadgroup = AsyncMock(side_effect=xreadgroup_side_effect)
        mock_redis_client.return_value = mock_client_instance

        consumer = RedisStreamsConsumer(
            redis_client=mock_client_instance,
            group="test-group",
            consumer_name="consumer-1"
        )

        # Consume messages
        messages = []
        async for message in consumer.consume_messages(["test-stream"], count=1, block=100):
            messages.append(message)
            consumer.stop()
            break

        # Verify
        assert len(messages) == 1
        assert messages[0].id == "msg-1"

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_workflow_producer_send_agent_request(self, mock_redis_client, mock_get_settings):
        """Test workflow producer send_agent_request"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xadd = AsyncMock(return_value="msg-123")
        mock_redis_client.return_value = mock_client_instance

        producer = RedisStreamsWorkflowProducer(redis_client=mock_client_instance)

        # Send agent request
        message_id = await producer.send_agent_request(
            message="Hello",
            request_id="req-123"
        )

        # Verify
        assert message_id == "msg-123"
        mock_client_instance.xadd.assert_called_once()


class TestRedisStreamsConsumerErrorHandling:
    """Test RedisStreamsConsumer error handling"""

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_ensure_consumer_group_success(self, mock_redis_client, mock_get_settings):
        """Test successful consumer group creation"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance
        mock_redis_client_instance.xgroup_create = AsyncMock()

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_redis_client_instance
        )

        await consumer.ensure_consumer_group("test-stream")

        mock_redis_client_instance.xgroup_create.assert_called_once_with(
            "test-stream", "test-group", id="0", mkstream=True
        )

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_ensure_consumer_group_already_exists(self, mock_redis_client, mock_get_settings):
        """Test consumer group already exists (BUSYGROUP error)"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance
        mock_redis_client_instance.xgroup_create = AsyncMock(
            side_effect=Exception("BUSYGROUP Consumer Group name already exists")
        )

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_redis_client_instance
        )

        # Should not raise exception for BUSYGROUP error
        await consumer.ensure_consumer_group("test-stream")

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_ensure_consumer_group_other_error(self, mock_redis_client, mock_get_settings):
        """Test consumer group creation with other error"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance
        mock_redis_client_instance.xgroup_create = AsyncMock(
            side_effect=Exception("Connection error")
        )

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_redis_client_instance
        )

        # Should raise exception for non-BUSYGROUP errors
        with pytest.raises(Exception):
            await consumer.ensure_consumer_group("test-stream")

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_handle_processing_error_retry(self, mock_redis_client, mock_get_settings):
        """Test error handling with retry logic"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_redis_client_instance
        )

        message = StreamMessage(
            id="123-0",
            fields={"data": "test"},
            stream="test-stream"
        )

        # First retry should return True (retry)
        result = await consumer.handle_processing_error(message, Exception("Test error"))
        assert result is True

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_handle_processing_error_max_retries(self, mock_redis_client, mock_get_settings):
        """Test error handling when max retries exceeded"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_redis_client_instance
        )

        message = StreamMessage(
            id="123-0",
            fields={"data": "test"},
            stream="test-stream"
        )

        # Simulate max retries
        consumer._retry_count[f"{message.stream}:{message.id}"] = 3

        # Mock manager
        mock_manager = AsyncMock()
        mock_manager.move_to_dead_letter = AsyncMock()

        # Should return False (no more retries)
        result = await consumer.handle_processing_error(message, Exception("Test error"), mock_manager)
        assert result is False
        mock_manager.move_to_dead_letter.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_acknowledge_message_success(self, mock_redis_client, mock_get_settings):
        """Test successful message acknowledgment"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance
        mock_redis_client_instance.xack = AsyncMock(return_value=1)

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_redis_client_instance
        )

        result = await consumer.acknowledge_message("test-stream", "123-0")

        assert result is True
        mock_redis_client_instance.xack.assert_called_once_with("test-stream", "test-group", "123-0")

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_acknowledge_message_failure(self, mock_redis_client, mock_get_settings):
        """Test message acknowledgment failure"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance
        mock_redis_client_instance.xack = AsyncMock(side_effect=Exception("Connection error"))

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_redis_client_instance
        )

        result = await consumer.acknowledge_message("test-stream", "123-0")

        assert result is False

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_consumer_stop(self, mock_redis_client, mock_get_settings):
        """Test stopping consumer"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance

        consumer = RedisStreamsConsumer(
            group="test-group",
            consumer_name="consumer-1",
            redis_client=mock_redis_client_instance
        )

        consumer._running = True
        consumer.stop()

        assert consumer._running is False


class TestRedisStreamsResponseReader:
    """Test RedisStreamsResponseReader"""

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_read_responses_success(self, mock_redis_client, mock_get_settings):
        """Test reading responses successfully"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance

        # Mock xread response
        mock_redis_client_instance.xread = AsyncMock(return_value={
            "test:agent:responses:conv123": [
                ("123-0", {"data": "response1", "timestamp": "1234567890"}),
                ("123-1", {"data": "response2", "timestamp": "1234567891"})
            ]
        })

        reader = RedisStreamsResponseReader(redis_client=mock_redis_client_instance)

        messages = await reader.read_responses("conv123", last_id="0", count=10)

        assert len(messages) == 2
        assert messages[0].id == "123-0"
        assert messages[0].fields["data"] == "response1"
        assert messages[1].id == "123-1"

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_read_responses_error(self, mock_redis_client, mock_get_settings):
        """Test reading responses with error"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance
        mock_redis_client_instance.xread = AsyncMock(side_effect=Exception("Connection error"))

        reader = RedisStreamsResponseReader(redis_client=mock_redis_client_instance)

        messages = await reader.read_responses("conv123")

        assert messages == []


class TestRedisStreamsConsumerErrorHandling:
    """Test suite for RedisStreamsConsumer error handling"""

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_acknowledge_message_success(self, mock_redis_client, mock_get_settings):
        """Test successful message acknowledgment"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance
        mock_redis_client_instance.xack = AsyncMock(return_value=1)

        consumer = RedisStreamsConsumer(
            redis_client=mock_redis_client_instance,
            group="test-group",
            consumer_name="test-consumer"
        )

        result = await consumer.acknowledge_message("test-stream", "123-0")

        assert result is True
        mock_redis_client_instance.xack.assert_called_once_with("test-stream", "test-group", "123-0")

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_acknowledge_message_failure(self, mock_redis_client, mock_get_settings):
        """Test message acknowledgment failure"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance
        mock_redis_client_instance.xack = AsyncMock(return_value=0)

        consumer = RedisStreamsConsumer(
            redis_client=mock_redis_client_instance,
            group="test-group",
            consumer_name="test-consumer"
        )

        result = await consumer.acknowledge_message("test-stream", "123-0")

        assert result is False

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_acknowledge_message_error(self, mock_redis_client, mock_get_settings):
        """Test message acknowledgment with error"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance
        mock_redis_client_instance.xack = AsyncMock(side_effect=Exception("Redis error"))

        consumer = RedisStreamsConsumer(
            redis_client=mock_redis_client_instance,
            group="test-group",
            consumer_name="test-consumer"
        )

        result = await consumer.acknowledge_message("test-stream", "123-0")

        assert result is False

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_stop_consumer(self, mock_redis_client, mock_get_settings):
        """Test stopping consumer"""
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance

        consumer = RedisStreamsConsumer(
            redis_client=mock_redis_client_instance,
            group="test-group",
            consumer_name="test-consumer"
        )

        # Initially _running is False
        assert consumer._running is False

        # Manually set it to True to simulate running state
        consumer._running = True
        assert consumer._running is True

        # Now stop it
        consumer.stop()
        assert consumer._running is False

    @pytest.mark.asyncio
    @patch("app.services.redis_streams.get_settings")
    @patch("app.services.redis_streams.RedisClient")
    async def test_consume_agent_requests(self, mock_redis_client, mock_get_settings):
        """Test consume_agent_requests method"""
        # Setup
        mock_settings = Mock()
        mock_settings.environment = "test"
        mock_settings.redis.consumer_group = "test-group"
        mock_settings.redis.consumer_name = "test-consumer"
        mock_get_settings.return_value = mock_settings

        mock_redis_client_instance = AsyncMock()
        mock_redis_client.return_value = mock_redis_client_instance

        # Mock consume_messages to yield one message then stop
        async def mock_consume_messages(streams, count=None):
            yield StreamMessage(
                id="123-0",
                fields={"data": "test"},
                stream="test:agent:requests",
                timestamp="2024-01-01T00:00:00"
            )

        consumer = RedisStreamsConsumer(mock_redis_client_instance, mock_settings)
        consumer.consume_messages = mock_consume_messages

        # Execute
        messages = []
        async for message in consumer.consume_agent_requests(count=1):
            messages.append(message)
            break  # Only get one message

        # Verify
        assert len(messages) == 1
        assert messages[0].id == "123-0"



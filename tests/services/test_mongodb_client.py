"""
Tests for app/services/mongodb_client.py
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from app.services.mongodb_client import (
    initialize_mongodb_client,
    get_mongodb_client,
    get_mongodb_saver,
    close_mongodb_client,
    _mongodb_client,
    _mongodb_saver,
)


class TestMongoDBClient:
    """Test MongoDB client management functions"""

    def setup_method(self):
        """Reset global state before each test"""
        import app.services.mongodb_client as module
        module._mongodb_client = None
        module._mongodb_saver = None

    def teardown_method(self):
        """Clean up after each test"""
        import app.services.mongodb_client as module
        module._mongodb_client = None
        module._mongodb_saver = None

    @patch("app.services.mongodb_client.MongoClient")
    @patch("app.services.mongodb_client.get_settings")
    @patch("app.services.mongodb_client.logger")
    def test_initialize_mongodb_client_success(self, mock_logger, mock_get_settings, mock_mongo_client):
        """Test successful MongoDB client initialization"""
        # Setup
        mock_settings = Mock()
        mock_settings.mongodb.url = "**************************************"
        mock_get_settings.return_value = mock_settings
        mock_client = Mock()
        mock_mongo_client.return_value = mock_client

        # Execute
        initialize_mongodb_client()

        # Verify
        mock_mongo_client.assert_called_once_with("**************************************")
        assert mock_logger.info.call_count >= 2

    @patch("app.services.mongodb_client.MongoClient")
    @patch("app.services.mongodb_client.get_settings")
    @patch("app.services.mongodb_client.logger")
    def test_initialize_mongodb_client_already_initialized(self, mock_logger, mock_get_settings, mock_mongo_client):
        """Test initialization when client already exists"""
        # Setup - initialize once
        mock_settings = Mock()
        mock_settings.mongodb.url = "mongodb://localhost:27017/db"
        mock_get_settings.return_value = mock_settings
        mock_client = Mock()
        mock_mongo_client.return_value = mock_client
        
        initialize_mongodb_client()
        
        # Execute - try to initialize again
        initialize_mongodb_client()

        # Verify - should only create client once
        assert mock_mongo_client.call_count == 1
        mock_logger.warning.assert_called_with("MongoDB client already initialized")

    @patch("app.services.mongodb_client.initialize_mongodb_client")
    def test_get_mongodb_client_lazy_initialization(self, mock_initialize):
        """Test lazy initialization when getting client"""
        mock_client = Mock()
        
        def set_client():
            import app.services.mongodb_client as module
            module._mongodb_client = mock_client
        
        mock_initialize.side_effect = set_client

        # Execute
        result = get_mongodb_client()

        # Verify
        mock_initialize.assert_called_once()
        assert result == mock_client

    @patch("app.services.mongodb_client.MongoClient")
    @patch("app.services.mongodb_client.get_settings")
    def test_get_mongodb_client_already_initialized(self, mock_get_settings, mock_mongo_client):
        """Test getting client when already initialized"""
        # Setup
        mock_settings = Mock()
        mock_settings.mongodb.url = "mongodb://localhost:27017/db"
        mock_get_settings.return_value = mock_settings
        mock_client = Mock()
        mock_mongo_client.return_value = mock_client
        
        initialize_mongodb_client()

        # Execute
        result = get_mongodb_client()

        # Verify
        assert result == mock_client

    @patch("app.services.mongodb_client.MongoDBSaver")
    @patch("app.services.mongodb_client.get_mongodb_client")
    @patch("app.services.mongodb_client.logger")
    def test_get_mongodb_saver_creates_new(self, mock_logger, mock_get_client, mock_saver_class):
        """Test creating new MongoDBSaver instance"""
        # Setup
        mock_client = Mock()
        mock_get_client.return_value = mock_client
        mock_saver = Mock()
        mock_saver_class.return_value = mock_saver

        # Execute
        result = get_mongodb_saver()

        # Verify
        mock_saver_class.assert_called_once_with(mock_client)
        assert result == mock_saver
        mock_logger.info.assert_called_with("MongoDBSaver initialized")

    @patch("app.services.mongodb_client.MongoDBSaver")
    @patch("app.services.mongodb_client.get_mongodb_client")
    def test_get_mongodb_saver_returns_existing(self, mock_get_client, mock_saver_class):
        """Test returning existing MongoDBSaver instance"""
        # Setup
        mock_client = Mock()
        mock_get_client.return_value = mock_client
        mock_saver = Mock()
        mock_saver_class.return_value = mock_saver
        
        # First call creates saver
        first_result = get_mongodb_saver()
        
        # Execute - second call should return same instance
        second_result = get_mongodb_saver()

        # Verify
        assert mock_saver_class.call_count == 1
        assert first_result == second_result

    @patch("app.services.mongodb_client.logger")
    def test_close_mongodb_client_success(self, mock_logger):
        """Test closing MongoDB client"""
        # Setup
        import app.services.mongodb_client as module
        mock_client = Mock()
        module._mongodb_client = mock_client

        # Execute
        close_mongodb_client()

        # Verify
        mock_client.close.assert_called_once()
        assert module._mongodb_client is None
        assert module._mongodb_saver is None

    @patch("app.services.mongodb_client.logger")
    def test_close_mongodb_client_not_initialized(self, mock_logger):
        """Test closing when client not initialized"""
        # Execute
        close_mongodb_client()

        # Verify
        mock_logger.warning.assert_called_with("MongoDB client already closed or never initialized")


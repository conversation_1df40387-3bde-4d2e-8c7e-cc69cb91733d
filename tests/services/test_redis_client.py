"""Tests for redis_client.py"""
import pytest
import json
from unittest.mock import Mock, patch, AsyncMock, MagicMock
from app.services.redis_client import RedisClient


class TestRedisClient:
    """Test suite for RedisClient"""

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_initialize(self, mock_redis, mock_get_settings):
        """Test Redis client initialization"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings
        
        client = RedisClient()
        await client.initialize()
        
        assert client._connection_initialized is True
        mock_redis.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_initialize_only_once(self, mock_redis, mock_get_settings):
        """Test that initialize only creates connection once"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings
        
        client = RedisClient()
        await client.initialize()
        await client.initialize()
        
        assert mock_redis.call_count == 1

    def test_serialize_value_dict(self):
        """Test serializing dict value"""
        client = RedisClient()
        result = client.serialize_value({"key": "value"})
        assert result == '{"key": "value"}'

    def test_serialize_value_list(self):
        """Test serializing list value"""
        client = RedisClient()
        result = client.serialize_value([1, 2, 3])
        assert result == '[1, 2, 3]'

    def test_serialize_value_string(self):
        """Test serializing string value"""
        client = RedisClient()
        result = client.serialize_value("test")
        assert result == "test"

    def test_deserialize_value_json(self):
        """Test deserializing JSON value"""
        client = RedisClient()
        result = client.deserialize_value('{"key": "value"}')
        assert result == {"key": "value"}

    def test_deserialize_value_string(self):
        """Test deserializing plain string value"""
        client = RedisClient()
        result = client.deserialize_value("test")
        assert result == "test"

    def test_deserialize_value_empty(self):
        """Test deserializing empty value"""
        client = RedisClient()
        result = client.deserialize_value("", default_type=None)
        assert result is None

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_set_hash(self, mock_redis, mock_get_settings):
        """Test setting hash in Redis"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings
        
        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance
        
        client = RedisClient()
        await client.initialize()
        
        await client.set_hash("test-key", {"field": "value"})
        
        mock_client_instance.hset.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_set_hash_with_ttl(self, mock_redis, mock_get_settings):
        """Test setting hash with TTL"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings
        
        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance
        
        client = RedisClient()
        await client.initialize()
        
        await client.set_hash("test-key", {"field": "value"}, ttl=3600)
        
        mock_client_instance.hset.assert_called_once()
        mock_client_instance.expire.assert_called_once_with("test-key", 3600)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_get_hash(self, mock_redis, mock_get_settings):
        """Test getting hash from Redis"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings
        
        mock_client_instance = AsyncMock()
        mock_client_instance.hgetall = AsyncMock(return_value={"field": "value"})
        mock_redis.return_value = mock_client_instance
        
        client = RedisClient()
        await client.initialize()
        
        result = await client.get_hash("test-key")
        
        assert result == {"field": "value"}
        mock_client_instance.hgetall.assert_called_once_with("test-key")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_exists_true(self, mock_redis, mock_get_settings):
        """Test checking if key exists (returns True)"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.exists = AsyncMock(return_value=1)
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.exists("test-key")

        assert result is True

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_exists_false(self, mock_redis, mock_get_settings):
        """Test checking if key exists (returns False)"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.exists = AsyncMock(return_value=0)
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.exists("test-key")

        assert result is False

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_delete(self, mock_redis, mock_get_settings):
        """Test deleting key from Redis"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.delete = AsyncMock(return_value=1)
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.delete("test-key")

        assert result == 1
        mock_client_instance.delete.assert_called_once_with("test-key")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_set_hash_error(self, mock_redis, mock_get_settings):
        """Test set_hash with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.hset = AsyncMock(side_effect=Exception("Redis error"))
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception, match="Redis error"):
            await client.set_hash("test-key", {"field": "value"})

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_get_hash_error(self, mock_redis, mock_get_settings):
        """Test get_hash with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.hgetall = AsyncMock(side_effect=Exception("Redis error"))
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception, match="Redis error"):
            await client.get_hash("test-key")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_exists_error(self, mock_redis, mock_get_settings):
        """Test exists with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.exists = AsyncMock(side_effect=Exception("Redis error"))
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception, match="Redis error"):
            await client.exists("test-key")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_delete_error(self, mock_redis, mock_get_settings):
        """Test delete with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.delete = AsyncMock(side_effect=Exception("Redis error"))
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception, match="Redis error"):
            await client.delete("test-key")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_set_value(self, mock_redis, mock_get_settings):
        """Test setting value in Redis"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.set_value("test-key", "test-value")

        mock_client_instance.set.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_set_value_with_ttl(self, mock_redis, mock_get_settings):
        """Test setting value with TTL"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.set_value("test-key", "test-value", ttl=3600)

        mock_client_instance.set.assert_called_once()
        mock_client_instance.expire.assert_called_once_with("test-key", 3600)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_get_value(self, mock_redis, mock_get_settings):
        """Test getting value from Redis"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.get = AsyncMock(return_value="test-value")
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.get_value("test-key")

        assert result == "test-value"

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_increment(self, mock_redis, mock_get_settings):
        """Test incrementing value in Redis"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.incr = AsyncMock(return_value=5)
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.increment("test-key", amount=2)

        assert result == 5
        mock_client_instance.incr.assert_called_once_with("test-key", 2)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_rpush(self, mock_redis, mock_get_settings):
        """Test pushing values to list"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.rpush = AsyncMock(return_value=3)
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.rpush("test-list", "val1", "val2", "val3")

        assert result == 3

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_set_list(self, mock_redis, mock_get_settings):
        """Test setting list in Redis"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.set_list("test-list", ["val1", "val2"])

        mock_client_instance.delete.assert_called_once_with("test-list")
        mock_client_instance.rpush.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_get_list(self, mock_redis, mock_get_settings):
        """Test getting list from Redis"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.lrange = AsyncMock(return_value=["val1", "val2"])
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.get_list("test-list")

        assert result == ["val1", "val2"]

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_publish(self, mock_redis, mock_get_settings):
        """Test publish to channel"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.publish("test-channel", "message")

        mock_client_instance.publish.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_subscribe(self, mock_redis, mock_get_settings):
        """Test subscribe to channel"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_pubsub = AsyncMock()
        # pubsub() is a regular method, not async
        mock_client_instance.pubsub = Mock(return_value=mock_pubsub)
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.subscribe("test-channel")

        assert result == mock_pubsub
        mock_pubsub.subscribe.assert_called_once_with("test-channel")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_expire(self, mock_redis, mock_get_settings):
        """Test setting expiration"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.expire("test-key", 60)

        mock_client_instance.expire.assert_called_once_with("test-key", 60)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_flushdb_async(self, mock_redis, mock_get_settings):
        """Test async flush database"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.flushdb(async_flush=True)

        mock_client_instance.flushdb.assert_called_once_with(asynchronous=True)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_flushdb_sync(self, mock_redis, mock_get_settings):
        """Test sync flush database"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.flushdb(async_flush=False)

        mock_client_instance.flushdb.assert_called_once_with(asynchronous=False)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_wait(self, mock_redis, mock_get_settings):
        """Test wait for replication"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.wait.return_value = 2
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.wait(2, 1000)

        assert result == 2
        mock_client_instance.wait.assert_called_once_with(2, 1000)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_close(self, mock_redis, mock_get_settings):
        """Test closing connection"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.close()

        mock_client_instance.close.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_hset(self, mock_redis, mock_get_settings):
        """Test hset operation"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.hset("test-key", "field1", "value1")

        mock_client_instance.hset.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_hincr(self, mock_redis, mock_get_settings):
        """Test hincr operation"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.hincrby.return_value = 5
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.hincr("test-key", "counter", 2)

        assert result == 5
        mock_client_instance.hincrby.assert_called_once_with("test-key", "counter", 2)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_scan(self, mock_redis, mock_get_settings):
        """Test scan operation"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        # Simulate scan returning results then cursor 0
        mock_client_instance.scan.side_effect = [
            (1, [b"key1", b"key2"]),
            (0, [b"key3"])
        ]
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.scan("test-*", count=100)

        assert result == [b"key1", b"key2", b"key3"]
        assert mock_client_instance.scan.call_count == 2

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_set_binary(self, mock_redis, mock_get_settings):
        """Test setting binary value"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.set_binary("test-key", b"binary-data", ttl=3600)

        mock_client_instance.set.assert_called_once_with("test-key", b"binary-data")
        mock_client_instance.expire.assert_called_once_with("test-key", 3600)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_get_binary(self, mock_redis, mock_get_settings):
        """Test getting binary value"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        # Mock the binary client
        mock_binary_client = AsyncMock()
        mock_binary_client.get.return_value = b"binary-data"
        mock_redis.side_effect = [AsyncMock(), mock_binary_client]

        client = RedisClient()
        await client.initialize()

        result = await client.get_binary("test-key")

        assert result == b"binary-data"
        mock_binary_client.close.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_publish_message(self, mock_redis, mock_get_settings):
        """Test publishing message to channel"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.publish_message("test-channel", "test-message")

        mock_client_instance.publish.assert_called_once_with("test-channel", "test-message")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xadd(self, mock_redis, mock_get_settings):
        """Test adding message to stream"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xadd.return_value = "1234567890-0"
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xadd("test-stream", {"field1": "value1"}, maxlen=1000)

        assert result == "1234567890-0"
        mock_client_instance.xadd.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xread_list_format(self, mock_redis, mock_get_settings):
        """Test reading from stream with list format response"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        # Simulate list format response
        mock_client_instance.xread.return_value = [
            ("test-stream", [("1234567890-0", {"field1": "value1"})])
        ]
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xread({"test-stream": "0"}, count=10)

        assert "test-stream" in result
        assert len(result["test-stream"]) == 1

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xread_dict_format(self, mock_redis, mock_get_settings):
        """Test reading from stream with dict format response"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        # Simulate dict format response
        mock_client_instance.xread.return_value = {
            "test-stream": [("1234567890-0", {"field1": "value1"})]
        }
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xread({"test-stream": "0"})

        assert "test-stream" in result
        assert len(result["test-stream"]) == 1

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xread_none_result(self, mock_redis, mock_get_settings):
        """Test reading from stream with None result"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xread.return_value = None
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xread({"test-stream": "0"}, block=1000)

        assert result == {}

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xreadgroup(self, mock_redis, mock_get_settings):
        """Test reading from stream with consumer group"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xreadgroup.return_value = [
            ("test-stream", [("1234567890-0", {"field1": "value1"})])
        ]
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xreadgroup("test-group", "consumer1", {"test-stream": ">"}, count=10)

        assert "test-stream" in result
        mock_client_instance.xreadgroup.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xreadgroup_dict_format(self, mock_redis, mock_get_settings):
        """Test xreadgroup with dict format response"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xreadgroup.return_value = {
            "test-stream": [("1234567890-0", {"field1": "value1"})]
        }
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xreadgroup("test-group", "consumer1", {"test-stream": ">"})

        assert "test-stream" in result

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xreadgroup_none_result(self, mock_redis, mock_get_settings):
        """Test xreadgroup with None result"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xreadgroup.return_value = None
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xreadgroup("test-group", "consumer1", {"test-stream": ">"})

        assert result == {}

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xack(self, mock_redis, mock_get_settings):
        """Test acknowledging messages"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xack.return_value = 2
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xack("test-stream", "test-group", "msg1", "msg2")

        assert result == 2
        mock_client_instance.xack.assert_called_once_with("test-stream", "test-group", "msg1", "msg2")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xgroup_create(self, mock_redis, mock_get_settings):
        """Test creating consumer group"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        await client.xgroup_create("test-stream", "test-group", mkstream=True)

        mock_client_instance.xgroup_create.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xgroup_create_already_exists(self, mock_redis, mock_get_settings):
        """Test creating consumer group that already exists"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xgroup_create.side_effect = Exception("BUSYGROUP Consumer Group name already exists")
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        # Should not raise exception for BUSYGROUP error
        await client.xgroup_create("test-stream", "test-group")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xgroup_destroy(self, mock_redis, mock_get_settings):
        """Test destroying consumer group"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xgroup_destroy.return_value = True
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xgroup_destroy("test-stream", "test-group")

        assert result is True

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xlen(self, mock_redis, mock_get_settings):
        """Test getting stream length"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xlen.return_value = 100
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xlen("test-stream")

        assert result == 100

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xtrim(self, mock_redis, mock_get_settings):
        """Test trimming stream"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xtrim.return_value = 50
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xtrim("test-stream", maxlen=1000)

        assert result == 50

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xpending_range(self, mock_redis, mock_get_settings):
        """Test getting pending messages range"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xpending_range.return_value = [
            {"message_id": "1234567890-0", "consumer": "consumer1"}
        ]
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xpending_range("test-stream", "test-group")

        assert len(result) == 1

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xclaim(self, mock_redis, mock_get_settings):
        """Test claiming pending messages"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xclaim.return_value = [
            ("1234567890-0", {"field1": "value1"})
        ]
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xclaim("test-stream", "test-group", "consumer2", 60000, "1234567890-0")

        assert len(result) == 1

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xinfo_stream(self, mock_redis, mock_get_settings):
        """Test getting stream info"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xinfo_stream.return_value = {
            "length": 100,
            "first-entry": "1234567890-0"
        }
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xinfo_stream("test-stream")

        assert result["length"] == 100

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xinfo_groups(self, mock_redis, mock_get_settings):
        """Test getting consumer groups info"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client_instance = AsyncMock()
        mock_client_instance.xinfo_groups.return_value = [
            {"name": "test-group", "consumers": 2}
        ]
        mock_redis.return_value = mock_client_instance

        client = RedisClient()
        await client.initialize()

        result = await client.xinfo_groups("test-stream")

        assert len(result) == 1


class TestRedisClientErrorHandling:
    """Test error handling in RedisClient"""

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_set_value_error(self, mock_redis, mock_get_settings):
        """Test set_value with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.set = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.set_value("test-key", "test-value")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_get_value_error(self, mock_redis, mock_get_settings):
        """Test get_value with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.get = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.get_value("test-key")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_increment_error(self, mock_redis, mock_get_settings):
        """Test increment with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.incr = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.increment("test-key")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_rpush_error(self, mock_redis, mock_get_settings):
        """Test rpush with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.rpush = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.rpush("test-list", "value1")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_set_list_error(self, mock_redis, mock_get_settings):
        """Test set_list with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.delete = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.set_list("test-list", ["value1", "value2"])

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_get_list_error(self, mock_redis, mock_get_settings):
        """Test get_list with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.lrange = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.get_list("test-list")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_lrange_error(self, mock_redis, mock_get_settings):
        """Test lrange with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.lrange = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.lrange("test-list", 0, -1)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_publish_error(self, mock_redis, mock_get_settings):
        """Test publish with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.publish = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.publish("test-channel", "test-message")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_set_list_with_ttl_error(self, mock_redis, mock_get_settings):
        """Test set_list with TTL error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.delete = AsyncMock()
        mock_client.rpush = AsyncMock()
        mock_client.expire = AsyncMock(side_effect=Exception("TTL error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.set_list("test-list", ["value1"], ttl=3600)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xread_error(self, mock_redis, mock_get_settings):
        """Test xread with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xread = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.xread({"stream1": "0"})

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xread_unexpected_format(self, mock_redis, mock_get_settings):
        """Test xread with unexpected result format"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        # Return unexpected format (string instead of list/dict)
        mock_client.xread = AsyncMock(return_value="unexpected")

        client = RedisClient()
        await client.initialize()

        result = await client.xread({"stream1": "0"})
        assert result == {}

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xreadgroup_error(self, mock_redis, mock_get_settings):
        """Test xreadgroup with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xreadgroup = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.xreadgroup("group1", "consumer1", {"stream1": ">"})

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xreadgroup_unexpected_format(self, mock_redis, mock_get_settings):
        """Test xreadgroup with unexpected result format"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        # Return unexpected format
        mock_client.xreadgroup = AsyncMock(return_value="unexpected")

        client = RedisClient()
        await client.initialize()

        result = await client.xreadgroup("group1", "consumer1", {"stream1": ">"})
        assert result == {}

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xack_error(self, mock_redis, mock_get_settings):
        """Test xack with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xack = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.xack("stream1", "group1", "123-0")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xgroup_create_error(self, mock_redis, mock_get_settings):
        """Test xgroup_create with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xgroup_create = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.xgroup_create("stream1", "group1")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xgroup_destroy_error(self, mock_redis, mock_get_settings):
        """Test xgroup_destroy with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xgroup_destroy = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.xgroup_destroy("stream1", "group1")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xlen_error(self, mock_redis, mock_get_settings):
        """Test xlen with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xlen = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.xlen("stream1")

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xtrim_error(self, mock_redis, mock_get_settings):
        """Test xtrim with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xtrim = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        with pytest.raises(Exception):
            await client.xtrim("stream1", maxlen=1000)

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xpending_range_error(self, mock_redis, mock_get_settings):
        """Test xpending_range with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xpending_range = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        result = await client.xpending_range("stream1", "group1")
        assert result == []

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xpending_range_with_consumer_filter(self, mock_redis, mock_get_settings):
        """Test xpending_range with consumer filter"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xpending_range = AsyncMock(return_value=[
            {"consumer": "consumer1", "message_id": "123-0"},
            {"consumer": "consumer2", "message_id": "123-1"},
        ])

        client = RedisClient()
        await client.initialize()

        result = await client.xpending_range("stream1", "group1", consumer="consumer1")
        assert len(result) == 1
        assert result[0]["consumer"] == "consumer1"

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xclaim_error(self, mock_redis, mock_get_settings):
        """Test xclaim with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xclaim = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        result = await client.xclaim("stream1", "group1", "consumer1", 60000, "123-0")
        assert result == []

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xinfo_stream_error(self, mock_redis, mock_get_settings):
        """Test xinfo_stream with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xinfo_stream = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        result = await client.xinfo_stream("stream1")
        assert result == {}

    @pytest.mark.asyncio
    @patch("app.services.redis_client.get_settings")
    @patch("app.services.redis_client.redis.Redis")
    async def test_xinfo_groups_error(self, mock_redis, mock_get_settings):
        """Test xinfo_groups with error"""
        mock_settings = Mock()
        mock_settings.redis.redis_host = "localhost"
        mock_settings.redis.redis_port = 6379
        mock_settings.redis.redis_db = 0
        mock_settings.redis.password = "password"
        mock_get_settings.return_value = mock_settings

        mock_client = AsyncMock()
        mock_redis.return_value = mock_client
        mock_client.xinfo_groups = AsyncMock(side_effect=Exception("Connection error"))

        client = RedisClient()
        await client.initialize()

        result = await client.xinfo_groups("stream1")
        assert result == []

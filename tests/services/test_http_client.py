"""
Tests for app/services/http_client.py
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
import httpx
from app.services import http_client


class TestPrewarmConnections:
    """Test _prewarm_connections function"""

    @patch("app.services.http_client._sync_http_client", None)
    @patch("app.services.http_client.logger")
    def test_prewarm_no_client(self, mock_logger):
        """Test pre-warming when client is not initialized"""
        http_client._prewarm_connections()
        mock_logger.warning.assert_called_once()

    @patch.dict("os.environ", {"AI_GATEWAY_BASE_URL": "https://test.gateway.com"})
    @patch("app.services.http_client.logger")
    def test_prewarm_success(self, mock_logger):
        """Test successful pre-warming"""
        # Setup
        mock_client = Mock()
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.http_version = "HTTP/2"
        mock_client.head.return_value = mock_response
        
        with patch("app.services.http_client._sync_http_client", mock_client):
            # Execute
            http_client._prewarm_connections()

            # Verify
            mock_client.head.assert_called_once_with(
                "https://test.gateway.com",
                timeout=10.0
            )
            mock_logger.info.assert_called()

    @patch.dict("os.environ", {"AI_GATEWAY_BASE_URL": "https://test.gateway.com"})
    @patch("app.services.http_client.logger")
    def test_prewarm_failure(self, mock_logger):
        """Test pre-warming failure handling"""
        # Setup
        mock_client = Mock()
        mock_client.head.side_effect = Exception("Connection failed")
        
        with patch("app.services.http_client._sync_http_client", mock_client):
            # Execute
            http_client._prewarm_connections()

            # Verify - should log warning but not raise
            mock_logger.warning.assert_called()


class TestInitializeHttpClient:
    """Test initialize_http_client function"""

    def teardown_method(self):
        """Clean up global state after each test"""
        http_client._http_client = None
        http_client._sync_http_client = None

    @patch("app.services.http_client._prewarm_connections")
    @patch("app.services.http_client.httpx.AsyncClient")
    @patch("app.services.http_client.httpx.Client")
    def test_initialize_default_params(self, mock_sync_client, mock_async_client, mock_prewarm):
        """Test initialization with default parameters"""
        # Setup
        mock_async = Mock()
        mock_sync = Mock()
        mock_async_client.return_value = mock_async
        mock_sync_client.return_value = mock_sync

        # Execute
        http_client.initialize_http_client()

        # Verify
        assert http_client._http_client == mock_async
        assert http_client._sync_http_client == mock_sync
        mock_prewarm.assert_called_once()

    @patch("app.services.http_client._prewarm_connections")
    @patch("app.services.http_client.httpx.AsyncClient")
    @patch("app.services.http_client.httpx.Client")
    def test_initialize_custom_params(self, mock_sync_client, mock_async_client, mock_prewarm):
        """Test initialization with custom parameters"""
        # Setup
        mock_async = Mock()
        mock_sync = Mock()
        mock_async_client.return_value = mock_async
        mock_sync_client.return_value = mock_sync

        # Execute
        http_client.initialize_http_client(
            timeout=600.0,
            max_connections=200,
            max_keepalive_connections=50,
            prewarm=False
        )

        # Verify
        assert http_client._http_client == mock_async
        assert http_client._sync_http_client == mock_sync
        mock_prewarm.assert_not_called()

    @patch("app.services.http_client._prewarm_connections")
    @patch("app.services.http_client.httpx.AsyncClient")
    @patch("app.services.http_client.httpx.Client")
    @patch("app.services.http_client.logger")
    def test_initialize_already_initialized(self, mock_logger, mock_sync_client, mock_async_client, mock_prewarm):
        """Test initialization when already initialized"""
        # Setup
        existing_async = Mock()
        existing_sync = Mock()
        http_client._http_client = existing_async
        http_client._sync_http_client = existing_sync

        # Execute
        http_client.initialize_http_client()

        # Verify - should log warning and not reinitialize
        mock_logger.warning.assert_called()
        assert http_client._http_client == existing_async
        assert http_client._sync_http_client == existing_sync


class TestGetHttpClient:
    """Test get_http_client function"""

    def teardown_method(self):
        """Clean up global state after each test"""
        http_client._http_client = None
        http_client._sync_http_client = None

    @patch("app.services.http_client.initialize_http_client")
    def test_get_http_client_not_initialized(self, mock_initialize):
        """Test getting client when not initialized"""
        # Execute
        result = http_client.get_http_client()

        # Verify
        mock_initialize.assert_called_once()

    def test_get_http_client_already_initialized(self):
        """Test getting client when already initialized"""
        # Setup
        mock_client = Mock()
        http_client._http_client = mock_client

        # Execute
        result = http_client.get_http_client()

        # Verify
        assert result == mock_client


class TestGetSyncHttpClient:
    """Test get_sync_http_client function"""

    def teardown_method(self):
        """Clean up global state after each test"""
        http_client._http_client = None
        http_client._sync_http_client = None

    @patch("app.services.http_client.initialize_http_client")
    def test_get_sync_http_client_not_initialized(self, mock_initialize):
        """Test getting sync client when not initialized"""
        # Execute
        result = http_client.get_sync_http_client()

        # Verify
        mock_initialize.assert_called_once()

    def test_get_sync_http_client_already_initialized(self):
        """Test getting sync client when already initialized"""
        # Setup
        mock_client = Mock()
        http_client._sync_http_client = mock_client

        # Execute
        result = http_client.get_sync_http_client()

        # Verify
        assert result == mock_client


class TestCloseHttpClient:
    """Test close_http_client function"""

    def teardown_method(self):
        """Clean up global state after each test"""
        http_client._http_client = None
        http_client._sync_http_client = None

    @pytest.mark.asyncio
    async def test_close_http_client_both_initialized(self):
        """Test closing both clients when initialized"""
        # Setup
        from unittest.mock import AsyncMock
        mock_async = Mock()
        mock_sync = Mock()
        mock_async.aclose = AsyncMock()
        mock_sync.close = Mock()

        http_client._http_client = mock_async
        http_client._sync_http_client = mock_sync

        # Execute
        await http_client.close_http_client()

        # Verify
        mock_async.aclose.assert_called_once()
        mock_sync.close.assert_called_once()
        assert http_client._http_client is None
        assert http_client._sync_http_client is None

    @pytest.mark.asyncio
    async def test_close_http_client_not_initialized(self):
        """Test closing when clients are not initialized"""
        # Execute - should not raise
        await http_client.close_http_client()

        # Verify
        assert http_client._http_client is None
        assert http_client._sync_http_client is None

    @pytest.mark.asyncio
    @patch("app.services.http_client.logger")
    async def test_close_http_client_error_propagates(self, mock_logger):
        """Test that errors during close propagate (no error handling in function)"""
        # Setup
        from unittest.mock import AsyncMock
        mock_async = Mock()
        mock_async.aclose = AsyncMock(side_effect=Exception("Close failed"))
        http_client._http_client = mock_async

        # Execute - should raise
        with pytest.raises(Exception, match="Close failed"):
            await http_client.close_http_client()


class TestGetClientStats:
    """Test get_client_stats function"""

    def teardown_method(self):
        """Clean up global state after each test"""
        http_client._http_client = None
        http_client._sync_http_client = None

    def test_get_client_stats_not_initialized(self):
        """Test getting stats when client is not initialized"""
        # Execute
        result = http_client.get_client_stats()

        # Verify
        assert result == {"status": "not_initialized"}

    def test_get_client_stats_initialized(self):
        """Test getting stats when client is initialized"""
        # Setup
        mock_client = Mock()
        mock_client.is_closed = False
        http_client._http_client = mock_client

        # Execute
        result = http_client.get_client_stats()

        # Verify
        assert result["status"] == "initialized"
        assert result["is_closed"] is False

    def test_get_client_stats_closed(self):
        """Test getting stats when client is closed"""
        # Setup
        mock_client = Mock()
        mock_client.is_closed = True
        http_client._http_client = mock_client

        # Execute
        result = http_client.get_client_stats()

        # Verify
        assert result["status"] == "initialized"
        assert result["is_closed"] is True


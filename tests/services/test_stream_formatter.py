"""Tests for stream_formatter.py"""
import pytest
from unittest.mock import Mock, MagicMock
from app.services.stream_formatter import stream_formatter
from app.shared.config.constants import EventType, AgentToolEnum


class TestStreamFormatter:
    """Test suite for stream_formatter function"""

    def test_ignore_chain_start_event(self):
        """Test that on_chain_start events are ignored"""
        chunk = {"event": "on_chain_start"}
        result = stream_formatter(chunk)
        assert result is None

    def test_ignore_chain_end_event(self):
        """Test that on_chain_end events are ignored"""
        chunk = {"event": "on_chain_end"}
        result = stream_formatter(chunk)
        assert result is None

    def test_on_chain_stream_missing_data(self):
        """Test on_chain_stream with missing data"""
        chunk = {"event": "on_chain_stream"}
        result = stream_formatter(chunk)
        assert result is None

    def test_on_chain_stream_missing_chunk(self):
        """Test on_chain_stream with missing chunk in data"""
        chunk = {"event": "on_chain_stream", "data": {}}
        result = stream_formatter(chunk)
        assert result is None

    def test_on_chain_stream_custom_event(self):
        """Test on_chain_stream with custom event"""
        chunk = {
            "event": "on_chain_stream",
            "data": {
                "chunk": [
                    ["supervisor:agent1"],
                    "custom",
                    {"type": "custom_data", "content": "test"}
                ]
            }
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["type"] == EventType.TOOL_STREAM.value
        assert result["delta"] == {"type": "custom_data", "content": "test"}
        assert result["source_agent"] == "supervisor"

    def test_on_chain_stream_workflow_complete_with_db_save(self):
        """Test on_chain_stream with workflow_complete and db_save"""
        chunk = {
            "event": "on_chain_stream",
            "data": {
                "chunk": [
                    [("agent:subagent",)],
                    "custom",
                    {"type": "workflow_complete", "db_save": True}
                ]
            }
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["type"] == EventType.TOOL_STREAM.value
        assert result["db_save"] is True

    def test_on_chain_stream_workflow_complete_without_db_save(self):
        """Test on_chain_stream with workflow_complete but no db_save"""
        chunk = {
            "event": "on_chain_stream",
            "data": {
                "chunk": [
                    [("agent:subagent",)],
                    "custom",
                    {"type": "workflow_complete"}
                ]
            }
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["type"] == EventType.TOOL_STREAM.value
        assert "db_save" not in result

    def test_on_chain_stream_unknown_source_agent(self):
        """Test on_chain_stream with unknown source agent"""
        chunk = {
            "event": "on_chain_stream",
            "data": {
                "chunk": [
                    [],
                    "custom",
                    {"data": "test"}
                ]
            }
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["source_agent"] == "unknown"

    def test_on_chat_model_start(self):
        """Test on_chat_model_start event"""
        chunk = {
            "event": "on_chat_model_start",
            "metadata": {"langgraph_checkpoint_ns": "supervisor:agent1"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["type"] == EventType.MESSAGE_START.value
        assert result["source_agent"] == "supervisor"

    def test_on_chat_model_start_no_metadata(self):
        """Test on_chat_model_start without metadata"""
        chunk = {"event": "on_chat_model_start"}
        result = stream_formatter(chunk)
        assert result is not None
        assert result["source_agent"] == "unknown"

    def test_on_chat_model_stream_with_tool_calls(self):
        """Test on_chat_model_stream with tool_calls (should be ignored)"""
        message_chunk = Mock()
        message_chunk.additional_kwargs = {"tool_calls": [{"name": "test"}]}
        
        chunk = {
            "event": "on_chat_model_stream",
            "data": {"chunk": message_chunk}
        }
        result = stream_formatter(chunk)
        assert result is None

    def test_on_chat_model_stream_with_content(self):
        """Test on_chat_model_stream with content"""
        message_chunk = Mock()
        message_chunk.content = "Hello world"
        message_chunk.additional_kwargs = {}
        
        chunk = {
            "event": "on_chat_model_stream",
            "data": {"chunk": message_chunk},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["type"] == EventType.MESSAGE.value
        assert result["delta"] == "Hello world"
        assert result["source_agent"] == "agent"

    def test_on_chat_model_stream_empty_content(self):
        """Test on_chat_model_stream with empty content"""
        message_chunk = Mock()
        message_chunk.content = ""
        message_chunk.additional_kwargs = {}
        
        chunk = {
            "event": "on_chat_model_stream",
            "data": {"chunk": message_chunk}
        }
        result = stream_formatter(chunk)
        assert result is None

    def test_on_chat_model_stream_reasoning_start(self):
        """Test on_chat_model_stream with reasoning content (first time)"""
        message_chunk = Mock()
        message_chunk.content = "test"
        message_chunk.additional_kwargs = {"reasoning_content": "thinking..."}

        chunk = {
            "event": "on_chat_model_stream",
            "data": {"chunk": message_chunk},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        # First reasoning call returns a list with THINKING_START and THINKING events
        assert result is not None
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0]["type"] == EventType.THINKING_START.value
        assert result[0]["source_agent"] == "agent"
        assert result[1]["type"] == EventType.THINKING.value
        assert result[1]["delta"] == "thinking..."

    def test_on_chat_model_stream_reasoning_delta(self):
        """Test on_chat_model_stream with reasoning content (subsequent)"""
        # First call to set reasoning active
        message_chunk1 = Mock()
        message_chunk1.content = "test"
        message_chunk1.additional_kwargs = {"reasoning_content": "thinking..."}
        chunk1 = {
            "event": "on_chat_model_stream",
            "data": {"chunk": message_chunk1},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        stream_formatter(chunk1)  # Sets reasoning active

        # Second call should return thinking delta
        message_chunk2 = Mock()
        message_chunk2.content = "test"
        message_chunk2.additional_kwargs = {"reasoning_content": "more thinking..."}
        chunk2 = {
            "event": "on_chat_model_stream",
            "data": {"chunk": message_chunk2},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk2)
        assert result is not None
        assert result["type"] == EventType.THINKING.value
        assert result["delta"] == "more thinking..."

    def test_on_chat_model_stream_reasoning_end(self):
        """Test on_chat_model_stream reasoning end"""
        # First call to set reasoning active
        message_chunk1 = Mock()
        message_chunk1.content = "test"
        message_chunk1.additional_kwargs = {"reasoning_content": "thinking..."}
        chunk1 = {
            "event": "on_chat_model_stream",
            "data": {"chunk": message_chunk1},
            "metadata": {"langgraph_checkpoint_ns": "agent2:sub"}
        }
        stream_formatter(chunk1)

        # Second call with reasoning content to activate
        stream_formatter(chunk1)

        # Third call without reasoning content should end thinking
        message_chunk2 = Mock()
        message_chunk2.content = "final"
        message_chunk2.additional_kwargs = {}
        chunk2 = {
            "event": "on_chat_model_stream",
            "data": {"chunk": message_chunk2},
            "metadata": {"langgraph_checkpoint_ns": "agent2:sub"}
        }
        result = stream_formatter(chunk2)
        assert result is not None
        assert result["type"] == EventType.THINKING_END.value

    def test_on_tool_start_basic(self):
        """Test on_tool_start event"""
        chunk = {
            "event": "on_tool_start",
            "name": "search_tool",
            "data": {"input": {"query": "test"}},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["type"] == EventType.TOOL_START.value
        assert result["tool_name"] == "search_tool"
        assert result["input"] == {"query": "test"}
        assert result["source_agent"] == "agent"

    def test_on_tool_start_no_name(self):
        """Test on_tool_start without name"""
        chunk = {"event": "on_tool_start"}
        result = stream_formatter(chunk)
        assert result is None

    def test_on_tool_start_transfer_to_ignored(self):
        """Test on_tool_start with transfer_to tool (should be ignored)"""
        chunk = {
            "event": "on_tool_start",
            "name": "transfer_to_agent"
        }
        result = stream_formatter(chunk)
        assert result is None

    def test_on_tool_start_read_todos(self):
        """Test on_tool_start with read_todos (empty input)"""
        chunk = {
            "event": "on_tool_start",
            "name": AgentToolEnum.READ_TODOS.value,
            "data": {"input": {"some": "data"}},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["input"] == ""

    def test_on_tool_start_remove_state(self):
        """Test on_tool_start removes state from input"""
        chunk = {
            "event": "on_tool_start",
            "name": "test_tool",
            "data": {"input": {"query": "test", "state": {"data": "hidden"}}},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert "state" not in result["input"]
        assert result["input"]["query"] == "test"

    def test_on_tool_end_basic(self):
        """Test on_tool_end event"""
        output_msg = Mock()
        output_msg.content = "Tool result"
        output_msg.tool_call_id = "call_123"

        chunk = {
            "event": "on_tool_end",
            "name": "search_tool",
            "data": {
                "output": output_msg,
                "input": {"query": "test"}
            },
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["type"] == EventType.TOOL_END.value
        assert result["tool_name"] == "search_tool"
        assert result["output"] == "Tool result"
        assert result["tool_id"] == "call_123"
        assert result["source_agent"] == "agent"
        assert result["isError"] is False
        assert result["db_save"] is True

    def test_on_tool_end_no_name(self):
        """Test on_tool_end without name"""
        chunk = {"event": "on_tool_end"}
        result = stream_formatter(chunk)
        assert result is None

    def test_on_tool_end_transfer_to_ignored(self):
        """Test on_tool_end with transfer_to tool"""
        chunk = {
            "event": "on_tool_end",
            "name": "transfer_to_agent"
        }
        result = stream_formatter(chunk)
        assert result is None

    def test_on_tool_end_command_output(self):
        """Test on_tool_end with Command output"""
        # Create a proper mock that doesn't have 'content' attribute initially
        tool_msg = type('ToolMessage', (), {
            'content': "Result from command",
            'tool_call_id': "call_456"
        })()

        command_output = type('Command', (), {
            'update': {"messages": [tool_msg]}
        })()

        chunk = {
            "event": "on_tool_end",
            "name": "test_tool",
            "data": {"output": command_output},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["output"] == "Result from command"
        assert result["tool_id"] == "call_456"

    def test_on_tool_end_read_todos_no_input(self):
        """Test on_tool_end with read_todos (no input)"""
        output_msg = Mock()
        output_msg.content = "[]"

        chunk = {
            "event": "on_tool_end",
            "name": AgentToolEnum.READ_TODOS.value,
            "data": {"output": output_msg, "input": {"some": "data"}},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["input"] == ""

    def test_on_tool_end_error_json(self):
        """Test on_tool_end with JSON error"""
        output_msg = Mock()
        output_msg.content = '{"isError": true, "message": "Failed"}'

        chunk = {
            "event": "on_tool_end",
            "name": "test_tool",
            "data": {"output": output_msg},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["isError"] is True

    def test_on_tool_end_error_dict_string(self):
        """Test on_tool_end with dict string error"""
        output_msg = Mock()
        output_msg.content = "{'isError': True, 'message': 'Failed'}"

        chunk = {
            "event": "on_tool_end",
            "name": "test_tool",
            "data": {"output": output_msg},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["isError"] is True

    def test_on_tool_end_error_dict_output(self):
        """Test on_tool_end with dict output containing isError"""
        chunk = {
            "event": "on_tool_end",
            "name": "test_tool",
            "data": {"output": Mock(content={"isError": True})},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["isError"] is True

    def test_on_chat_model_end_basic(self):
        """Test on_chat_model_end event"""
        ai_message = Mock()
        ai_message.content = "Response text"
        ai_message.tool_calls = []
        ai_message.usage_metadata = {
            "input_tokens": 100,
            "output_tokens": 50,
            "total_tokens": 150
        }
        ai_message.additional_kwargs = {}

        chunk = {
            "event": "on_chat_model_end",
            "data": {"output": ai_message},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["type"] == EventType.MESSAGE_END.value
        assert result["text_content"] == "Response text"
        assert result["tool_call"] == {}
        assert result["usage_data"]["input_tokens"] == 100
        assert result["usage_data"]["output_tokens"] == 50
        assert result["source_agent"] == "agent"
        assert result["db_save"] is True

    def test_on_chat_model_end_with_tool_call(self):
        """Test on_chat_model_end with tool call"""
        ai_message = Mock()
        ai_message.content = ""
        ai_message.tool_calls = [{
            "name": "search",
            "id": "call_789",
            "args": {"query": "test"}
        }]
        ai_message.usage_metadata = {}
        ai_message.additional_kwargs = {}

        chunk = {
            "event": "on_chat_model_end",
            "data": {"output": ai_message},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["tool_call"]["tool_name"] == "search"
        assert result["tool_call"]["tool_id"] == "call_789"
        assert result["tool_call"]["tool_input"] == {"query": "test"}
        assert result["db_save"] is False  # Empty text_content

    def test_on_chat_model_end_with_reasoning(self):
        """Test on_chat_model_end with reasoning content"""
        ai_message = Mock()
        ai_message.content = "Final answer"
        ai_message.tool_calls = []
        ai_message.usage_metadata = {}
        ai_message.additional_kwargs = {"reasoning_content": "I thought about..."}

        chunk = {
            "event": "on_chat_model_end",
            "data": {"output": ai_message},
            "metadata": {"langgraph_checkpoint_ns": "agent:sub"}
        }
        result = stream_formatter(chunk)
        assert result is not None
        assert result["thinking_content"] == "I thought about..."

    def test_on_chat_model_end_missing_data(self):
        """Test on_chat_model_end with missing data"""
        chunk = {"event": "on_chat_model_end"}
        result = stream_formatter(chunk)
        assert result is None

    def test_legacy_tuple_format_basic(self):
        """Test legacy tuple format"""
        message_chunk = Mock()
        message_chunk.content = "Legacy message"

        chunk = (
            (("agent:sub",),),
            "messages",
            (message_chunk, {"langgraph_checkpoint_ns": "agent:sub"})
        )
        result = stream_formatter(chunk)
        assert result is not None
        assert result["type"] == EventType.MESSAGE.value
        assert result["delta"] == "Legacy message"
        assert result["source_agent"] == "agent"

    def test_legacy_tuple_format_empty_content(self):
        """Test legacy tuple format with empty content"""
        message_chunk = Mock()
        message_chunk.content = ""

        chunk = (
            (("agent:sub",),),
            "messages",
            (message_chunk,)
        )
        result = stream_formatter(chunk)
        assert result is None

    def test_legacy_tuple_format_from_namespace(self):
        """Test legacy tuple format extracting agent from namespace"""
        message_chunk = Mock()
        message_chunk.content = "Test"

        chunk = (
            ("supervisor:agent1",),
            "messages",
            (message_chunk,)
        )
        result = stream_formatter(chunk)
        assert result is not None
        assert result["source_agent"] == "supervisor"

    def test_non_dict_non_tuple_chunk(self):
        """Test non-dict, non-tuple chunk"""
        chunk = "invalid"
        result = stream_formatter(chunk)
        assert result is None

    def test_dict_without_event_key(self):
        """Test dict chunk without event key"""
        chunk = {"data": "test"}
        result = stream_formatter(chunk)
        assert result is None


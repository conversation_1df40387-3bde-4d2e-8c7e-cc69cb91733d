"""
Tests for app/shared/config/base.py
"""
import pytest
from unittest.mock import patch, Mock
from app.shared.config.base import get_settings, clear_settings_cache


class TestGetSettings:
    """Test get_settings function"""

    @patch("app.shared.config.base.load_dotenv")
    @patch.dict("os.environ", {
        "API_HOST": "127.0.0.1",
        "API_PORT": "8000",
        "OPENAI_API_KEY": "test-key",
        "LLM_MODEL": "gpt-4",
    })
    def test_get_settings_basic(self, mock_load_dotenv):
        """Test basic settings retrieval"""
        # Clear cache first
        clear_settings_cache()
        
        # Execute
        settings = get_settings()

        # Verify
        assert settings.api.host == "127.0.0.1"
        assert settings.api.port == 8000
        assert settings.openai.api_key == "test-key"
        assert settings.openai.model == "gpt-4"
        mock_load_dotenv.assert_called_once_with(override=True)

    @patch("app.shared.config.base.load_dotenv")
    def test_get_settings_caching(self, mock_load_dotenv):
        """Test that settings are cached"""
        # Clear cache first
        clear_settings_cache()
        
        # Execute - call twice
        settings1 = get_settings()
        settings2 = get_settings()

        # Verify - should be same instance
        assert settings1 is settings2
        # load_dotenv should only be called once due to caching
        assert mock_load_dotenv.call_count == 1

    @patch("app.shared.config.base.load_dotenv")
    def test_get_settings_force_reload(self, mock_load_dotenv):
        """Test force reload parameter"""
        # Clear cache first
        clear_settings_cache()
        
        # Execute with force_reload
        settings1 = get_settings(force_reload=False)
        settings2 = get_settings(force_reload=True)

        # Verify - should be different instances due to cache invalidation
        assert settings1 is not settings2
        # load_dotenv should be called twice
        assert mock_load_dotenv.call_count == 2


class TestClearSettingsCache:
    """Test clear_settings_cache function"""

    @patch("app.shared.config.base.load_dotenv")
    def test_clear_settings_cache(self, mock_load_dotenv):
        """Test clearing settings cache"""
        # Setup - get settings to populate cache
        settings1 = get_settings()
        
        # Execute - clear cache
        clear_settings_cache()
        
        # Get settings again
        settings2 = get_settings()

        # Verify - should be different instances
        assert settings1 is not settings2
        # load_dotenv should be called twice (once for each get_settings)
        assert mock_load_dotenv.call_count == 2

    @patch("app.shared.config.base.load_dotenv")
    def test_clear_settings_cache_multiple_times(self, mock_load_dotenv):
        """Test clearing cache multiple times"""
        # Execute - clear cache multiple times
        clear_settings_cache()
        clear_settings_cache()
        clear_settings_cache()

        # Get settings
        settings = get_settings()

        # Verify - should work without errors
        assert settings is not None
        mock_load_dotenv.assert_called_once()


class TestSettingsDefaults:
    """Test default values in settings"""

    @patch("app.shared.config.base.load_dotenv")
    @patch.dict("os.environ", {}, clear=True)
    def test_default_api_config(self, mock_load_dotenv):
        """Test default API configuration"""
        # Clear cache
        clear_settings_cache()
        
        # Execute
        settings = get_settings()

        # Verify defaults
        assert settings.api.host == "0.0.0.0"
        assert settings.api.port == 6000

    @patch("app.shared.config.base.load_dotenv")
    @patch.dict("os.environ", {}, clear=True)
    def test_default_openai_config(self, mock_load_dotenv):
        """Test default OpenAI configuration"""
        # Clear cache
        clear_settings_cache()
        
        # Execute
        settings = get_settings()

        # Verify defaults
        assert settings.openai.api_key == ""
        assert settings.openai.model == "gpt-4o-mini"

    @patch("app.shared.config.base.load_dotenv")
    @patch.dict("os.environ", {}, clear=True)
    def test_default_environment(self, mock_load_dotenv):
        """Test default environment"""
        # Clear cache
        clear_settings_cache()
        
        # Execute
        settings = get_settings()

        # Verify default
        assert settings.environment == "dev"

    @patch("app.shared.config.base.load_dotenv")
    @patch.dict("os.environ", {}, clear=True)
    def test_default_model_provider(self, mock_load_dotenv):
        """Test default model provider"""
        # Clear cache
        clear_settings_cache()
        
        # Execute
        settings = get_settings()

        # Verify default
        assert settings.model_provider == "requesty"


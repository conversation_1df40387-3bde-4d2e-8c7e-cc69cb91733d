"""Tests for logging configuration."""

import pytest
import logging
import json
from unittest.mock import MagicMock, patch
from io import String<PERSON>

from app.shared.config.logging_config import (
    JSONFormatter,
    add_trace_context_to_record,
    setup_logging,
    get_logger,
    get_structured_logger,
    get_level_logger,
    log_with_context,
    log_to_all_levels,
)


class TestJSONFormatter:
    """Tests for JSONFormatter class."""

    def test_format_basic_record(self):
        """Test formatting a basic log record."""
        formatter = JSONFormatter()

        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None,
        )
        # Set the message attribute (normally done by logging framework)
        record.message = record.getMessage()

        result = formatter.format(record)
        parsed = json.loads(result)

        # Check that it's valid JSON with expected fields
        assert "level" in parsed
        assert parsed["level"] == "INFO"
        assert "name" in parsed
        assert parsed["name"] == "test_logger"
        assert "timestamp" in parsed
        # Message should be present
        assert "message" in parsed
        assert parsed["message"] == "Test message"

    def test_format_with_extra_fields(self):
        """Test formatting with extra fields."""
        formatter = JSONFormatter()

        record = logging.LogRecord(
            name="test_logger",
            level=logging.INFO,
            pathname="test.py",
            lineno=10,
            msg="Test message",
            args=(),
            exc_info=None,
        )
        # Set the message attribute (normally done by logging framework)
        record.message = record.getMessage()
        record.extra = {"user_id": "user-123", "conversation_id": "conv-456"}

        result = formatter.format(record)
        parsed = json.loads(result)

        assert parsed["level"] == "INFO"
        # Extra fields should be included
        assert parsed.get("user_id") == "user-123"
        assert parsed.get("conversation_id") == "conv-456"

    def test_format_with_exception(self):
        """Test formatting with exception info."""
        formatter = JSONFormatter()

        try:
            raise ValueError("Test error")
        except ValueError:
            import sys
            exc_info = sys.exc_info()

        record = logging.LogRecord(
            name="test_logger",
            level=logging.ERROR,
            pathname="test.py",
            lineno=10,
            msg="Error occurred",
            args=(),
            exc_info=exc_info,
        )
        # Set the message attribute (normally done by logging framework)
        record.message = record.getMessage()

        result = formatter.format(record)
        parsed = json.loads(result)

        assert parsed["level"] == "ERROR"
        assert "exception" in parsed

    def test_format_with_custom_fmt_dict(self):
        """Test formatting with custom format dictionary."""
        custom_fmt = {
            "level": "%(levelname)s",
            "logger": "%(name)s",
        }
        formatter = JSONFormatter(fmt_dict=custom_fmt)

        record = logging.LogRecord(
            name="custom_logger",
            level=logging.WARNING,
            pathname="test.py",
            lineno=10,
            msg="Warning message",
            args=(),
            exc_info=None,
        )

        result = formatter.format(record)
        parsed = json.loads(result)

        assert parsed["level"] == "WARNING"
        assert parsed["logger"] == "custom_logger"


class TestAddTraceContextToRecord:
    """Tests for add_trace_context_to_record function."""

    def test_add_trace_context_with_valid_span(self):
        """Test adding trace context with valid span."""
        record_dict = {"message": "test"}

        with patch("app.shared.config.logging_config.OTEL_AVAILABLE", True):
            with patch("app.shared.config.logging_config.trace") as mock_trace:
                mock_span = MagicMock()
                mock_ctx = MagicMock()
                mock_ctx.is_valid = True
                mock_ctx.trace_id = 12345
                mock_ctx.span_id = 67890
                mock_ctx.trace_flags = 1
                mock_span.get_span_context.return_value = mock_ctx
                mock_trace.get_current_span.return_value = mock_span

                result = add_trace_context_to_record(record_dict)

                assert "trace_id" in result
                assert "span_id" in result

    def test_add_trace_context_no_otel(self):
        """Test adding trace context when OTEL not available."""
        record_dict = {"message": "test"}

        with patch("app.shared.config.logging_config.OTEL_AVAILABLE", False):
            result = add_trace_context_to_record(record_dict)

            # Should return dict unchanged
            assert result == record_dict


class TestSetupLogging:
    """Tests for setup_logging function."""

    def test_setup_logging_default(self):
        """Test setup_logging with default parameters."""
        # Just verify it doesn't raise
        setup_logging()

    def test_setup_logging_with_json(self):
        """Test setup_logging with JSON format."""
        setup_logging(use_json=True)

    def test_setup_logging_with_custom_dir(self):
        """Test setup_logging with custom logs directory."""
        setup_logging(logs_dir="custom_logs")


class TestGetLogger:
    """Tests for get_logger function."""

    def test_get_logger_returns_logger(self):
        """Test that get_logger returns a logger."""
        logger = get_logger("test_module")

        assert logger is not None
        assert isinstance(logger, logging.Logger)

    def test_get_logger_with_name(self):
        """Test get_logger with specific name."""
        logger = get_logger("my_custom_logger")

        assert logger.name == "my_custom_logger"


class TestGetStructuredLogger:
    """Tests for get_structured_logger function."""

    def test_get_structured_logger_returns_logger(self):
        """Test that get_structured_logger returns a logger."""
        logger = get_structured_logger("test_module")

        assert logger is not None
        assert isinstance(logger, logging.Logger)


class TestGetLevelLogger:
    """Tests for get_level_logger function."""

    def test_get_level_logger_returns_logger(self):
        """Test that get_level_logger returns a logger."""
        logger = get_level_logger("debug")

        assert logger is not None
        assert isinstance(logger, logging.Logger)

    def test_get_level_logger_with_invalid_level(self):
        """Test get_level_logger with invalid level defaults to info."""
        logger = get_level_logger("invalid_level")

        assert logger is not None


class TestLogWithContext:
    """Tests for log_with_context function."""

    def test_log_with_context(self):
        """Test logging with context."""
        mock_logger = MagicMock()

        log_with_context(
            mock_logger,
            logging.INFO,
            "Test message",
            extra={"user_id": "user-123"},
        )

        mock_logger.log.assert_called_once()

    def test_log_with_context_no_extra(self):
        """Test logging with context without extra."""
        mock_logger = MagicMock()

        log_with_context(
            mock_logger,
            logging.INFO,
            "Test message",
        )

        mock_logger.log.assert_called_once()


class TestLogToAllLevels:
    """Tests for log_to_all_levels function."""

    def test_log_to_all_levels(self):
        """Test logging to all levels."""
        with patch("app.shared.config.logging_config.get_level_logger") as mock_get_logger:
            mock_logger = MagicMock()
            mock_get_logger.return_value = mock_logger

            log_to_all_levels("Test message", extra={"key": "value"})

            # Should be called for each level
            assert mock_get_logger.call_count == 5


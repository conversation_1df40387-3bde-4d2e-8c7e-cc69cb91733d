"""
Tests for app/shared/config/constants.py
"""
import pytest
from app.shared.config.constants import (
    RedisStreamEnum,
    RedisConsumerGroupEnum,
    RequestPlatforms,
    SSEEventType,
    RedisWorkflowStreamEnum,
    RedisWorkflowConsumerGroupEnum,
)


class TestRedisStreamEnum:
    """Test RedisStreamEnum constants"""

    def test_agent_requests_format(self):
        """Test AGENT_REQUESTS stream name format"""
        assert RedisStreamEnum.AGENT_REQUESTS == "{env}:agent:chat:requests"

    def test_agent_responses_format(self):
        """Test AGENT_RESPONSES stream name format"""
        assert RedisStreamEnum.AGENT_RESPONSES == "{env}:agent:chat:responses:{conversation_id}"

    def test_memory_requests_format(self):
        """Test MEMORY_REQUESTS stream name format"""
        assert RedisStreamEnum.MEMORY_REQUESTS == "{env}:memory:requests"

    def test_stop_requests_format(self):
        """Test STOP_REQUESTS stream name format"""
        assert RedisStreamEnum.STOP_REQUESTS == "{env}:agent:stop:requests"

    def test_enum_is_string(self):
        """Test that enum values are strings"""
        assert isinstance(RedisStreamEnum.AGENT_REQUESTS.value, str)


class TestRedisConsumerGroupEnum:
    """Test RedisConsumerGroupEnum constants"""

    def test_agent_processors(self):
        """Test AGENT_PROCESSORS group name"""
        assert RedisConsumerGroupEnum.AGENT_PROCESSORS == "agent-processors"

    def test_memory_processors(self):
        """Test MEMORY_PROCESSORS group name"""
        assert RedisConsumerGroupEnum.MEMORY_PROCESSORS == "memory-processors"

    def test_stop_processors(self):
        """Test STOP_PROCESSORS group name"""
        assert RedisConsumerGroupEnum.STOP_PROCESSORS == "stop-processors"


class TestRequestPlatforms:
    """Test RequestPlatforms enum"""

    def test_web_platform(self):
        """Test WEB platform value"""
        assert RequestPlatforms.WEB == "web"

    def test_sms_platform(self):
        """Test SMS platform value"""
        assert RequestPlatforms.SMS == "sms"

    def test_enum_values(self):
        """Test all platform values are present"""
        platforms = [p.value for p in RequestPlatforms]
        assert "web" in platforms
        assert "sms" in platforms


class TestSSEEventType:
    """Test SSEEventType enum"""

    def test_stream_start(self):
        """Test STREAM_START event type"""
        assert SSEEventType.STREAM_START == "stream_start"

    def test_message_start(self):
        """Test MESSAGE_START event type"""
        assert SSEEventType.MESSAGE_START == "message_start"

    def test_chunk(self):
        """Test CHUNK event type"""
        assert SSEEventType.CHUNK == "chunk"

    def test_tool_use_start(self):
        """Test TOOL_USE_START event type"""
        assert SSEEventType.TOOL_USE_START == "tool_use_start"

    def test_tool_chunk(self):
        """Test TOOL_CHUNK event type"""
        assert SSEEventType.TOOL_CHUNK == "tool_chunk"

    def test_tool_result(self):
        """Test TOOL_RESULT event type"""
        assert SSEEventType.TOOL_RESULT == "tool_result"

    def test_text_result(self):
        """Test TEXT_RESULT event type"""
        assert SSEEventType.TEXT_RESULT == "text_result"

    def test_stream_end(self):
        """Test STREAM_END event type"""
        assert SSEEventType.STREAM_END == "stream_end"


class TestRedisWorkflowStreamEnum:
    """Test RedisWorkflowStreamEnum constants"""

    def test_workflow_requests(self):
        """Test WORKFLOW_REQUESTS stream name"""
        assert RedisWorkflowStreamEnum.WORKFLOW_REQUESTS == "{env}:workflow:requests"

    def test_workflow_responses(self):
        """Test WORKFLOW_RESPONSES stream name"""
        assert RedisWorkflowStreamEnum.WORKFLOW_RESPONSES == "{env}:workflow:responses"

    def test_workflow_chat_responses(self):
        """Test WORKFLOW_CHAT_RESPONSES stream name"""
        assert RedisWorkflowStreamEnum.WORKFLOW_CHAT_RESPONSES == "{env}:workflow:chat:responses"

    def test_workflow_chat_requests(self):
        """Test WORKFLOW_CHAT_REQUESTS stream name"""
        assert RedisWorkflowStreamEnum.WORKFLOW_CHAT_REQUESTS == "{env}:workflow:chat:requests"


class TestRedisWorkflowConsumerGroupEnum:
    """Test RedisWorkflowConsumerGroupEnum constants"""

    def test_enum_exists(self):
        """Test that RedisWorkflowConsumerGroupEnum exists and is importable"""
        assert RedisWorkflowConsumerGroupEnum is not None


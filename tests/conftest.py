"""
Shared test fixtures and configuration for all tests.
"""
import pytest
from unittest.mock import Mock, MagicMock, AsyncMock, patch
from typing import Any, Dict, List
import asyncio


# ============================================================================
# Mock Database Fixtures
# ============================================================================

@pytest.fixture
def mock_db_session():
    """Mock database session for testing."""
    session = Mock()
    session.query = Mock()
    session.add = Mock()
    session.commit = Mock()
    session.rollback = Mock()
    session.close = Mock()
    session.flush = Mock()
    session.delete = Mock()
    session.refresh = Mock()
    return session


@pytest.fixture
def mock_mongodb_client():
    """Mock MongoDB client for testing."""
    client = Mock()
    db = Mock()
    collection = Mock()
    
    client.__getitem__ = Mock(return_value=db)
    db.__getitem__ = Mock(return_value=collection)
    
    collection.find_one = AsyncMock(return_value=None)
    collection.find = Mock()
    collection.insert_one = AsyncMock()
    collection.update_one = AsyncMock()
    collection.delete_one = AsyncMock()
    collection.count_documents = AsyncMock(return_value=0)
    
    return client


# ============================================================================
# Mock Redis Fixtures
# ============================================================================

@pytest.fixture
def mock_redis_client():
    """Mock Redis client for testing."""
    redis = AsyncMock()
    redis.get = AsyncMock(return_value=None)
    redis.set = AsyncMock(return_value=True)
    redis.delete = AsyncMock(return_value=1)
    redis.exists = AsyncMock(return_value=0)
    redis.expire = AsyncMock(return_value=True)
    redis.hget = AsyncMock(return_value=None)
    redis.hset = AsyncMock(return_value=1)
    redis.hgetall = AsyncMock(return_value={})
    redis.hdel = AsyncMock(return_value=1)
    redis.lpush = AsyncMock(return_value=1)
    redis.rpush = AsyncMock(return_value=1)
    redis.lpop = AsyncMock(return_value=None)
    redis.rpop = AsyncMock(return_value=None)
    redis.lrange = AsyncMock(return_value=[])
    redis.xadd = AsyncMock(return_value=b"1234567890-0")
    redis.xread = AsyncMock(return_value=[])
    redis.xack = AsyncMock(return_value=1)
    redis.xgroup_create = AsyncMock()
    redis.close = AsyncMock()
    return redis


# ============================================================================
# Mock HTTP Client Fixtures
# ============================================================================

@pytest.fixture
def mock_http_response():
    """Mock HTTP response for testing."""
    response = Mock()
    response.status_code = 200
    response.json = Mock(return_value={})
    response.text = ""
    response.content = b""
    response.headers = {}
    response.raise_for_status = Mock()
    return response


@pytest.fixture
def mock_async_http_client():
    """Mock async HTTP client for testing."""
    client = AsyncMock()
    response = AsyncMock()
    response.status_code = 200
    response.json = AsyncMock(return_value={})
    response.text = ""
    response.content = b""
    response.headers = {}
    response.raise_for_status = AsyncMock()
    
    client.get = AsyncMock(return_value=response)
    client.post = AsyncMock(return_value=response)
    client.put = AsyncMock(return_value=response)
    client.delete = AsyncMock(return_value=response)
    client.close = AsyncMock()
    
    return client


# ============================================================================
# Mock Agent/LLM Fixtures
# ============================================================================

@pytest.fixture
def mock_openai_client():
    """Mock OpenAI client for testing."""
    client = Mock()
    client.chat = Mock()
    client.chat.completions = Mock()
    client.chat.completions.create = Mock()
    return client


@pytest.fixture
def mock_agent_state():
    """Mock agent state for testing."""
    return {
        "messages": [],
        "context": {},
        "metadata": {},
        "session_id": "test-session-123",
        "user_id": "test-user-456"
    }


# ============================================================================
# Event Loop Fixture
# ============================================================================

@pytest.fixture(scope="function")
def event_loop():
    """Create an instance of the default event loop for each test case."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


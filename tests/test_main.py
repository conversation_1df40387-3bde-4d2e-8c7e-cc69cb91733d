"""Tests for main application."""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock


class TestInitializeServices:
    """Tests for initialize_services function."""

    @pytest.mark.asyncio
    async def test_initialize_services_success(self):
        """Test successful service initialization."""
        with patch("app.main.initialize_http_client") as mock_http:
            with patch("app.main.initialize_mongodb_client") as mock_mongo:
                with patch("app.main.initialize_redis_manager", new_callable=AsyncMock) as mock_redis:
                    with patch("app.main.AgentFetchService") as mock_agent_fetch:
                        with patch("app.main.get_infrastructure_monitor") as mock_monitor:
                            mock_monitor_instance = MagicMock()
                            mock_monitor_instance.start_monitoring = AsyncMock()
                            mock_monitor.return_value = mock_monitor_instance

                            from app.main import initialize_services

                            await initialize_services()

                            mock_http.assert_called_once()
                            mock_mongo.assert_called_once()
                            mock_redis.assert_called_once()
                            mock_agent_fetch.assert_called_once()


class TestShutdownServices:
    """Tests for shutdown_services function."""

    @pytest.mark.asyncio
    async def test_shutdown_services_success(self):
        """Test successful service shutdown."""
        with patch("app.main.close_http_client", new_callable=AsyncMock) as mock_close_http:
            with patch("app.main.close_redis_manager", new_callable=AsyncMock) as mock_close_redis:
                with patch("app.main.close_mongodb_client") as mock_close_mongo:
                    with patch("app.main.get_infrastructure_monitor") as mock_monitor:
                        mock_monitor_instance = MagicMock()
                        mock_monitor_instance.stop_monitoring = AsyncMock()
                        mock_monitor.return_value = mock_monitor_instance

                        from app.main import shutdown_services

                        await shutdown_services()

                        mock_close_http.assert_called_once()
                        mock_close_redis.assert_called_once()
                        mock_close_mongo.assert_called_once()


class TestRunApplication:
    """Tests for run_application function."""

    def test_run_application_exists(self):
        """Test that run_application function exists."""
        from app.main import run_application

        assert run_application is not None
        assert callable(run_application)

    @pytest.mark.asyncio
    async def test_run_application_initializes_and_shuts_down(self):
        """Test that run_application initializes and shuts down services."""
        with patch("app.main.initialize_services", new_callable=AsyncMock) as mock_init:
            with patch("app.main.shutdown_services", new_callable=AsyncMock) as mock_shutdown:
                with patch("app.main.listen_event_from_redis_pubsub", new_callable=AsyncMock) as mock_listen:
                    from app.main import run_application
                    await run_application()

                    mock_init.assert_called_once()
                    mock_listen.assert_called_once()
                    mock_shutdown.assert_called_once()


class TestBackgroundScheduler:
    """Tests for background scheduler."""

    def test_background_scheduler_exists(self):
        """Test that background_scheduler is defined."""
        from app.main import background_scheduler

        assert background_scheduler is not None

    def test_background_scheduler_is_running(self):
        """Test that background_scheduler is running."""
        from app.main import background_scheduler

        assert background_scheduler.running


class TestMain:
    """Tests for main function."""

    def test_main_exists(self):
        """Test that main function exists."""
        from app.main import main

        assert main is not None
        assert callable(main)

    def test_main_keyboard_interrupt(self):
        """Test main handles keyboard interrupt."""
        with patch("app.main.asyncio.run") as mock_run:
            mock_run.side_effect = KeyboardInterrupt()

            from app.main import main
            # Should not raise
            main()

    def test_main_exception(self):
        """Test main handles exceptions."""
        with patch("app.main.asyncio.run") as mock_run:
            with patch("app.main.get_exception_tracker") as mock_tracker:
                mock_tracker_instance = MagicMock()
                mock_tracker.return_value = mock_tracker_instance
                mock_run.side_effect = Exception("Test error")

                from app.main import main
                # Should not raise, should track exception
                main()

                mock_tracker_instance.track_exception.assert_called_once()


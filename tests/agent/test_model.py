"""
Tests for app/agent/model.py
"""
import pytest
from unittest.mock import Mock, patch
from app.agent.model import get_chat_model


class TestGetChatModel:
    """Test get_chat_model function"""

    @patch("app.agent.model.get_sync_http_client")
    @patch("app.agent.model.ChatOpenAIWithReasoning")
    @patch.dict("os.environ", {
        "DEFAULT_PROVIDER": "openai",
        "DEFAULT_MODEL_NAME": "gpt-4o-mini",
        "OPENROUTER_API_KEY": "test-api-key",
        "AI_GATEWAY_BASE_URL": "https://test.gateway.com",
        "AI_GATEWAY_API_KEY": "gateway-key"
    })
    def test_get_chat_model_with_defaults(self, mock_chat_class, mock_http_client):
        """Test creating chat model with default values"""
        # Setup
        mock_http = Mock()
        mock_http_client.return_value = mock_http
        mock_model = Mock()
        mock_chat_class.return_value = mock_model

        # Execute
        result = get_chat_model()

        # Verify
        assert result == mock_model
        mock_chat_class.assert_called_once()
        call_kwargs = mock_chat_class.call_args[1]
        assert call_kwargs["model"] == "openai/gpt-4o-mini"
        assert call_kwargs["base_url"] == "https://test.gateway.com"

    @patch("app.agent.model.get_sync_http_client")
    @patch("app.agent.model.ChatOpenAIWithReasoning")
    @patch.dict("os.environ", {
        "OPENROUTER_API_KEY": "test-api-key",
        "AI_GATEWAY_BASE_URL": "https://test.gateway.com"
    })
    def test_get_chat_model_with_custom_provider(self, mock_chat_class, mock_http_client):
        """Test creating chat model with custom provider and model"""
        # Setup
        mock_http = Mock()
        mock_http_client.return_value = mock_http
        mock_model = Mock()
        mock_chat_class.return_value = mock_model

        # Execute
        result = get_chat_model(provider="anthropic", model_name="claude-3-opus")

        # Verify
        call_kwargs = mock_chat_class.call_args[1]
        assert call_kwargs["model"] == "anthropic/claude-3-opus"

    @patch("app.agent.model.get_sync_http_client")
    @patch("app.agent.model.ChatOpenAIWithReasoning")
    @patch.dict("os.environ", {
        "OPENROUTER_API_KEY": "test-api-key",
        "AI_GATEWAY_BASE_URL": "https://test.gateway.com",
        "AI_GATEWAY_API_KEY": "gateway-key"
    })
    def test_get_chat_model_with_headers(self, mock_chat_class, mock_http_client):
        """Test creating chat model with custom headers"""
        # Setup
        mock_http = Mock()
        mock_http_client.return_value = mock_http
        mock_model = Mock()
        mock_chat_class.return_value = mock_model

        # Execute
        result = get_chat_model(
            user_id="user-123",
            organisation_id="org-456",
            request_type="chat",
            agent_id="agent-789",
            conversation_id="conv-abc"
        )

        # Verify
        call_kwargs = mock_chat_class.call_args[1]
        headers = call_kwargs["default_headers"]
        assert headers["X-User-Id"] == "user-123"
        assert headers["X-Organisation-Id"] == "org-456"
        assert headers["X-Request-Type"] == "chat"
        assert headers["X-Agent-Id"] == "agent-789"
        assert headers["X-Conversation-Id"] == "conv-abc"
        assert headers["X-Server-Auth"] == "gateway-key"
        assert headers["X-Source"] == "agent-platform"

    @patch("app.agent.model.get_sync_http_client")
    @patch("app.agent.model.ChatOpenAIWithReasoning")
    @patch.dict("os.environ", {
        "OPENROUTER_API_KEY": "test-api-key",
        "AI_GATEWAY_BASE_URL": "https://test.gateway.com"
    })
    def test_get_chat_model_with_thinking(self, mock_chat_class, mock_http_client):
        """Test creating chat model with thinking/reasoning enabled"""
        # Setup
        mock_http = Mock()
        mock_http_client.return_value = mock_http
        mock_model = Mock()
        mock_chat_class.return_value = mock_model

        # Execute
        result = get_chat_model(use_thinking=True)

        # Verify
        call_kwargs = mock_chat_class.call_args[1]
        extra_body = call_kwargs["extra_body"]
        assert extra_body["reasoning"]["type"] == "enabled"
        assert extra_body["reasoning"]["effort"] == "minimal"
        assert extra_body["include_reasoning"] is True

    @patch("app.agent.model.get_sync_http_client")
    @patch("app.agent.model.ChatOpenAIWithReasoning")
    @patch.dict("os.environ", {
        "OPENROUTER_API_KEY": "test-api-key",
        "AI_GATEWAY_BASE_URL": "https://test.gateway.com"
    })
    def test_get_chat_model_without_thinking(self, mock_chat_class, mock_http_client):
        """Test creating chat model without thinking"""
        # Setup
        mock_http = Mock()
        mock_http_client.return_value = mock_http
        mock_model = Mock()
        mock_chat_class.return_value = mock_model

        # Execute
        result = get_chat_model(use_thinking=False)

        # Verify
        call_kwargs = mock_chat_class.call_args[1]
        extra_body = call_kwargs["extra_body"]
        assert extra_body == {}

    @patch("app.agent.model.get_sync_http_client")
    @patch("app.agent.model.ChatOpenAIWithReasoning")
    @patch.dict("os.environ", {
        "OPENROUTER_API_KEY": "test-api-key"
    })
    def test_get_chat_model_fallback_on_error(self, mock_chat_class, mock_http_client):
        """Test fallback behavior when error occurs"""
        # Setup
        mock_http = Mock()
        mock_http_client.return_value = mock_http
        
        # First call raises error, second call succeeds
        mock_model = Mock()
        mock_chat_class.side_effect = [Exception("Test error"), mock_model]

        # Execute
        result = get_chat_model()

        # Verify fallback was called
        assert mock_chat_class.call_count == 2

    @patch("app.agent.model.get_sync_http_client")
    @patch("app.agent.model.ChatOpenAIWithReasoning")
    @patch.dict("os.environ", {}, clear=True)
    def test_get_chat_model_missing_api_key(self, mock_chat_class, mock_http_client):
        """Test behavior when API key is missing"""
        # Setup
        mock_http = Mock()
        mock_http_client.return_value = mock_http
        mock_model = Mock()
        mock_chat_class.return_value = mock_model

        # Execute - should use fallback
        result = get_chat_model()

        # Verify fallback was used
        assert result == mock_model


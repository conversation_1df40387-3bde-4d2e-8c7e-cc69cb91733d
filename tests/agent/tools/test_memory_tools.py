"""Tests for memory_tools.py"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from app.agent.tools.memory_tools import (
    setup_mem0_with_qdrant,
    get_memory_instance,
    initialize_memory,
    _store_memory_impl_async,
    _retrieve_memories_impl,
    get_store_memory_tool,
    get_retrieve_memories_tool,
)


class TestSetupMem0WithQdrant:
    """Test setup_mem0_with_qdrant function"""

    @patch("app.agent.tools.memory_tools.Memory")
    @patch("app.agent.tools.memory_tools.OpenAIEmbeddingWithHeaders")
    @patch("app.agent.tools.memory_tools.OpenAILLMWithHeaders")
    @patch.dict("os.environ", {
        "QDRANT_HOST": "test-host",
        "QDRANT_PORT": "6333",
        "QDRANT_API_KEY": "test-key",
        "AGENT_MEMORY_COLLECTION_NAME": "test-collection",
        "QDRANT_VECTOR_SIZE": "1536",
        "OPENROUTER_API_KEY": "test-openrouter-key",
        "AI_GATEWAY_BASE_URL": "https://gateway.test",
        "AI_GATEWAY_API_KEY": "test-gateway-key",
        "MEM0_LLM_MODEL": "gpt-4",
        "MEM0_EMBEDDING_MODEL": "text-embedding-3-small",
    })
    def test_setup_mem0_with_all_params(self, mock_llm_class, mock_embedding_class, mock_memory_class):
        """Test setup with all parameters"""
        # Setup mocks
        mock_llm = Mock()
        mock_llm_class.return_value = mock_llm
        
        mock_embedder = Mock()
        mock_embedding_class.return_value = mock_embedder
        
        mock_memory = Mock()
        mock_memory_class.from_config.return_value = mock_memory
        
        # Execute
        result = setup_mem0_with_qdrant(
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000"
        )
        
        # Verify
        assert result == mock_memory
        assert result.llm == mock_llm
        assert result.embedding_model == mock_embedder
        mock_memory_class.from_config.assert_called_once()

    @patch("app.agent.tools.memory_tools.Memory")
    @patch("app.agent.tools.memory_tools.OpenAIEmbeddingWithHeaders")
    @patch("app.agent.tools.memory_tools.OpenAILLMWithHeaders")
    @patch.dict("os.environ", {
        "QDRANT_HOST": "localhost",
        "QDRANT_PORT": "6333",
    })
    def test_setup_mem0_with_minimal_params(self, mock_llm_class, mock_embedding_class, mock_memory_class):
        """Test setup with minimal parameters"""
        # Setup mocks
        mock_llm = Mock()
        mock_llm_class.return_value = mock_llm
        
        mock_embedder = Mock()
        mock_embedding_class.return_value = mock_embedder
        
        mock_memory = Mock()
        mock_memory_class.from_config.return_value = mock_memory
        
        # Execute
        result = setup_mem0_with_qdrant()
        
        # Verify
        assert result == mock_memory
        mock_memory_class.from_config.assert_called_once()


class TestGetMemoryInstance:
    """Test get_memory_instance function"""

    @patch("app.agent.tools.memory_tools.setup_mem0_with_qdrant")
    def test_get_memory_instance_creates_new(self, mock_setup):
        """Test creating new memory instance"""
        # Reset global state
        import app.agent.tools.memory_tools as mem_tools
        mem_tools._memory_instance = None
        mem_tools._memory_llm_headers = None
        
        mock_memory = Mock()
        mock_setup.return_value = mock_memory
        
        # Execute
        result = get_memory_instance(
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000"
        )
        
        # Verify
        assert result == mock_memory
        mock_setup.assert_called_once_with(
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000"
        )

    @patch("app.agent.tools.memory_tools.setup_mem0_with_qdrant")
    def test_get_memory_instance_reuses_existing(self, mock_setup):
        """Test reusing existing memory instance"""
        # Setup existing instance
        import app.agent.tools.memory_tools as mem_tools
        mock_memory = Mock()
        mem_tools._memory_instance = mock_memory
        mem_tools._memory_llm_headers = {
            "user_id": "user123",
            "conversation_id": "conv456",
            "agent_id": "agent789",
            "organisation_id": "org000"
        }
        
        # Execute with same context
        result = get_memory_instance(
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000"
        )
        
        # Verify - should reuse existing instance
        assert result == mock_memory
        mock_setup.assert_not_called()

    @patch("app.agent.tools.memory_tools.setup_mem0_with_qdrant")
    def test_get_memory_instance_reinitializes_on_context_change(self, mock_setup):
        """Test reinitializing memory instance when context changes"""
        # Setup existing instance with different context
        import app.agent.tools.memory_tools as mem_tools
        old_memory = Mock()
        mem_tools._memory_instance = old_memory
        mem_tools._memory_llm_headers = {
            "user_id": "old_user",
            "conversation_id": "old_conv",
            "agent_id": "old_agent",
            "organisation_id": "old_org"
        }
        
        new_memory = Mock()
        mock_setup.return_value = new_memory
        
        # Execute with different context
        result = get_memory_instance(
            user_id="new_user",
            conversation_id="new_conv",
            agent_id="new_agent",
            organisation_id="new_org"
        )
        
        # Verify - should create new instance
        assert result == new_memory
        mock_setup.assert_called_once()


class TestInitializeMemory:
    """Test initialize_memory function"""

    @patch("app.agent.tools.memory_tools.setup_mem0_with_qdrant")
    def test_initialize_memory_creates_instance(self, mock_setup):
        """Test initializing memory instance"""
        # Reset global state
        import app.agent.tools.memory_tools as mem_tools
        mem_tools._memory_instance = None
        mem_tools._memory_llm_headers = None

        mock_memory = Mock()
        mock_setup.return_value = mock_memory

        # Execute
        initialize_memory(
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000"
        )

        # Verify
        assert mem_tools._memory_instance == mock_memory
        assert mem_tools._memory_llm_headers == {
            "user_id": "user123",
            "conversation_id": "conv456",
            "agent_id": "agent789",
            "organisation_id": "org000"
        }

    @patch("app.agent.tools.memory_tools.setup_mem0_with_qdrant")
    def test_initialize_memory_skips_if_exists(self, mock_setup):
        """Test skipping initialization if instance exists"""
        # Setup existing instance
        import app.agent.tools.memory_tools as mem_tools
        existing_memory = Mock()
        mem_tools._memory_instance = existing_memory

        # Execute
        initialize_memory(user_id="user123")

        # Verify - should not reinitialize
        mock_setup.assert_not_called()
        assert mem_tools._memory_instance == existing_memory


class TestStoreMemoryImplAsync:
    """Test _store_memory_impl_async function"""

    @pytest.mark.asyncio
    @patch("app.agent.tools.memory_tools.get_memory_instance")
    @patch("app.agent.tools.memory_tools.trace_operation")
    @patch("app.agent.tools.memory_tools.asyncio.get_running_loop")
    async def test_store_memory_success(self, mock_get_loop, mock_trace, mock_get_memory):
        """Test successful memory storage"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_memory = Mock()
        mock_memory.add = Mock(return_value=["mem_id_1", "mem_id_2"])
        mock_get_memory.return_value = mock_memory

        mock_loop = Mock()
        mock_loop.run_in_executor = AsyncMock(return_value=["mem_id_1", "mem_id_2"])
        mock_get_loop.return_value = mock_loop

        # Execute
        await _store_memory_impl_async(
            text="Test memory",
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000"
        )

        # Verify
        mock_get_memory.assert_called_once_with(
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000"
        )
        mock_loop.run_in_executor.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.agent.tools.memory_tools.exception_tracker")
    @patch("app.agent.tools.memory_tools.get_memory_instance")
    @patch("app.agent.tools.memory_tools.trace_operation")
    @patch("app.agent.tools.memory_tools.asyncio.get_running_loop")
    async def test_store_memory_error(self, mock_get_loop, mock_trace, mock_get_memory, mock_tracker):
        """Test memory storage with error"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_loop = Mock()
        mock_loop.run_in_executor = AsyncMock(side_effect=Exception("Storage error"))
        mock_get_loop.return_value = mock_loop

        mock_memory = Mock()
        mock_get_memory.return_value = mock_memory

        # Execute - should not raise
        await _store_memory_impl_async(
            text="Test memory",
            user_id="user123"
        )

        # Verify error handling
        mock_tracker.track_exception.assert_called_once()
        mock_span.set_attribute.assert_any_call("error", True)

    @pytest.mark.asyncio
    @patch("app.agent.tools.memory_tools.get_memory_instance")
    @patch("app.agent.tools.memory_tools.trace_operation")
    @patch("app.agent.tools.memory_tools.asyncio.get_running_loop")
    async def test_store_memory_with_additional_metadata(self, mock_get_loop, mock_trace, mock_get_memory):
        """Test memory storage with additional metadata"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_memory = Mock()
        mock_memory.add = Mock(return_value=["mem_id_1"])
        mock_get_memory.return_value = mock_memory

        mock_loop = Mock()
        mock_loop.run_in_executor = AsyncMock(return_value=["mem_id_1"])
        mock_get_loop.return_value = mock_loop

        # Execute
        await _store_memory_impl_async(
            text="Test memory",
            user_id="user123",
            additional_metadata={"key": "value"}
        )

        # Verify
        mock_loop.run_in_executor.assert_called_once()


class TestRetrieveMemoriesImpl:
    """Test _retrieve_memories_impl function"""

    @patch("app.agent.tools.memory_tools.get_memory_instance")
    @patch("app.agent.tools.memory_tools.trace_operation")
    def test_retrieve_memories_success(self, mock_trace, mock_get_memory):
        """Test successful memory retrieval"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_memory = Mock()
        mock_memory.search = Mock(return_value=[
            {"memory": "Memory 1", "score": 0.9},
            {"memory": "Memory 2", "score": 0.8}
        ])
        mock_get_memory.return_value = mock_memory

        # Execute
        result = _retrieve_memories_impl(
            query="test query",
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000",
            limit=5
        )

        # Verify
        assert len(result) == 2
        assert result[0]["memory"] == "Memory 1"
        mock_memory.search.assert_called_once()

    @patch("app.agent.tools.memory_tools.exception_tracker")
    @patch("app.agent.tools.memory_tools.get_memory_instance")
    @patch("app.agent.tools.memory_tools.trace_operation")
    def test_retrieve_memories_error(self, mock_trace, mock_get_memory, mock_tracker):
        """Test memory retrieval with error"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_memory = Mock()
        mock_memory.search = Mock(side_effect=Exception("Search error"))
        mock_get_memory.return_value = mock_memory

        # Execute
        result = _retrieve_memories_impl(
            query="test query",
            user_id="user123"
        )

        # Verify error handling
        assert len(result) == 1
        assert "error" in result[0]
        mock_tracker.track_exception.assert_called_once()
        mock_span.set_attribute.assert_any_call("error", True)


class TestGetStoreMemoryTool:
    """Test get_store_memory_tool function"""

    @pytest.mark.asyncio
    @patch("app.agent.tools.memory_tools.get_metrics_manager")
    @patch("app.services.redis_streams.RedisStreamsManager")
    async def test_store_memory_tool_success(self, mock_streams_manager_class, mock_metrics):
        """Test store memory tool success"""
        # Setup
        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_producer = AsyncMock()
        mock_producer.send_memory_request = AsyncMock()

        mock_streams_manager = Mock()
        mock_streams_manager.producer = mock_producer
        mock_streams_manager_class.return_value = mock_streams_manager

        # Get tool
        tool = get_store_memory_tool(
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000"
        )

        # Execute
        result = await tool.ainvoke({
            "text": "Test memory",
            "additional_metadata": {"key": "value"}
        })

        # Verify
        assert result["isError"] is False
        assert "Memory sent to store" in result["message"]
        mock_producer.send_memory_request.assert_called_once()
        mock_metrics_manager.record_memory_operation.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.agent.tools.memory_tools.exception_tracker")
    @patch("app.agent.tools.memory_tools.get_metrics_manager")
    @patch("app.services.redis_streams.RedisStreamsManager")
    async def test_store_memory_tool_error(self, mock_streams_manager_class, mock_metrics, mock_tracker):
        """Test store memory tool with error"""
        # Setup
        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_streams_manager_class.side_effect = Exception("Redis error")

        # Get tool
        tool = get_store_memory_tool(user_id="user123")

        # Execute
        result = await tool.ainvoke({"text": "Test memory"})

        # Verify
        assert result["isError"] is True
        assert "error" in result
        mock_tracker.track_exception.assert_called_once()
        mock_metrics_manager.record_tool_error.assert_called_once()


class TestGetRetrieveMemoriesTool:
    """Test get_retrieve_memories_tool function"""

    @patch("app.agent.tools.memory_tools.get_metrics_manager")
    @patch("app.agent.tools.memory_tools._retrieve_memories_impl")
    def test_retrieve_memories_tool_success(self, mock_retrieve_impl, mock_metrics):
        """Test retrieve memories tool success"""
        # Setup
        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_retrieve_impl.return_value = [
            {"memory": "Memory 1", "score": 0.9},
            {"memory": "Memory 2", "score": 0.8}
        ]

        # Get tool
        tool = get_retrieve_memories_tool(
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000"
        )

        # Execute
        result = tool.invoke({"query": "test query", "limit": 5})

        # Verify
        assert result["isError"] is False
        assert "memories" in result
        assert len(result["memories"]) == 2
        mock_retrieve_impl.assert_called_once_with(
            query="test query",
            user_id="user123",
            conversation_id="conv456",
            agent_id="agent789",
            organisation_id="org000",
            limit=5
        )
        mock_metrics_manager.record_memory_operation.assert_called_once()

    @patch("app.agent.tools.memory_tools.get_metrics_manager")
    @patch("app.agent.tools.memory_tools._retrieve_memories_impl")
    def test_retrieve_memories_tool_with_error_result(self, mock_retrieve_impl, mock_metrics):
        """Test retrieve memories tool with error in result"""
        # Setup
        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_retrieve_impl.return_value = [{"error": "Search failed"}]

        # Get tool
        tool = get_retrieve_memories_tool(user_id="user123")

        # Execute
        result = tool.invoke({"query": "test query"})

        # Verify
        assert result["isError"] is True
        assert "error" in result

    @patch("app.agent.tools.memory_tools.get_metrics_manager")
    @patch("app.agent.tools.memory_tools._retrieve_memories_impl")
    def test_retrieve_memories_tool_exception(self, mock_retrieve_impl, mock_metrics):
        """Test retrieve memories tool with exception"""
        # Setup
        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_retrieve_impl.side_effect = Exception("Retrieval error")

        # Get tool
        tool = get_retrieve_memories_tool(user_id="user123")

        # Execute
        result = tool.invoke({"query": "test query"})

        # Verify
        assert result["isError"] is True
        assert "error" in result
        mock_metrics_manager.record_tool_error.assert_called_once()


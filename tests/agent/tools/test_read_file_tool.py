"""Tests for read_file_tool module"""

import pytest
from unittest.mock import Mo<PERSON>, Async<PERSON>ock, patch, MagicMock, mock_open
import aiohttp
import asyncio
import tempfile
import os

from app.agent.tools.read_file_tool import (
    download_file_with_retry,
    parse_pdf,
    parse_csv,
    parse_docx,
    parse_txt,
    get_file_extension,
    read_file,
)


class TestGetFileExtension:
    """Test get_file_extension function"""

    def test_pdf_extension(self):
        """Test extracting PDF extension"""
        assert get_file_extension("document.pdf") == ".pdf"
        assert get_file_extension("document.PDF") == ".pdf"

    def test_csv_extension(self):
        """Test extracting CSV extension"""
        assert get_file_extension("data.csv") == ".csv"

    def test_docx_extension(self):
        """Test extracting DOCX extension"""
        assert get_file_extension("report.docx") == ".docx"

    def test_txt_extension(self):
        """Test extracting TXT extension"""
        assert get_file_extension("notes.txt") == ".txt"

    def test_no_extension(self):
        """Test file with no extension"""
        assert get_file_extension("filename") == ""


class TestParseTxt:
    """Test parse_txt function"""

    @patch("builtins.open", new_callable=mock_open, read_data="Line 1\nLine 2\n\nLine 4")
    def test_parse_txt_success(self, mock_file):
        """Test successful TXT parsing"""
        result = parse_txt("/tmp/test.txt")

        assert result["type"] == "txt"
        assert result["total_lines"] == 4
        assert result["non_empty_lines"] == 3
        assert "Line 1" in result["full_text"]
        assert "Line 2" in result["full_text"]

    @patch("builtins.open", side_effect=FileNotFoundError("File not found"))
    @patch("app.agent.tools.read_file_tool.exception_tracker")
    def test_parse_txt_file_not_found(self, mock_tracker, mock_file):
        """Test TXT parsing with file not found"""
        with pytest.raises(Exception, match="Failed to parse TXT"):
            parse_txt("/tmp/nonexistent.txt")

        mock_tracker.track_exception.assert_called_once()


class TestParseCsv:
    """Test parse_csv function"""

    @patch("builtins.open", new_callable=mock_open, read_data="name,age\nAlice,30\nBob,25")
    def test_parse_csv_success(self, mock_file):
        """Test successful CSV parsing"""
        result = parse_csv("/tmp/test.csv")

        assert result["type"] == "csv"
        assert result["total_rows"] == 2
        assert result["headers"] == ["name", "age"]
        assert len(result["data"]) == 2
        assert "name, age" in result["text_representation"]

    @patch("builtins.open", side_effect=Exception("Read error"))
    @patch("app.agent.tools.read_file_tool.exception_tracker")
    def test_parse_csv_error(self, mock_tracker, mock_file):
        """Test CSV parsing with error"""
        with pytest.raises(Exception, match="Failed to parse CSV"):
            parse_csv("/tmp/test.csv")

        mock_tracker.track_exception.assert_called_once()


class TestParsePdf:
    """Test parse_pdf function"""

    @patch("pypdf.PdfReader")
    def test_parse_pdf_success(self, mock_pdf_reader):
        """Test successful PDF parsing"""
        # Setup mock
        mock_page1 = Mock()
        mock_page1.extract_text.return_value = "Page 1 content"

        mock_page2 = Mock()
        mock_page2.extract_text.return_value = "Page 2 content"

        mock_reader = Mock()
        mock_reader.pages = [mock_page1, mock_page2]
        mock_pdf_reader.return_value = mock_reader

        # Execute
        result = parse_pdf("/tmp/test.pdf")

        # Verify
        assert result["type"] == "pdf"
        assert result["total_pages"] == 2
        assert result["pages_with_content"] == 2
        assert "[Page 1]" in result["full_text"]
        assert "Page 1 content" in result["full_text"]

    @patch("pypdf.PdfReader", side_effect=Exception("PDF error"))
    @patch("app.agent.tools.read_file_tool.exception_tracker")
    def test_parse_pdf_error(self, mock_tracker, mock_pdf_reader):
        """Test PDF parsing with error"""
        with pytest.raises(Exception, match="Failed to parse PDF"):
            parse_pdf("/tmp/test.pdf")

        mock_tracker.track_exception.assert_called_once()


class TestParseDocx:
    """Test parse_docx function"""

    @patch("docx.Document")
    def test_parse_docx_success(self, mock_document):
        """Test successful DOCX parsing"""
        # Setup mock
        mock_para1 = Mock()
        mock_para1.text = "Paragraph 1"

        mock_para2 = Mock()
        mock_para2.text = "Paragraph 2"

        mock_doc = Mock()
        mock_doc.paragraphs = [mock_para1, mock_para2]
        mock_doc.tables = []
        mock_document.return_value = mock_doc

        # Execute
        result = parse_docx("/tmp/test.docx")

        # Verify
        assert result["type"] == "docx"
        assert result["total_paragraphs"] == 2
        assert result["total_tables"] == 0
        assert "Paragraph 1" in result["full_text"]

    @patch("docx.Document")
    def test_parse_docx_with_tables(self, mock_document):
        """Test DOCX parsing with tables"""
        # Setup mock
        mock_para = Mock()
        mock_para.text = "Document text"

        # Mock table
        mock_cell1 = Mock()
        mock_cell1.text = "Cell 1"
        mock_cell2 = Mock()
        mock_cell2.text = "Cell 2"

        mock_row = Mock()
        mock_row.cells = [mock_cell1, mock_cell2]

        mock_table = Mock()
        mock_table.rows = [mock_row]

        mock_doc = Mock()
        mock_doc.paragraphs = [mock_para]
        mock_doc.tables = [mock_table]
        mock_document.return_value = mock_doc

        # Execute
        result = parse_docx("/tmp/test.docx")

        # Verify
        assert result["type"] == "docx"
        assert result["total_tables"] == 1
        assert len(result["tables"]) == 1
        assert result["tables"][0]["table"] == 1

    @patch("docx.Document", side_effect=Exception("DOCX error"))
    @patch("app.agent.tools.read_file_tool.exception_tracker")
    def test_parse_docx_error(self, mock_tracker, mock_document):
        """Test DOCX parsing with error"""
        with pytest.raises(Exception, match="Failed to parse DOCX"):
            parse_docx("/tmp/test.docx")

        mock_tracker.track_exception.assert_called_once()


class TestReadFile:
    """Test read_file tool function"""

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    @patch("app.agent.tools.read_file_tool.download_file_with_retry")
    @patch("app.agent.tools.read_file_tool.parse_pdf")
    @patch("os.unlink")
    @patch("tempfile.NamedTemporaryFile")
    async def test_read_pdf_file_success(
        self, mock_tempfile, mock_unlink, mock_parse_pdf,
        mock_download, mock_trace, mock_metrics
    ):
        """Test reading PDF file successfully"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_download.return_value = b"pdf content"

        mock_parse_pdf.return_value = {
            "type": "pdf",
            "total_pages": 2,
            "pages_with_content": 2,
            "full_text": "Page 1 content\n\nPage 2 content"
        }

        mock_temp = Mock()
        mock_temp.name = "/tmp/test.pdf"
        mock_temp.write = Mock()
        mock_temp.__enter__ = Mock(return_value=mock_temp)
        mock_temp.__exit__ = Mock()
        mock_tempfile.return_value = mock_temp

        # Execute
        result = await read_file.ainvoke(
            {"file_name": "document.pdf", "file_url": "https://example.com/document.pdf"}
        )

        # Verify
        assert "📄 File: document.pdf" in result
        assert "📋 Type: PDF" in result
        assert "📊 Total Pages: 2" in result
        mock_download.assert_called_once_with("https://example.com/document.pdf")
        mock_parse_pdf.assert_called_once()
        mock_metrics_manager.record_tool_execution.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    @patch("app.agent.tools.read_file_tool.download_file_with_retry")
    @patch("app.agent.tools.read_file_tool.parse_csv")
    @patch("os.unlink")
    @patch("tempfile.NamedTemporaryFile")
    async def test_read_csv_file_success(
        self, mock_tempfile, mock_unlink, mock_parse_csv,
        mock_download, mock_trace, mock_metrics
    ):
        """Test reading CSV file successfully"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_download.return_value = b"csv content"

        mock_parse_csv.return_value = {
            "type": "csv",
            "total_rows": 2,
            "headers": ["name", "age"],
            "data": [{"name": "Alice", "age": "30"}],
            "text_representation": "name, age\nAlice, 30"
        }

        mock_temp = Mock()
        mock_temp.name = "/tmp/test.csv"
        mock_temp.write = Mock()
        mock_temp.__enter__ = Mock(return_value=mock_temp)
        mock_temp.__exit__ = Mock()
        mock_tempfile.return_value = mock_temp

        # Execute
        result = await read_file.ainvoke(
            {"file_name": "data.csv", "file_url": "https://example.com/data.csv"}
        )

        # Verify
        assert "📄 File: data.csv" in result
        assert "📋 Type: CSV" in result
        assert "📊 Total Rows: 2" in result
        mock_parse_csv.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    async def test_read_file_unsupported_type(self, mock_trace, mock_metrics):
        """Test reading unsupported file type"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        # Execute - call the underlying function directly
        result = await read_file.ainvoke(
            {"file_name": "file.xyz", "file_url": "https://example.com/file.xyz"}
        )

        # Verify
        assert "❌ Error: Unsupported file type" in result
        assert ".xyz" in result
        mock_metrics_manager.record_tool_error.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    @patch("app.agent.tools.read_file_tool.download_file_with_retry")
    @patch("app.agent.tools.read_file_tool.parse_docx")
    @patch("os.unlink")
    @patch("tempfile.NamedTemporaryFile")
    async def test_read_docx_file_success(
        self, mock_tempfile, mock_unlink, mock_parse_docx,
        mock_download, mock_trace, mock_metrics
    ):
        """Test reading DOCX file successfully"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_download.return_value = b"docx content"

        mock_parse_docx.return_value = {
            "type": "docx",
            "total_paragraphs": 3,
            "total_tables": 1,
            "full_text": "Document content",
            "tables": [{"table": 1, "content": [["Cell 1", "Cell 2"]]}]
        }

        mock_temp = Mock()
        mock_temp.name = "/tmp/test.docx"
        mock_temp.write = Mock()
        mock_temp.__enter__ = Mock(return_value=mock_temp)
        mock_temp.__exit__ = Mock()
        mock_tempfile.return_value = mock_temp

        # Execute
        result = await read_file.ainvoke(
            {"file_name": "report.docx", "file_url": "https://example.com/report.docx"}
        )

        # Verify
        assert "📄 File: report.docx" in result
        assert "📋 Type: DOCX" in result
        assert "📊 Total Paragraphs: 3" in result
        assert "📝 Total Tables: 1" in result
        assert "📊 Tables:" in result
        mock_parse_docx.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    @patch("app.agent.tools.read_file_tool.download_file_with_retry")
    @patch("app.agent.tools.read_file_tool.parse_txt")
    @patch("os.unlink")
    @patch("tempfile.NamedTemporaryFile")
    async def test_read_txt_file_success(
        self, mock_tempfile, mock_unlink, mock_parse_txt,
        mock_download, mock_trace, mock_metrics
    ):
        """Test reading TXT file successfully"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_download.return_value = b"text content"

        mock_parse_txt.return_value = {
            "type": "txt",
            "total_lines": 10,
            "non_empty_lines": 8,
            "full_text": "Text file content"
        }

        mock_temp = Mock()
        mock_temp.name = "/tmp/test.txt"
        mock_temp.write = Mock()
        mock_temp.__enter__ = Mock(return_value=mock_temp)
        mock_temp.__exit__ = Mock()
        mock_tempfile.return_value = mock_temp

        # Execute
        result = await read_file.ainvoke(
            {"file_name": "notes.txt", "file_url": "https://example.com/notes.txt"}
        )

        # Verify
        assert "📄 File: notes.txt" in result
        assert "📋 Type: TXT" in result
        assert "📊 Total Lines: 10" in result
        mock_parse_txt.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    @patch("app.agent.tools.read_file_tool.download_file_with_retry")
    async def test_read_file_download_error(self, mock_download, mock_trace, mock_metrics):
        """Test read_file handles download errors"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_download.side_effect = Exception("Download failed")

        # Execute
        result = await read_file.ainvoke(
            {"file_name": "document.pdf", "file_url": "https://example.com/document.pdf"}
        )

        # Verify
        assert "❌ Error reading file" in result
        mock_metrics_manager.record_tool_error.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    @patch("app.agent.tools.read_file_tool.download_file_with_retry")
    @patch("app.agent.tools.read_file_tool.parse_pdf")
    @patch("os.unlink")
    @patch("tempfile.NamedTemporaryFile")
    async def test_read_file_parse_error(
        self, mock_tempfile, mock_unlink, mock_parse_pdf,
        mock_download, mock_trace, mock_metrics
    ):
        """Test read_file handles parsing errors"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_download.return_value = b"pdf content"
        mock_parse_pdf.side_effect = Exception("Failed to parse PDF: Corrupted file")

        mock_temp = Mock()
        mock_temp.name = "/tmp/test.pdf"
        mock_temp.write = Mock()
        mock_temp.__enter__ = Mock(return_value=mock_temp)
        mock_temp.__exit__ = Mock()
        mock_tempfile.return_value = mock_temp

        # Execute
        result = await read_file.ainvoke(
            {"file_name": "document.pdf", "file_url": "https://example.com/document.pdf"}
        )

        # Verify
        assert "❌ Error reading file" in result
        assert "Corrupted file" in result
        mock_metrics_manager.record_tool_error.assert_called_once()


class TestDownloadFileWithRetry:
    """Test download_file_with_retry function"""

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    @patch("app.agent.tools.read_file_tool.aiohttp.ClientSession")
    async def test_download_timeout_retry(self, mock_session, mock_trace, mock_metrics):
        """Test download with timeout and retry"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        # First attempt times out, second succeeds
        mock_response_success = AsyncMock()
        mock_response_success.status = 200
        mock_response_success.read = AsyncMock(return_value=b"file content")

        mock_session_instance = AsyncMock()
        mock_session_instance.__aenter__ = AsyncMock(return_value=mock_session_instance)
        mock_session_instance.__aexit__ = AsyncMock()

        # First call raises timeout, second succeeds
        mock_get_call = MagicMock()
        mock_get_call.return_value.__aenter__ = AsyncMock(side_effect=[
            asyncio.TimeoutError(),
            mock_response_success
        ])
        mock_get_call.return_value.__aexit__ = AsyncMock()
        mock_session_instance.get = mock_get_call

        mock_session.return_value = mock_session_instance

        # Execute
        result = await download_file_with_retry("https://example.com/file.pdf", max_retries=2)

        # Verify
        assert result == b"file content"

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.get_exception_tracker")
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    @patch("app.agent.tools.read_file_tool.aiohttp.ClientSession")
    async def test_download_timeout_max_retries(self, mock_session, mock_trace, mock_metrics, mock_tracker):
        """Test download fails after max retries due to timeout"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_tracker_instance = Mock()
        mock_tracker.return_value = mock_tracker_instance

        mock_session_instance = AsyncMock()
        mock_session_instance.__aenter__ = AsyncMock(side_effect=asyncio.TimeoutError())
        mock_session.return_value = mock_session_instance

        # Execute and verify
        with pytest.raises(Exception, match="Failed to download file after .* attempts: Timeout"):
            await download_file_with_retry("https://example.com/file.pdf", max_retries=2)

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.get_exception_tracker")
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    @patch("app.agent.tools.read_file_tool.aiohttp.ClientSession")
    async def test_download_http_error_retry(self, mock_session, mock_trace, mock_metrics, mock_tracker):
        """Test download with HTTP error and retry"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_tracker_instance = Mock()
        mock_tracker.return_value = mock_tracker_instance

        # First attempt returns 500, second succeeds
        mock_response_error = AsyncMock()
        mock_response_error.status = 500

        mock_response_success = AsyncMock()
        mock_response_success.status = 200
        mock_response_success.read = AsyncMock(return_value=b"file content")

        mock_session_instance = AsyncMock()
        mock_session_instance.__aenter__ = AsyncMock(return_value=mock_session_instance)
        mock_session_instance.__aexit__ = AsyncMock()

        call_count = [0]
        async def get_side_effect(*args, **kwargs):
            call_count[0] += 1
            if call_count[0] == 1:
                return mock_response_error
            return mock_response_success

        mock_get_call = MagicMock()
        mock_get_call.return_value.__aenter__ = AsyncMock(side_effect=get_side_effect)
        mock_get_call.return_value.__aexit__ = AsyncMock()
        mock_session_instance.get = mock_get_call

        mock_session.return_value = mock_session_instance

        # Execute
        result = await download_file_with_retry("https://example.com/file.pdf", max_retries=2)

        # Verify
        assert result == b"file content"

    @pytest.mark.asyncio
    @patch("app.agent.tools.read_file_tool.exception_tracker")
    @patch("app.agent.tools.read_file_tool.get_metrics_manager")
    @patch("app.agent.tools.read_file_tool.trace_operation")
    @patch("app.agent.tools.read_file_tool.aiohttp.ClientSession")
    async def test_download_exception_max_retries(self, mock_session, mock_trace, mock_metrics, mock_tracker):
        """Test download fails after max retries due to exception"""
        # Setup
        mock_span = MagicMock()
        mock_trace.return_value.__enter__.return_value = mock_span

        mock_metrics_manager = Mock()
        mock_metrics.return_value = mock_metrics_manager

        mock_tracker.track_exception = Mock()

        mock_session_instance = AsyncMock()
        mock_session_instance.__aenter__ = AsyncMock(side_effect=Exception("Network error"))
        mock_session.return_value = mock_session_instance

        # Execute and verify
        with pytest.raises(Exception, match="Failed to download file after .* attempts"):
            await download_file_with_retry("https://example.com/file.pdf", max_retries=2)

        # Verify exception was tracked
        mock_tracker.track_exception.assert_called_once()

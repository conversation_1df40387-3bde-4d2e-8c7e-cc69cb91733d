"""Tests for workflow execution tool."""

import pytest
from unittest.mock import MagicM<PERSON>, patch, AsyncMock

from app.agent.tools.workflow_execution import (
    _execute_workflow,
    workflow_execution,
    WORKFLOW_EXECUTION_TOOL_DESCRIPTION,
)


class TestExecuteWorkflow:
    """Tests for _execute_workflow function."""

    @pytest.mark.asyncio
    async def test_execute_workflow_success(self):
        """Test successful workflow execution."""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"correlationId": "corr-123"}

        mock_http_client = MagicMock()
        mock_http_client.post = AsyncMock(return_value=mock_response)

        with patch("app.agent.tools.workflow_execution.get_http_client", return_value=mock_http_client):
            with patch("app.agent.tools.workflow_execution.get_settings") as mock_settings:
                mock_settings.return_value.external_api_gateway.api_url = "http://test.com"
                mock_settings.return_value.external_api_gateway.token = "test-token"

                result = await _execute_workflow(
                    workflow_id="wf-123",
                    user_id="user-456",
                    organisation_id="org-789",
                    payload={"key": "value"},
                )

                assert result == "corr-123"

    @pytest.mark.asyncio
    async def test_execute_workflow_with_exception(self):
        """Test workflow execution with exception."""
        mock_http_client = MagicMock()
        mock_http_client.post = AsyncMock(side_effect=Exception("API Error"))

        with patch("app.agent.tools.workflow_execution.get_http_client", return_value=mock_http_client):
            with patch("app.agent.tools.workflow_execution.get_settings") as mock_settings:
                mock_settings.return_value.external_api_gateway.api_url = "http://test.com"
                mock_settings.return_value.external_api_gateway.token = "test-token"

                with pytest.raises(Exception):
                    await _execute_workflow(
                        workflow_id="wf-123",
                        user_id="user-456",
                        organisation_id="org-789",
                        payload={"key": "value"},
                    )


class TestWorkflowExecution:
    """Tests for workflow_execution tool."""

    def test_tool_exists(self):
        """Test that workflow_execution tool exists."""
        assert workflow_execution is not None
        assert workflow_execution.name == "workflow_execution"

    def test_tool_has_description(self):
        """Test that the tool has a description."""
        assert workflow_execution.description is not None
        assert len(workflow_execution.description) > 0

    def test_tool_description_constant(self):
        """Test that the tool description constant exists."""
        assert WORKFLOW_EXECUTION_TOOL_DESCRIPTION is not None
        assert "workflow" in WORKFLOW_EXECUTION_TOOL_DESCRIPTION.lower()

    @pytest.mark.asyncio
    async def test_tool_missing_user_id(self):
        """Test tool execution with missing user_id."""
        config = {"configurable": {}}

        result = await workflow_execution.coroutine(
            workflow_id="wf-123",
            workflow_name="Test Workflow",
            organisation_id="org-456",
            payload={"key": "value"},
            tool_call_id="tool-123",
            config=config,
        )

        # Should return error about missing user_id
        assert result is not None

    @pytest.mark.asyncio
    async def test_tool_missing_conversation_id(self):
        """Test tool execution with missing conversation_id."""
        config = {"configurable": {"user_id": "user-123"}}

        result = await workflow_execution.coroutine(
            workflow_id="wf-123",
            workflow_name="Test Workflow",
            organisation_id="org-456",
            payload={"key": "value"},
            tool_call_id="tool-123",
            config=config,
        )

        # Should return error about missing conversation_id
        assert result is not None

    @pytest.mark.asyncio
    async def test_tool_successful_execution(self):
        """Test successful workflow execution."""
        config = {"configurable": {"user_id": "user-123", "conversation_id": "conv-456"}}

        mock_writer = MagicMock()

        with patch("app.agent.tools.workflow_execution.get_stream_writer", return_value=mock_writer):
            with patch("app.agent.tools.workflow_execution._execute_workflow", new_callable=AsyncMock) as mock_execute:
                mock_execute.return_value = "corr-123"

                with patch("app.agent.tools.workflow_execution._stream_workflow_events", new_callable=AsyncMock) as mock_stream:
                    mock_stream.return_value = (
                        {"event_type": "workflow.completed", "workflow_status": "completed"},
                        [{"type": "step", "name": "Step 1", "status": "completed"}],
                        "completed"
                    )

                    result = await workflow_execution.coroutine(
                        workflow_id="wf-123",
                        workflow_name="Test Workflow",
                        organisation_id="org-456",
                        payload={"key": "value"},
                        tool_call_id="tool-123",
                        config=config,
                    )

                    assert result is not None
                    import json
                    message = result.update["messages"][0]
                    content = json.loads(message.content)
                    assert content["isError"] is False
                    assert content["correlation_id"] == "corr-123"
                    assert content["workflow_status"] == "completed"

    @pytest.mark.asyncio
    async def test_tool_timeout_error(self):
        """Test workflow execution with timeout error."""
        config = {"configurable": {"user_id": "user-123", "conversation_id": "conv-456"}}

        mock_writer = MagicMock()

        with patch("app.agent.tools.workflow_execution.get_stream_writer", return_value=mock_writer):
            with patch("app.agent.tools.workflow_execution._execute_workflow", new_callable=AsyncMock) as mock_execute:
                mock_execute.side_effect = TimeoutError("Workflow timed out")

                result = await workflow_execution.coroutine(
                    workflow_id="wf-123",
                    workflow_name="Test Workflow",
                    organisation_id="org-456",
                    payload={"key": "value"},
                    tool_call_id="tool-123",
                    config=config,
                )

                assert result is not None
                import json
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is True
                assert "timed out" in content["error"].lower()

    @pytest.mark.asyncio
    async def test_tool_general_exception(self):
        """Test workflow execution with general exception."""
        config = {"configurable": {"user_id": "user-123", "conversation_id": "conv-456"}}

        mock_writer = MagicMock()

        with patch("app.agent.tools.workflow_execution.get_stream_writer", return_value=mock_writer):
            with patch("app.agent.tools.workflow_execution._execute_workflow", new_callable=AsyncMock) as mock_execute:
                mock_execute.side_effect = Exception("Unexpected error")

                result = await workflow_execution.coroutine(
                    workflow_id="wf-123",
                    workflow_name="Test Workflow",
                    organisation_id="org-456",
                    payload={"key": "value"},
                    tool_call_id="tool-123",
                    config=config,
                )

                assert result is not None
                import json
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is True


class TestStreamWorkflowEvents:
    """Tests for _stream_workflow_events function."""

    @pytest.mark.asyncio
    async def test_stream_workflow_completed(self):
        """Test streaming workflow events until completion."""
        from app.agent.tools.workflow_execution import _stream_workflow_events

        mock_writer = MagicMock()

        # Create mock response that yields SSE events
        async def mock_aiter_lines():
            yield "event: workflow_event"
            yield 'data: {"event_type": "step.started", "step_name": "Step 1"}'
            yield ""
            yield "event: workflow_event"
            yield 'data: {"event_type": "workflow.completed", "workflow_status": "completed"}'

        mock_response = AsyncMock()
        mock_response.raise_for_status = MagicMock()
        mock_response.aiter_lines = mock_aiter_lines

        mock_http_client = MagicMock()
        mock_http_client.stream = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("app.agent.tools.workflow_execution.get_http_client", return_value=mock_http_client):
            with patch("app.agent.tools.workflow_execution.get_settings") as mock_settings:
                mock_settings.return_value.external_api_gateway.api_url = "http://test.com"
                mock_settings.return_value.external_api_gateway.token = "test-token"

                result, events, status = await _stream_workflow_events(
                    correlation_id="corr-123",
                    writer=mock_writer,
                    tool_call_id="tool-123",
                    workflow_name="Test Workflow",
                    workflow_id="wf-123",
                )

                assert status == "completed"
                assert result is not None

    @pytest.mark.asyncio
    async def test_stream_workflow_failed(self):
        """Test streaming workflow events with failure."""
        from app.agent.tools.workflow_execution import _stream_workflow_events

        mock_writer = MagicMock()

        # Create mock response that yields SSE events
        async def mock_aiter_lines():
            yield "event: workflow_event"
            yield 'data: {"event_type": "workflow.failed", "workflow_status": "failed", "error": "Something went wrong"}'

        mock_response = AsyncMock()
        mock_response.raise_for_status = MagicMock()
        mock_response.aiter_lines = mock_aiter_lines

        mock_http_client = MagicMock()
        mock_http_client.stream = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("app.agent.tools.workflow_execution.get_http_client", return_value=mock_http_client):
            with patch("app.agent.tools.workflow_execution.get_settings") as mock_settings:
                mock_settings.return_value.external_api_gateway.api_url = "http://test.com"
                mock_settings.return_value.external_api_gateway.token = "test-token"

                result, events, status = await _stream_workflow_events(
                    correlation_id="corr-123",
                    writer=mock_writer,
                    tool_call_id="tool-123",
                    workflow_name="Test Workflow",
                    workflow_id="wf-123",
                )

                assert status == "failed"

    @pytest.mark.asyncio
    async def test_stream_keep_alive_skipped(self):
        """Test that keep-alive events are skipped."""
        from app.agent.tools.workflow_execution import _stream_workflow_events

        mock_writer = MagicMock()

        # Create mock response that yields SSE events including keep-alive
        async def mock_aiter_lines():
            yield "data: keep-alive"
            yield ""
            yield "event: workflow_event"
            yield 'data: {"event_type": "workflow.completed", "workflow_status": "completed"}'

        mock_response = AsyncMock()
        mock_response.raise_for_status = MagicMock()
        mock_response.aiter_lines = mock_aiter_lines

        mock_http_client = MagicMock()
        mock_http_client.stream = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("app.agent.tools.workflow_execution.get_http_client", return_value=mock_http_client):
            with patch("app.agent.tools.workflow_execution.get_settings") as mock_settings:
                mock_settings.return_value.external_api_gateway.api_url = "http://test.com"
                mock_settings.return_value.external_api_gateway.token = "test-token"

                result, events, status = await _stream_workflow_events(
                    correlation_id="corr-123",
                    writer=mock_writer,
                    tool_call_id="tool-123",
                    workflow_name="Test Workflow",
                    workflow_id="wf-123",
                )

                assert status == "completed"


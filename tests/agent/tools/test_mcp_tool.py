"""Tests for app/agent/tools/mcp_tool.py"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from app.agent.tools.mcp_tool import execute_mcp


class TestExecuteMcp:
    """Tests for execute_mcp tool"""

    @pytest.fixture
    def mock_config(self):
        """Create mock config with user_id"""
        return {
            "configurable": {
                "user_id": "user-123",
                "organisation_id": "org-456",
            }
        }

    @pytest.fixture
    def mock_metrics_manager(self):
        """Create mock metrics manager"""
        manager = MagicMock()
        manager.record_tool_execution = MagicMock()
        manager.record_tool_error = MagicMock()
        return manager

    @pytest.mark.asyncio
    async def test_execute_mcp_missing_user_id(self, mock_metrics_manager):
        """Test that missing user_id returns error"""
        config = {"configurable": {}}

        with patch(
            "app.agent.tools.mcp_tool.get_metrics_manager",
            return_value=mock_metrics_manager
        ):
            result = await execute_mcp.coroutine(
                mcp_name_slug="test-mcp",
                tool_name="test-tool",
                tool_parameters={},
                mcp_name="Test MCP",
                mcp_description="A test MCP",
                config=config
            )

            assert result["isError"] is True
            assert "user_id" in result["error"]
            mock_metrics_manager.record_tool_error.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_mcp_missing_mcp_name(self, mock_config, mock_metrics_manager):
        """Test that missing mcp_name returns error"""
        with patch(
            "app.agent.tools.mcp_tool.get_metrics_manager",
            return_value=mock_metrics_manager
        ):
            result = await execute_mcp.coroutine(
                mcp_name_slug="test-mcp",
                tool_name="test-tool",
                tool_parameters={},
                mcp_name="",  # Empty
                mcp_description="A test MCP",
                config=mock_config
            )

            assert result["isError"] is True
            assert "mcp_name" in result["error"]
            mock_metrics_manager.record_tool_error.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_mcp_missing_mcp_description(self, mock_config, mock_metrics_manager):
        """Test that missing mcp_description returns error"""
        with patch(
            "app.agent.tools.mcp_tool.get_metrics_manager",
            return_value=mock_metrics_manager
        ):
            result = await execute_mcp.coroutine(
                mcp_name_slug="test-mcp",
                tool_name="test-tool",
                tool_parameters={},
                mcp_name="Test MCP",
                mcp_description="",  # Empty
                config=mock_config
            )

            assert result["isError"] is True
            assert "mcp_description" in result["error"]
            mock_metrics_manager.record_tool_error.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_mcp_success(self, mock_config, mock_metrics_manager):
        """Test successful MCP execution"""
        mock_client = MagicMock()
        mock_client.execute_tool = AsyncMock(return_value={"data": "test result"})

        with patch(
            "app.agent.tools.mcp_tool.get_metrics_manager",
            return_value=mock_metrics_manager
        ):
            with patch(
                "app.agent.tools.mcp_tool.MCPClient",
                return_value=mock_client
            ):
                result = await execute_mcp.coroutine(
                    mcp_name_slug="test-mcp",
                    tool_name="test-tool",
                    tool_parameters={"param1": "value1"},
                    mcp_name="Test MCP",
                    mcp_description="A test MCP",
                    mcp_logo="https://example.com/logo.png",
                    config=mock_config
                )

                assert result["isError"] is False
                assert result["result"] == {"data": "test result"}
                assert result["mcp_name"] == "Test MCP"
                assert result["mcp_description"] == "A test MCP"
                assert result["mcp_logo"] == "https://example.com/logo.png"
                mock_client.execute_tool.assert_called_once_with(
                    mcp_name_slug="test-mcp",
                    tool_name="test-tool",
                    tool_parameters={"param1": "value1"},
                )
                mock_metrics_manager.record_tool_execution.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_mcp_exception_handling(self, mock_config, mock_metrics_manager):
        """Test that exceptions are handled properly"""
        mock_client = MagicMock()
        mock_client.execute_tool = AsyncMock(side_effect=Exception("MCP execution failed"))
        mock_exception_tracker = MagicMock()

        with patch(
            "app.agent.tools.mcp_tool.get_metrics_manager",
            return_value=mock_metrics_manager
        ):
            with patch(
                "app.agent.tools.mcp_tool.MCPClient",
                return_value=mock_client
            ):
                with patch(
                    "app.agent.tools.mcp_tool.exception_tracker",
                    mock_exception_tracker
                ):
                    result = await execute_mcp.coroutine(
                        mcp_name_slug="test-mcp",
                        tool_name="test-tool",
                        tool_parameters={},
                        mcp_name="Test MCP",
                        mcp_description="A test MCP",
                        config=mock_config
                    )

                    assert result["isError"] is True
                    assert "MCP execution failed" in result["error"]
                    mock_exception_tracker.track_exception.assert_called_once()
                    mock_metrics_manager.record_tool_error.assert_called_once()


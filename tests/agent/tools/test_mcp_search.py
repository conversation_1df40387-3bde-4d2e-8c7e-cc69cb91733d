"""Tests for app/agent/tools/mcp_search.py"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from langgraph.types import Command
from app.agent.tools.mcp_search import mcp_search


class TestMcpSearch:
    """Tests for mcp_search tool"""

    @pytest.fixture
    def mock_config(self):
        """Create mock config with conversation_id"""
        return {
            "configurable": {
                "user_id": "user-123",
                "organisation_id": "org-456",
                "conversation_id": "conv-789",
            }
        }

    @pytest.fixture
    def mock_metrics_manager(self):
        """Create mock metrics manager"""
        manager = MagicMock()
        manager.record_tool_execution = MagicMock()
        manager.record_tool_error = MagicMock()
        return manager

    @pytest.fixture
    def mock_trace_context(self):
        """Create mock trace context"""
        mock_span = MagicMock()
        mock_span.set_attribute = MagicMock()
        mock_span.record_exception = MagicMock()
        mock_context = MagicMock()
        mock_context.__enter__ = MagicMock(return_value=mock_span)
        mock_context.__exit__ = MagicMock(return_value=None)
        return mock_context

    @pytest.mark.asyncio
    async def test_mcp_search_with_results(
        self, mock_config, mock_metrics_manager, mock_trace_context
    ):
        """Test mcp_search returns results and stores approval data"""
        mock_results = [
            {"type_id": "mcp-1", "name": "Test MCP 1", "description": "Test 1"},
            {"type_id": "mcp-2", "name": "Test MCP 2", "description": "Test 2"},
        ]

        with patch(
            "app.agent.tools.mcp_search.get_metrics_manager",
            return_value=mock_metrics_manager
        ):
            with patch(
                "app.agent.tools.mcp_search.trace_operation",
                return_value=mock_trace_context
            ):
                with patch(
                    "app.agent.tools.mcp_search.provider.search_marketplace",
                    return_value=mock_results
                ):
                    with patch(
                        "app.agent.tools.mcp_search.store_approval_data",
                        new_callable=AsyncMock
                    ) as mock_store:
                        result = await mcp_search.coroutine(
                            query="test query",
                            tool_call_id="call-123",
                            config=mock_config
                        )

                        assert isinstance(result, Command)
                        # Verify store_approval_data was called
                        mock_store.assert_called_once()
                        call_args = mock_store.call_args
                        assert call_args[0][0] == "conv-789"
                        assert call_args[0][1] == "mcp_search"
                        # Verify type_id was renamed to mcp_id
                        assert "mcp_id" in mock_results[0]
                        assert "type_id" not in mock_results[0]

    @pytest.mark.asyncio
    async def test_mcp_search_no_results(
        self, mock_config, mock_metrics_manager, mock_trace_context
    ):
        """Test mcp_search with no results"""
        with patch(
            "app.agent.tools.mcp_search.get_metrics_manager",
            return_value=mock_metrics_manager
        ):
            with patch(
                "app.agent.tools.mcp_search.trace_operation",
                return_value=mock_trace_context
            ):
                with patch(
                    "app.agent.tools.mcp_search.provider.search_marketplace",
                    return_value=[]
                ):
                    result = await mcp_search.coroutine(
                        query="nonexistent query",
                        tool_call_id="call-123",
                        config=mock_config
                    )

                    assert isinstance(result, Command)
                    # Parse the message content
                    message = result.update["messages"][0]
                    content = json.loads(message.content)
                    assert content["isError"] is False
                    assert content["result"] == []
                    assert "No tools found" in content["message"]

    @pytest.mark.asyncio
    async def test_mcp_search_exception_handling(
        self, mock_config, mock_metrics_manager, mock_trace_context
    ):
        """Test mcp_search handles exceptions properly"""
        mock_exception_tracker = MagicMock()

        with patch(
            "app.agent.tools.mcp_search.get_metrics_manager",
            return_value=mock_metrics_manager
        ):
            with patch(
                "app.agent.tools.mcp_search.trace_operation",
                return_value=mock_trace_context
            ):
                with patch(
                    "app.agent.tools.mcp_search.provider.search_marketplace",
                    side_effect=Exception("Search failed")
                ):
                    with patch(
                        "app.agent.tools.mcp_search.exception_tracker",
                        mock_exception_tracker
                    ):
                        result = await mcp_search.coroutine(
                            query="test query",
                            tool_call_id="call-123",
                            config=mock_config
                        )

                        assert isinstance(result, Command)
                        message = result.update["messages"][0]
                        content = json.loads(message.content)
                        assert content["isError"] is True
                        assert "Search failed" in content["error"]
                        mock_exception_tracker.track_exception.assert_called_once()
                        mock_metrics_manager.record_tool_error.assert_called_once()

    @pytest.mark.asyncio
    async def test_mcp_search_without_conversation_id(
        self, mock_metrics_manager, mock_trace_context
    ):
        """Test mcp_search works without conversation_id (no approval data stored)"""
        config = {"configurable": {}}
        mock_results = [
            {"type_id": "mcp-1", "name": "Test MCP 1", "description": "Test 1"},
        ]

        with patch(
            "app.agent.tools.mcp_search.get_metrics_manager",
            return_value=mock_metrics_manager
        ):
            with patch(
                "app.agent.tools.mcp_search.trace_operation",
                return_value=mock_trace_context
            ):
                with patch(
                    "app.agent.tools.mcp_search.provider.search_marketplace",
                    return_value=mock_results
                ):
                    with patch(
                        "app.agent.tools.mcp_search.store_approval_data",
                        new_callable=AsyncMock
                    ) as mock_store:
                        result = await mcp_search.coroutine(
                            query="test query",
                            tool_call_id="call-123",
                            config=config
                        )

                        assert isinstance(result, Command)
                        # store_approval_data should NOT be called without conversation_id
                        mock_store.assert_not_called()


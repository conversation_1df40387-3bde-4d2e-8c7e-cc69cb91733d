"""
Tests for app/agent/tools/web_search_tool.py
"""
import pytest
import json
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from app.agent.tools.web_search_tool import (
    _format_search_response,
    get_web_search_tool,
)


class TestFormatSearchResponse:
    """Test _format_search_response function"""

    def test_format_with_results(self):
        """Test formatting response with results"""
        data = {
            "results": [
                {
                    "id": "result1",
                    "title": "Test Result 1",
                    "url": "https://example.com/1",
                    "publishedDate": "2024-01-01",
                    "text": "This is the text content",
                    "summary": "This is a summary"
                },
                {
                    "id": "result2",
                    "title": "Test Result 2",
                    "url": "https://example.com/2",
                    "publishedDate": "2024-01-02"
                }
            ]
        }
        
        result = _format_search_response(data, "test query")
        parsed = json.loads(result)
        
        assert parsed["query"] == "test query"
        assert len(parsed["results"]) == 2
        assert parsed["results"][0]["id"] == "result1"
        assert parsed["results"][0]["title"] == "Test Result 1"
        assert parsed["results"][0]["text"] == "This is the text content"
        assert parsed["results"][0]["summary"] == "This is a summary"
        assert parsed["results"][1]["id"] == "result2"
        assert "text" not in parsed["results"][1]
        assert "summary" not in parsed["results"][1]

    def test_format_without_results(self):
        """Test formatting response without results key"""
        data = {"error": "No results found"}
        
        result = _format_search_response(data, "test query")
        parsed = json.loads(result)
        
        assert parsed["error"] == "No results found"
        assert "query" not in parsed

    def test_format_with_missing_fields(self):
        """Test formatting with missing optional fields"""
        data = {
            "results": [
                {
                    "title": "Test Result"
                }
            ]
        }
        
        result = _format_search_response(data, "test query")
        parsed = json.loads(result)
        
        assert parsed["results"][0]["id"] == ""
        assert parsed["results"][0]["title"] == "Test Result"
        assert parsed["results"][0]["url"] == ""
        assert parsed["results"][0]["publishedDate"] == ""


class TestGetWebSearchTool:
    """Test get_web_search_tool function"""

    def test_tool_creation(self):
        """Test that tool can be created with user context"""
        tool = get_web_search_tool(
            user_id="user123",
            conversation_id="conv123",
            agent_id="agent123",
            organisation_id="org123"
        )

        assert tool is not None
        assert tool.name == "web_search"
        assert "search" in tool.description.lower()

    def test_tool_creation_minimal(self):
        """Test tool creation with minimal parameters"""
        tool = get_web_search_tool(user_id="user123")

        assert tool is not None
        assert tool.name == "web_search"

    @pytest.mark.asyncio
    async def test_web_search_empty_query(self):
        """Test web search with empty query returns error"""
        tool = get_web_search_tool(user_id="user123")

        with patch("app.agent.tools.web_search_tool.trace_operation") as mock_trace:
            mock_trace.return_value.__enter__ = MagicMock(return_value=MagicMock())
            mock_trace.return_value.__exit__ = MagicMock(return_value=False)

            result = await tool.coroutine(query="", tool_call_id="test-id")

            # Should return error for empty query
            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "empty" in content["error"].lower()

    @pytest.mark.asyncio
    async def test_web_search_success(self):
        """Test successful web search"""
        tool = get_web_search_tool(user_id="user123", conversation_id="conv123")

        mock_response_data = {
            "results": [
                {
                    "id": "result1",
                    "title": "Test Result",
                    "url": "https://example.com",
                    "publishedDate": "2024-01-01",
                    "text": "Test content",
                    "summary": "Test summary"
                }
            ]
        }

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(return_value=mock_response_data)

        mock_session = AsyncMock()
        mock_session.post = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("aiohttp.ClientSession") as mock_client:
            mock_client.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client.return_value.__aexit__ = AsyncMock()
            with patch("app.agent.tools.web_search_tool.store_sources", new_callable=AsyncMock):
                result = await tool.coroutine(query="test query", tool_call_id="test-id", config={"configurable": {"conversation_id": "conv123"}})

                assert result is not None
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is False
                assert len(content["results"]) == 1

    @pytest.mark.asyncio
    async def test_web_search_api_error(self):
        """Test web search with API error"""
        tool = get_web_search_tool(user_id="user123")

        mock_response = AsyncMock()
        mock_response.status = 500
        mock_response.text = AsyncMock(return_value="Internal Server Error")

        mock_session = AsyncMock()
        mock_session.post = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("aiohttp.ClientSession") as mock_client:
            mock_client.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client.return_value.__aexit__ = AsyncMock()

            result = await tool.coroutine(query="test query", tool_call_id="test-id")

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "500" in content["error"]

    @pytest.mark.asyncio
    async def test_web_search_json_parse_error(self):
        """Test web search with JSON parse error"""
        tool = get_web_search_tool(user_id="user123")

        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json = AsyncMock(side_effect=Exception("JSON parse error"))

        mock_session = AsyncMock()
        mock_session.post = MagicMock(return_value=AsyncMock(__aenter__=AsyncMock(return_value=mock_response), __aexit__=AsyncMock()))

        with patch("aiohttp.ClientSession") as mock_client:
            mock_client.return_value.__aenter__ = AsyncMock(return_value=mock_session)
            mock_client.return_value.__aexit__ = AsyncMock()

            result = await tool.coroutine(query="test query", tool_call_id="test-id")

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "parse" in content["error"].lower()

    @pytest.mark.asyncio
    async def test_web_search_connection_error(self):
        """Test web search with connection error"""
        import aiohttp
        tool = get_web_search_tool(user_id="user123")

        # Mock the entire aiohttp.ClientSession to raise on __aenter__
        mock_client_session = AsyncMock()
        mock_client_session.__aenter__ = AsyncMock(side_effect=aiohttp.ClientError("Connection failed"))
        mock_client_session.__aexit__ = AsyncMock()

        with patch("aiohttp.ClientSession", return_value=mock_client_session):
            result = await tool.coroutine(query="test query", tool_call_id="test-id")

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "connection" in content["error"].lower()

    @pytest.mark.asyncio
    async def test_web_search_value_error(self):
        """Test web search with value error"""
        tool = get_web_search_tool(user_id="user123")

        # Mock the entire aiohttp.ClientSession to raise on __aenter__
        mock_client_session = AsyncMock()
        mock_client_session.__aenter__ = AsyncMock(side_effect=ValueError("Invalid value"))
        mock_client_session.__aexit__ = AsyncMock()

        with patch("aiohttp.ClientSession", return_value=mock_client_session):
            result = await tool.coroutine(query="test query", tool_call_id="test-id")

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True

    @pytest.mark.asyncio
    async def test_web_search_unexpected_error(self):
        """Test web search with unexpected error"""
        tool = get_web_search_tool(user_id="user123")

        # Mock the entire aiohttp.ClientSession to raise on __aenter__
        mock_client_session = AsyncMock()
        mock_client_session.__aenter__ = AsyncMock(side_effect=RuntimeError("Unexpected error"))
        mock_client_session.__aexit__ = AsyncMock()

        with patch("aiohttp.ClientSession", return_value=mock_client_session):
            result = await tool.coroutine(query="test query", tool_call_id="test-id")

            assert result is not None
            message = result.update["messages"][0]
            content = json.loads(message.content)
            assert content["isError"] is True
            assert "unexpected" in content["error"].lower()



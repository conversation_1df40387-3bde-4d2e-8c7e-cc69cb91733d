"""Tests for todo.py"""
import pytest
import json
from unittest.mock import Mock, patch
from langgraph.types import Command
from app.agent.tools.todo import write_todos, read_todos
from app.agent.state import Todo


class TestWriteTodos:
    """Test suite for write_todos tool"""

    @patch("app.agent.tools.todo.exception_tracker")
    def test_write_todos_success(self, mock_exception_tracker):
        """Test successful todo writing"""
        todos = [
            Todo(content="Task 1", status="pending"),
            Todo(content="Task 2", status="completed")
        ]

        # Call the underlying function directly
        result = write_todos.func(todos, tool_call_id="call-123")
        
        assert isinstance(result, Command)
        assert "todos" in result.update
        assert "messages" in result.update
        assert len(result.update["messages"]) == 1
        
        message = result.update["messages"][0]
        content = json.loads(message.content)
        assert content["isError"] is False
        assert len(content["todos"]) == 2

    @patch("app.agent.tools.todo.exception_tracker")
    def test_write_todos_empty_list(self, mock_exception_tracker):
        """Test writing empty todo list"""
        todos = []

        result = write_todos.func(todos, tool_call_id="call-123")
        
        assert isinstance(result, Command)
        assert result.update["todos"] == []
        
        message = result.update["messages"][0]
        content = json.loads(message.content)
        assert content["isError"] is False
        assert content["todos"] == []

    @patch("app.agent.tools.todo.exception_tracker")
    def test_write_todos_error_handling(self, mock_exception_tracker):
        """Test error handling in write_todos"""
        # Create a mock that raises an exception when converted to dict
        class BadTodo:
            def __iter__(self):
                raise Exception("Conversion error")

        todos = [BadTodo()]

        result = write_todos.func(todos, tool_call_id="call-123")
        
        assert isinstance(result, Command)
        assert "messages" in result.update
        
        message = result.update["messages"][0]
        content = json.loads(message.content)
        assert content["isError"] is True
        assert "error" in content
        mock_exception_tracker.track_exception.assert_called_once()


class TestReadTodos:
    """Test suite for read_todos tool"""

    @patch("app.agent.tools.todo.exception_tracker")
    def test_read_todos_with_items(self, mock_exception_tracker):
        """Test reading todos when items exist"""
        state = {
            "todos": [
                Todo(content="Task 1", status="pending"),
                Todo(content="Task 2", status="completed")
            ]
        }

        result = read_todos.func(state, tool_call_id="call-123")
        
        content = json.loads(result)
        assert content["isError"] is False
        assert len(content["todos"]) == 2
        assert content["todos"][0]["content"] == "Task 1"
        assert content["todos"][1]["status"] == "completed"

    @patch("app.agent.tools.todo.exception_tracker")
    def test_read_todos_empty_list(self, mock_exception_tracker):
        """Test reading todos when list is empty"""
        state = {"todos": []}

        result = read_todos.func(state, tool_call_id="call-123")

        content = json.loads(result)
        assert content["isError"] is False
        assert content["todos"] == []

    @patch("app.agent.tools.todo.exception_tracker")
    def test_read_todos_no_todos_key(self, mock_exception_tracker):
        """Test reading todos when todos key doesn't exist"""
        state = {}

        result = read_todos.func(state, tool_call_id="call-123")

        content = json.loads(result)
        assert content["isError"] is False
        assert content["todos"] == []

    @patch("app.agent.tools.todo.exception_tracker")
    def test_read_todos_error_handling(self, mock_exception_tracker):
        """Test error handling in read_todos"""
        # Create a state that raises an exception when accessed
        class BadState:
            def get(self, key, default=None):
                raise Exception("State access error")

        state = BadState()

        result = read_todos.func(state, tool_call_id="call-123")

        content = json.loads(result)
        assert content["isError"] is True
        assert "error" in content
        mock_exception_tracker.track_exception.assert_called_once()

    @patch("app.agent.tools.todo.exception_tracker")
    def test_read_todos_single_item(self, mock_exception_tracker):
        """Test reading todos with single item"""
        state = {
            "todos": [Todo(content="Single task", status="in_progress")]
        }

        result = read_todos.func(state, tool_call_id="call-123")
        
        content = json.loads(result)
        assert content["isError"] is False
        assert len(content["todos"]) == 1
        assert content["todos"][0]["content"] == "Single task"
        assert content["todos"][0]["status"] == "in_progress"


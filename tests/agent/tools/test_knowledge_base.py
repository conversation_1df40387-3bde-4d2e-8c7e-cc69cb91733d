"""Tests for app/agent/tools/knowledge_base.py"""

import pytest
import json
from unittest.mock import AsyncMock, MagicMock, patch
from langgraph.types import Command
from app.agent.tools.knowledge_base import search_by_source, resolve_identity


class TestSearchBySource:
    """Tests for search_by_source tool"""

    @pytest.fixture
    def mock_config(self):
        """Create mock config with required fields"""
        return {
            "configurable": {
                "user_id": "user-123",
                "organisation_id": "org-456",
                "conversation_id": "conv-789",
                "kb_source": "GOOGLE_DRIVE",
                "kb_file_ids": [],
                "timezone": "UTC",
            }
        }

    @pytest.fixture
    def mock_metrics_manager(self):
        """Create mock metrics manager"""
        manager = MagicMock()
        manager.record_tool_execution = MagicMock()
        manager.record_tool_error = MagicMock()
        return manager

    @pytest.fixture
    def mock_trace_context(self):
        """Create mock trace context"""
        mock_span = MagicMock()
        mock_span.set_attribute = MagicMock()
        mock_span.record_exception = MagicMock()
        mock_context = MagicMock()
        mock_context.__enter__ = MagicMock(return_value=mock_span)
        mock_context.__exit__ = MagicMock(return_value=None)
        return mock_context

    @pytest.mark.asyncio
    async def test_search_by_source_missing_user_id(
        self, mock_metrics_manager, mock_trace_context
    ):
        """Test that missing user_id returns error"""
        config = {"configurable": {"organisation_id": "org-456"}}

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                result = await search_by_source.coroutine(
                    query_text="test query",
                    tool_call_id="call-123",
                    config=config
                )

                assert isinstance(result, Command)
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is True
                assert "user_id" in content["error"]

    @pytest.mark.asyncio
    async def test_search_by_source_missing_organisation_id(
        self, mock_metrics_manager, mock_trace_context
    ):
        """Test that missing organisation_id returns error"""
        config = {"configurable": {"user_id": "user-123"}}

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                result = await search_by_source.coroutine(
                    query_text="test query",
                    tool_call_id="call-123",
                    config=config
                )

                assert isinstance(result, Command)
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is True
                assert "organisation_id" in content["error"]

    @pytest.mark.asyncio
    async def test_search_by_source_invalid_source(
        self, mock_metrics_manager, mock_trace_context
    ):
        """Test that invalid kb_source returns error"""
        config = {
            "configurable": {
                "user_id": "user-123",
                "organisation_id": "org-456",
                "kb_source": "INVALID_SOURCE",
            }
        }

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                result = await search_by_source.coroutine(
                    query_text="test query",
                    tool_call_id="call-123",
                    config=config
                )

                assert isinstance(result, Command)
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is True
                assert "Invalid knowledge source" in content["error"]

    @pytest.mark.asyncio
    async def test_search_by_source_success(
        self, mock_config, mock_metrics_manager, mock_trace_context
    ):
        """Test successful knowledge base search"""
        mock_client = MagicMock()
        mock_client.execute_tool = AsyncMock(return_value={"results": []})
        mock_settings = MagicMock()
        mock_settings.kb_mcp = "knowledge-base-mcp"

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                with patch(
                    "app.agent.tools.knowledge_base.MCPClient",
                    return_value=mock_client
                ):
                    with patch(
                        "app.agent.tools.knowledge_base.get_settings",
                        return_value=mock_settings
                    ):
                        with patch(
                            "app.agent.tools.knowledge_base.extract_tool_response",
                            return_value={"results": []}
                        ):
                            with patch(
                                "app.agent.tools.knowledge_base.format_knowledge_base_response",
                                return_value={"results": [], "isError": False}
                            ):
                                with patch(
                                    "app.agent.tools.knowledge_base.extract_knowledge_base_sources",
                                    return_value=[]
                                ):
                                    with patch(
                                        "app.agent.tools.knowledge_base.store_sources",
                                        new_callable=AsyncMock
                                    ):
                                        result = await search_by_source.coroutine(
                                            query_text="test query",
                                            tool_call_id="call-123",
                                            config=mock_config
                                        )

                                        assert isinstance(result, Command)
                                        mock_client.execute_tool.assert_called_once()

    @pytest.mark.asyncio
    async def test_search_by_source_all_source(
        self, mock_metrics_manager, mock_trace_context
    ):
        """Test search with ALL source uses global_search_agent"""
        config = {
            "configurable": {
                "user_id": "user-123",
                "organisation_id": "org-456",
                "conversation_id": "conv-789",
                "kb_source": "ALL",
                "timezone": "UTC",
            }
        }
        mock_client = MagicMock()
        mock_client.execute_tool = AsyncMock(return_value={"results": []})
        mock_settings = MagicMock()
        mock_settings.kb_mcp = "knowledge-base-mcp"

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                with patch(
                    "app.agent.tools.knowledge_base.MCPClient",
                    return_value=mock_client
                ):
                    with patch(
                        "app.agent.tools.knowledge_base.get_settings",
                        return_value=mock_settings
                    ):
                        with patch(
                            "app.agent.tools.knowledge_base.extract_tool_response",
                            return_value={"results": []}
                        ):
                            with patch(
                                "app.agent.tools.knowledge_base.parse_global_search_response",
                                return_value={"results": [], "isError": False}
                            ):
                                with patch(
                                    "app.agent.tools.knowledge_base.extract_global_search_sources",
                                    return_value=[]
                                ):
                                    with patch(
                                        "app.agent.tools.knowledge_base.store_sources",
                                        new_callable=AsyncMock
                                    ):
                                        result = await search_by_source.coroutine(
                                            query_text="test query",
                                            tool_call_id="call-123",
                                            config=config
                                        )

                                        assert isinstance(result, Command)
                                        # Verify global_search_agent was called
                                        call_args = mock_client.execute_tool.call_args
                                        assert call_args.kwargs["tool_name"] == "global_search_agent"

    @pytest.mark.asyncio
    async def test_search_by_source_exception_handling(
        self, mock_config, mock_metrics_manager, mock_trace_context
    ):
        """Test that exceptions are handled properly"""
        mock_exception_tracker = MagicMock()
        mock_settings = MagicMock()
        mock_settings.kb_mcp = "knowledge-base-mcp"

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                with patch(
                    "app.agent.tools.knowledge_base.MCPClient",
                    side_effect=Exception("Connection failed")
                ):
                    with patch(
                        "app.agent.tools.knowledge_base.get_settings",
                        return_value=mock_settings
                    ):
                        with patch(
                            "app.agent.tools.knowledge_base.exception_tracker",
                            mock_exception_tracker
                        ):
                            result = await search_by_source.coroutine(
                                query_text="test query",
                                tool_call_id="call-123",
                                config=mock_config
                            )

                            assert isinstance(result, Command)
                            message = result.update["messages"][0]
                            content = json.loads(message.content)
                            assert content["isError"] is True
                            assert "Connection failed" in content["error"]
                            mock_exception_tracker.track_exception.assert_called_once()


class TestResolveIdentity:
    """Tests for resolve_identity tool"""

    @pytest.fixture
    def mock_config(self):
        """Create mock config with required fields"""
        return {
            "configurable": {
                "user_id": "user-123",
                "organisation_id": "org-456",
                "conversation_id": "conv-789",
            }
        }

    @pytest.fixture
    def mock_metrics_manager(self):
        """Create mock metrics manager"""
        manager = MagicMock()
        manager.record_tool_execution = MagicMock()
        manager.record_tool_error = MagicMock()
        return manager

    @pytest.fixture
    def mock_trace_context(self):
        """Create mock trace context"""
        mock_span = MagicMock()
        mock_span.set_attribute = MagicMock()
        mock_span.record_exception = MagicMock()
        mock_context = MagicMock()
        mock_context.__enter__ = MagicMock(return_value=mock_span)
        mock_context.__exit__ = MagicMock(return_value=None)
        return mock_context

    @pytest.mark.asyncio
    async def test_resolve_identity_missing_user_id(
        self, mock_metrics_manager, mock_trace_context
    ):
        """Test that missing user_id returns error"""
        config = {"configurable": {"organisation_id": "org-456"}}

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                result = await resolve_identity.coroutine(
                    name="John Doe",
                    tool_call_id="call-123",
                    config=config
                )

                assert isinstance(result, Command)
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is True
                assert "user_id" in content["error"]

    @pytest.mark.asyncio
    async def test_resolve_identity_missing_organisation_id(
        self, mock_metrics_manager, mock_trace_context
    ):
        """Test that missing organisation_id returns error"""
        config = {"configurable": {"user_id": "user-123"}}

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                result = await resolve_identity.coroutine(
                    name="John Doe",
                    tool_call_id="call-123",
                    config=config
                )

                assert isinstance(result, Command)
                message = result.update["messages"][0]
                content = json.loads(message.content)
                assert content["isError"] is True
                assert "organisation_id" in content["error"]

    @pytest.mark.asyncio
    async def test_resolve_identity_no_results(
        self, mock_config, mock_metrics_manager, mock_trace_context
    ):
        """Test resolve_identity with no matching results"""
        mock_client = MagicMock()
        mock_client.execute_tool = AsyncMock(return_value={"results": []})
        mock_settings = MagicMock()
        mock_settings.kb_mcp = "knowledge-base-mcp"

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                with patch(
                    "app.agent.tools.knowledge_base.MCPClient",
                    return_value=mock_client
                ):
                    with patch(
                        "app.agent.tools.knowledge_base.get_settings",
                        return_value=mock_settings
                    ):
                        with patch(
                            "app.agent.tools.knowledge_base.extract_tool_response",
                            return_value={"results": []}
                        ):
                            with patch(
                                "app.agent.tools.knowledge_base.format_identity_response",
                                return_value=(0, [])
                            ):
                                result = await resolve_identity.coroutine(
                                    name="Unknown Person",
                                    tool_call_id="call-123",
                                    config=mock_config
                                )

                                assert isinstance(result, Command)
                                message = result.update["messages"][0]
                                content = json.loads(message.content)
                                assert content["isError"] is False
                                assert content["result"] == []
                                assert "No member found" in content["message"]

    @pytest.mark.asyncio
    async def test_resolve_identity_single_result(
        self, mock_config, mock_metrics_manager, mock_trace_context
    ):
        """Test resolve_identity with single matching result"""
        mock_client = MagicMock()
        mock_client.execute_tool = AsyncMock(return_value={"results": []})
        mock_settings = MagicMock()
        mock_settings.kb_mcp = "knowledge-base-mcp"
        single_result = {"name": "John Doe", "email": "<EMAIL>"}

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                with patch(
                    "app.agent.tools.knowledge_base.MCPClient",
                    return_value=mock_client
                ):
                    with patch(
                        "app.agent.tools.knowledge_base.get_settings",
                        return_value=mock_settings
                    ):
                        with patch(
                            "app.agent.tools.knowledge_base.extract_tool_response",
                            return_value={"results": [single_result]}
                        ):
                            with patch(
                                "app.agent.tools.knowledge_base.format_identity_response",
                                return_value=(1, [single_result])
                            ):
                                result = await resolve_identity.coroutine(
                                    name="John Doe",
                                    tool_call_id="call-123",
                                    config=mock_config
                                )

                                assert isinstance(result, Command)
                                message = result.update["messages"][0]
                                content = json.loads(message.content)
                                assert content["isError"] is False
                                assert content["result"]["name"] == "John Doe"
                                assert "Found member" in content["message"]

    @pytest.mark.asyncio
    async def test_resolve_identity_multiple_results(
        self, mock_config, mock_metrics_manager, mock_trace_context
    ):
        """Test resolve_identity with multiple matching results stores approval data"""
        mock_client = MagicMock()
        mock_client.execute_tool = AsyncMock(return_value={"results": []})
        mock_settings = MagicMock()
        mock_settings.kb_mcp = "knowledge-base-mcp"
        multiple_results = [
            {"name": "John Doe", "email": "<EMAIL>"},
            {"name": "John Smith", "email": "<EMAIL>"},
        ]

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                with patch(
                    "app.agent.tools.knowledge_base.MCPClient",
                    return_value=mock_client
                ):
                    with patch(
                        "app.agent.tools.knowledge_base.get_settings",
                        return_value=mock_settings
                    ):
                        with patch(
                            "app.agent.tools.knowledge_base.extract_tool_response",
                            return_value={"results": multiple_results}
                        ):
                            with patch(
                                "app.agent.tools.knowledge_base.format_identity_response",
                                return_value=(2, multiple_results)
                            ):
                                with patch(
                                    "app.agent.tools.knowledge_base.store_approval_data",
                                    new_callable=AsyncMock
                                ) as mock_store:
                                    result = await resolve_identity.coroutine(
                                        name="John",
                                        tool_call_id="call-123",
                                        config=mock_config
                                    )

                                    assert isinstance(result, Command)
                                    message = result.update["messages"][0]
                                    content = json.loads(message.content)
                                    assert content["isError"] is False
                                    assert len(content["result"]) == 2
                                    assert "Awaiting user approval" in content["message"]
                                    mock_store.assert_called_once()

    @pytest.mark.asyncio
    async def test_resolve_identity_exception_handling(
        self, mock_config, mock_metrics_manager, mock_trace_context
    ):
        """Test that exceptions are handled properly"""
        mock_exception_tracker = MagicMock()
        mock_settings = MagicMock()
        mock_settings.kb_mcp = "knowledge-base-mcp"

        with patch(
            "app.agent.tools.knowledge_base.trace_operation",
            return_value=mock_trace_context
        ):
            with patch(
                "app.agent.tools.knowledge_base.get_metrics_manager",
                return_value=mock_metrics_manager
            ):
                with patch(
                    "app.agent.tools.knowledge_base.MCPClient",
                    side_effect=Exception("Connection failed")
                ):
                    with patch(
                        "app.agent.tools.knowledge_base.get_settings",
                        return_value=mock_settings
                    ):
                        with patch(
                            "app.agent.tools.knowledge_base.exception_tracker",
                            mock_exception_tracker
                        ):
                            result = await resolve_identity.coroutine(
                                name="John Doe",
                                tool_call_id="call-123",
                                config=mock_config
                            )

                            assert isinstance(result, Command)
                            message = result.update["messages"][0]
                            content = json.loads(message.content)
                            assert content["isError"] is True
                            assert "Connection failed" in content["error"]
                            mock_exception_tracker.track_exception.assert_called_once()


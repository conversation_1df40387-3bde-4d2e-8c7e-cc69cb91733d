"""Tests for subagent creation functions."""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock

from app.agent.subagents.knowledge_base_subagent import create_knowledge_base_subagent
from app.agent.subagents.mcp_subagent import create_mcp_subagent
from app.agent.subagents.web_search_subagent import create_web_search_subagent
from app.agent.subagents.workflow_subagent import create_workflow_subagent


class TestCreateKnowledgeBaseSubagent:
    """Tests for create_knowledge_base_subagent function."""

    def test_create_subagent_returns_dict(self):
        """Test that create_knowledge_base_subagent returns a dict."""
        result = create_knowledge_base_subagent(
            user_id="user-123",
            conversation_id="conv-456",
            agent_id="agent-789",
            organisation_id="org-abc",
            timezone="UTC",
            kb_source="All",
            user_name="Test User",
            user_email="<EMAIL>",
        )

        assert isinstance(result, dict)
        assert "name" in result
        assert "description" in result
        assert "system_prompt" in result
        assert "tools" in result
        assert result["name"] == "knowledge_base_agent"

    def test_create_subagent_with_email(self):
        """Test subagent creation with user email."""
        result = create_knowledge_base_subagent(
            user_email="<EMAIL>",
        )

        assert "<EMAIL>" in result["system_prompt"]

    def test_create_subagent_with_kb_source(self):
        """Test subagent creation with specific kb_source."""
        result = create_knowledge_base_subagent(
            kb_source="Gmail",
        )

        assert "Gmail" in result["system_prompt"]


class TestCreateMCPSubagent:
    """Tests for create_mcp_subagent function."""

    def test_create_subagent_returns_dict(self):
        """Test that create_mcp_subagent returns a dict."""
        result = create_mcp_subagent(
            user_id="user-123",
            conversation_id="conv-456",
            agent_id="agent-789",
            organisation_id="org-abc",
            timezone="UTC",
        )

        assert isinstance(result, dict)
        assert "name" in result
        assert "description" in result
        assert "system_prompt" in result
        assert "tools" in result
        assert result["name"] == "mcp_subagent"

    def test_create_subagent_with_mcps_data(self):
        """Test subagent creation with MCPs data."""
        mcps_data = [{"name": "GitHub MCP", "description": "GitHub integration"}]
        result = create_mcp_subagent(
            mcps_data=mcps_data,
            user_id="user-123",
        )

        assert isinstance(result, dict)
        assert "system_prompt" in result


class TestCreateWebSearchSubagent:
    """Tests for create_web_search_subagent function."""

    def test_create_subagent_returns_dict(self):
        """Test that create_web_search_subagent returns a dict."""
        result = create_web_search_subagent(
            user_id="user-123",
            conversation_id="conv-456",
            agent_id="agent-789",
            organisation_id="org-abc",
            timezone="UTC",
        )

        assert isinstance(result, dict)
        assert "name" in result
        assert "description" in result
        assert "system_prompt" in result
        assert "tools" in result
        assert result["name"] == "web_search_agent"

    def test_create_subagent_with_timezone(self):
        """Test subagent creation with timezone."""
        result = create_web_search_subagent(
            timezone="America/New_York",
        )

        assert isinstance(result, dict)


class TestCreateWorkflowSubagent:
    """Tests for create_workflow_subagent function."""

    def test_create_subagent_returns_dict(self):
        """Test that create_workflow_subagent returns a dict."""
        workflows_data = [{"id": "wf-1", "name": "Test Workflow"}]

        result = create_workflow_subagent(
            user_id="user-123",
            conversation_id="conv-456",
            agent_id="agent-789",
            organisation_id="org-abc",
            timezone="UTC",
            workflows_data=workflows_data,
        )

        assert isinstance(result, dict)
        assert "name" in result
        assert "description" in result
        assert "system_prompt" in result
        assert "tools" in result
        assert result["name"] == "workflow_subagent"

    def test_create_subagent_with_workflows(self):
        """Test subagent creation with workflow data."""
        workflows_data = [
            {"id": "wf-1", "name": "Email Workflow"},
            {"id": "wf-2", "name": "Data Workflow"},
        ]

        result = create_workflow_subagent(
            workflows_data=workflows_data,
        )

        assert isinstance(result, dict)
        # Workflow info should be in the prompt
        assert "workflow" in result["system_prompt"].lower()


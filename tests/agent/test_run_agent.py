"""Tests for app/agent/run_agent.py"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from app.agent.run_agent import run_agent_stream, run_agent
from app.shared.config.constants import EventType


class TestRunAgentStream:
    """Tests for run_agent_stream async generator function"""

    @pytest.fixture
    def mock_redis_manager(self):
        """Create mock Redis manager"""
        manager = MagicMock()
        manager.redis_client = AsyncMock()
        return manager

    @pytest.fixture
    def mock_agent(self):
        """Create mock agent with astream_events"""
        agent = MagicMock()
        return agent

    @pytest.fixture
    def default_params(self):
        """Default parameters for run_agent_stream"""
        return {
            "user_message": "Hello, world!",
            "provider": "openai",
            "model": "gpt-4",
            "use_knowledge": False,
            "use_search": False,
            "user_id": "user-123",
            "organisation_id": "org-456",
            "conversation_id": "conv-789",
        }

    @pytest.mark.asyncio
    async def test_run_agent_stream_sets_is_global_false_when_agent_id_provided(
        self, default_params, mock_redis_manager, mock_agent
    ):
        """Test that is_global is set to False when agent_id is provided"""
        params = {**default_params, "agent_id": "agent-123"}

        with patch(
            "app.agent.run_agent.get_redis_manager",
            return_value=mock_redis_manager
        ) as mock_get_redis:
            with patch(
                "app.agent.run_agent.create_deep_agent",
                new_callable=AsyncMock,
                return_value=mock_agent
            ) as mock_create:
                mock_agent.astream_events = AsyncMock(return_value=AsyncMock(__aiter__=lambda _: iter([])))
                
                # Collect first chunk (STREAM_START)
                async for chunk in run_agent_stream(**params):
                    assert chunk["type"] == EventType.STREAM_START.value
                    break

    @pytest.mark.asyncio
    async def test_run_agent_stream_yields_stream_start_first(
        self, default_params, mock_redis_manager, mock_agent
    ):
        """Test that STREAM_START is yielded first"""

        async def mock_astream_events(*args, **kwargs):
            return
            yield  # Make it a generator

        mock_agent.astream_events = mock_astream_events

        with patch(
            "app.agent.run_agent.get_redis_manager",
            new_callable=AsyncMock,
            return_value=mock_redis_manager
        ):
            with patch(
                "app.agent.run_agent.create_deep_agent",
                new_callable=AsyncMock,
                return_value=mock_agent
            ):
                with patch(
                    "app.agent.run_agent.clear_stop_signal",
                    new_callable=AsyncMock
                ):
                    with patch(
                        "app.agent.run_agent.check_stop_signal",
                        new_callable=AsyncMock,
                        return_value=False
                    ):
                        with patch(
                            "app.agent.run_agent.get_sources",
                            new_callable=AsyncMock,
                            return_value=None
                        ):
                            with patch(
                                "app.agent.run_agent.get_approval_data",
                                new_callable=AsyncMock,
                                return_value=None
                            ):
                                with patch(
                                    "app.agent.run_agent.clear_approval_data",
                                    new_callable=AsyncMock
                                ):
                                    with patch(
                                        "app.agent.run_agent.clear_sources",
                                        new_callable=AsyncMock
                                    ):
                                        chunks = []
                                        async for chunk in run_agent_stream(**default_params):
                                            chunks.append(chunk)

                                        assert len(chunks) >= 1
                                        assert chunks[0]["type"] == EventType.STREAM_START.value

    @pytest.mark.asyncio
    async def test_run_agent_stream_handles_stop_signal(
        self, default_params, mock_redis_manager, mock_agent
    ):
        """Test that stop signal properly ends stream with is_stopped flag"""

        async def mock_astream_events(*args, **kwargs):
            yield {"event": "test", "data": {}}

        mock_agent.astream_events = mock_astream_events

        with patch(
            "app.agent.run_agent.get_redis_manager",
            new_callable=AsyncMock,
            return_value=mock_redis_manager
        ):
            with patch(
                "app.agent.run_agent.create_deep_agent",
                new_callable=AsyncMock,
                return_value=mock_agent
            ):
                with patch(
                    "app.agent.run_agent.clear_stop_signal",
                    new_callable=AsyncMock
                ):
                    # Return True for stop signal
                    with patch(
                        "app.agent.run_agent.check_stop_signal",
                        new_callable=AsyncMock,
                        return_value=True
                    ):
                        with patch(
                            "app.agent.run_agent.clear_sources",
                            new_callable=AsyncMock
                        ):
                            with patch(
                                "app.agent.run_agent.clear_approval_data",
                                new_callable=AsyncMock
                            ):
                                chunks = []
                                async for chunk in run_agent_stream(**default_params):
                                    chunks.append(chunk)

                                # Should have STREAM_START and STREAM_END with is_stopped
                                assert any(c.get("type") == EventType.STREAM_END.value and c.get("is_stopped") for c in chunks)

    @pytest.mark.asyncio
    async def test_run_agent_stream_handles_exception(
        self, default_params, mock_redis_manager
    ):
        """Test that exceptions are handled and returned in stream_end"""
        with patch(
            "app.agent.run_agent.get_redis_manager",
            new_callable=AsyncMock,
            return_value=mock_redis_manager
        ):
            with patch(
                "app.agent.run_agent.clear_stop_signal",
                new_callable=AsyncMock
            ):
                with patch(
                    "app.agent.run_agent.create_deep_agent",
                    new_callable=AsyncMock,
                    side_effect=Exception("Test error")
                ):
                    chunks = []
                    async for chunk in run_agent_stream(**default_params):
                        chunks.append(chunk)

                    # Should have error stream_end
                    error_chunk = next(
                        (c for c in chunks if c.get("type") == EventType.STREAM_END.value and c.get("is_error")),
                        None
                    )
                    assert error_chunk is not None
                    assert error_chunk.get("error") == "Test error"

    @pytest.mark.asyncio
    async def test_run_agent_stream_with_approval_data(
        self, default_params, mock_redis_manager, mock_agent
    ):
        """Test that approval data is included in stream_end"""

        async def mock_astream_events(*args, **kwargs):
            return
            yield

        mock_agent.astream_events = mock_astream_events

        approval_data = {
            "requires_approval": True,
            "approval_type": "mcp_search",
            "approval_data": {"tools": []}
        }

        with patch(
            "app.agent.run_agent.get_redis_manager",
            new_callable=AsyncMock,
            return_value=mock_redis_manager
        ):
            with patch(
                "app.agent.run_agent.create_deep_agent",
                new_callable=AsyncMock,
                return_value=mock_agent
            ):
                with patch(
                    "app.agent.run_agent.clear_stop_signal",
                    new_callable=AsyncMock
                ):
                    with patch(
                        "app.agent.run_agent.check_stop_signal",
                        new_callable=AsyncMock,
                        return_value=False
                    ):
                        with patch(
                            "app.agent.run_agent.get_sources",
                            new_callable=AsyncMock,
                            return_value=None
                        ):
                            with patch(
                                "app.agent.run_agent.get_approval_data",
                                new_callable=AsyncMock,
                                return_value=approval_data
                            ):
                                with patch(
                                    "app.agent.run_agent.clear_approval_data",
                                    new_callable=AsyncMock
                                ):
                                    with patch(
                                        "app.agent.run_agent.clear_sources",
                                        new_callable=AsyncMock
                                    ):
                                        chunks = []
                                        async for chunk in run_agent_stream(**default_params):
                                            chunks.append(chunk)

                                        end_chunk = next(
                                            (c for c in chunks if c.get("type") == EventType.STREAM_END.value),
                                            None
                                        )
                                        assert end_chunk is not None
                                        assert end_chunk.get("requires_approval") is True
                                        assert end_chunk.get("approval_type") == "mcp_search"


class TestRunAgent:
    """Tests for run_agent async function"""

    @pytest.fixture
    def mock_agent(self):
        """Create mock agent"""
        agent = MagicMock()
        return agent

    @pytest.fixture
    def default_params(self):
        """Default parameters for run_agent"""
        return {
            "user_message": "Hello!",
            "provider": "openai",
            "model": "gpt-4",
            "use_knowledge": False,
            "use_search": False,
            "user_id": "user-123",
            "organisation_id": "org-456",
            "conversation_id": "conv-789",
        }

    @pytest.mark.asyncio
    async def test_run_agent_sets_is_global_false_when_agent_id_provided(
        self, default_params, mock_agent
    ):
        """Test that is_global is set to False when agent_id is provided"""
        params = {**default_params, "agent_id": "agent-123"}

        async def mock_astream_events(*args, **kwargs):
            yield {"messages": [{"content": "Hello"}]}

        mock_agent.astream_events = mock_astream_events

        with patch(
            "app.agent.run_agent.create_deep_agent",
            new_callable=AsyncMock,
            return_value=mock_agent
        ) as mock_create:
            result = await run_agent(**params)
            # Verify create_deep_agent was called with is_global=False
            call_args = mock_create.call_args
            assert call_args is not None

    @pytest.mark.asyncio
    async def test_run_agent_returns_last_chunk(self, default_params, mock_agent):
        """Test that run_agent returns the last chunk from stream"""
        expected_result = {"messages": [{"content": "Final response"}]}

        async def mock_astream_events(*args, **kwargs):
            yield {"messages": [{"content": "First"}]}
            yield {"messages": [{"content": "Second"}]}
            yield expected_result

        mock_agent.astream_events = mock_astream_events

        with patch(
            "app.agent.run_agent.create_deep_agent",
            new_callable=AsyncMock,
            return_value=mock_agent
        ):
            result = await run_agent(**default_params)
            assert result == expected_result

    @pytest.mark.asyncio
    async def test_run_agent_raises_exception_on_error(self, default_params):
        """Test that run_agent raises exceptions properly"""
        with patch(
            "app.agent.run_agent.create_deep_agent",
            new_callable=AsyncMock,
            side_effect=Exception("Agent creation failed")
        ):
            with pytest.raises(Exception) as exc_info:
                await run_agent(**default_params)
            assert "Agent creation failed" in str(exc_info.value)


"""Tests for agent prompt generation."""

import pytest
from app.agent.system_prompts.agent_prompt import get_agent_prompt


class TestGetAgentPrompt:
    """Tests for get_agent_prompt function."""

    def test_basic_prompt_generation(self):
        """Test generating basic prompt with minimal params."""
        result = get_agent_prompt(
            agent_name="Test Agent",
        )
        
        assert "Test Agent" in result
        assert "intro-and-description" in result

    def test_prompt_with_all_features_enabled(self):
        """Test prompt with all features enabled."""
        result = get_agent_prompt(
            use_knowledge=True,
            use_search=True,
            use_memory=True,
            has_mcp_tools=True,
            has_workflows=True,
            agent_name="Full Agent",
            agent_description="A fully featured agent",
            system_message="Custom instructions here",
            tone="professional",
            agent_topic_type="customer_support",
            current_datetime="2025-12-08 14:30 (UTC)",
            user_id="user-123",
            conversation_id="conv-456",
            agent_id="agent-789",
            organisation_id="org-abc",
            timezone="UTC",
            user_name="<PERSON>",
            user_email="<EMAIL>",
        )
        
        assert "Full Agent" in result
        assert "fully featured agent" in result
        assert "Custom instructions here" in result
        assert "professional" in result
        assert "customer_support" in result
        # Memory should be enabled
        assert "memory-disabled-notice" not in result
        # Search should be enabled
        assert "web-search-disabled-notice" not in result
        # Knowledge should be enabled
        assert "knowledge-base-disabled-notice" not in result

    def test_prompt_with_all_features_disabled(self):
        """Test prompt with all features disabled."""
        result = get_agent_prompt(
            use_knowledge=False,
            use_search=False,
            use_memory=False,
            has_mcp_tools=False,
            has_workflows=False,
            agent_name="Basic Agent",
        )
        
        assert "memory-disabled-notice" in result
        assert "web-search-disabled-notice" in result
        assert "knowledge-base-disabled-notice" in result
        assert "disabled-features-information" in result

    def test_prompt_agent_identity_section(self):
        """Test that agent identity section is included."""
        result = get_agent_prompt(
            agent_name="Identity Agent",
            agent_topic_type="sales",
            agent_description="Sales assistant",
            tone="friendly",
        )
        
        assert "agent-identity" in result
        assert "Role" in result
        assert "sales" in result
        assert "Sales assistant" in result
        assert "friendly" in result

    def test_prompt_without_identity_fields(self):
        """Test prompt without identity fields."""
        result = get_agent_prompt(
            agent_name="No Identity Agent",
        )
        
        # Should not have agent-identity section if no identity fields
        # This depends on implementation but agent_name alone doesn't add identity
        assert "No Identity Agent" in result

    def test_prompt_with_system_message(self):
        """Test prompt includes system message in primary instructions."""
        result = get_agent_prompt(
            agent_name="Instructed Agent",
            system_message="Always be helpful and concise.",
        )
        
        assert "primary-instructions" in result
        assert "Always be helpful and concise." in result
        assert "final-reminder" in result

    def test_prompt_without_system_message(self):
        """Test prompt without system message."""
        result = get_agent_prompt(
            agent_name="Simple Agent",
        )
        
        assert "primary-instructions" not in result
        # Should still have final reminder
        assert "final-reminder" in result

    def test_prompt_contains_todo_instructions(self):
        """Test that TODO usage instructions are included."""
        result = get_agent_prompt(agent_name="Todo Agent")
        
        # Check for common TODO instruction elements
        assert "todo" in result.lower() or "TODO" in result

    def test_prompt_contains_mcp_instructions(self):
        """Test that MCP usage instructions are included."""
        result = get_agent_prompt(agent_name="MCP Agent")
        
        # MCP instructions should always be included
        assert "MCP" in result

    def test_prompt_user_context_formatting(self):
        """Test that user context is properly formatted."""
        result = get_agent_prompt(
            agent_name="Context Agent",
            user_id="test-user",
            conversation_id="test-conv",
            agent_id="test-agent",
            organisation_id="test-org",
            timezone="America/New_York",
            user_name="Alice",
            user_email="<EMAIL>",
        )
        
        assert "test-user" in result or "N/A" not in result

    def test_prompt_workflow_enabled(self):
        """Test prompt with workflows enabled."""
        result = get_agent_prompt(
            agent_name="Workflow Agent",
            has_workflows=True,
        )
        
        # Should contain workflow instructions when enabled
        assert "workflows-disabled-notice" not in result.lower() or "in progress" in result.lower()

    def test_prompt_read_file_instructions_included(self):
        """Test that read file instructions are included."""
        result = get_agent_prompt(agent_name="File Agent")
        
        # Read file instructions should be included
        assert len(result) > 1000  # Should be substantial prompt


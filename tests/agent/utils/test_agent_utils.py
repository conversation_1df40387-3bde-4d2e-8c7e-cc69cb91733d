"""
Tests for app/agent/utils/agent_utils.py
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from app.agent.utils.agent_utils import to_snake_case, build_subagent


class TestToSnakeCase:
    """Test suite for to_snake_case function"""

    def test_simple_camel_case(self):
        """Test simple CamelCase conversion"""
        assert to_snake_case("HelloWorld") == "hello_world"
        assert to_snake_case("helloWorld") == "hello_world"

    def test_pascal_case(self):
        """Test PascalCase conversion"""
        assert to_snake_case("SalesAgent") == "sales_agent"
        assert to_snake_case("CampaignManager") == "campaign_manager"

    def test_title_case_with_spaces(self):
        """Test Title Case with spaces"""
        assert to_snake_case("Sales Agent") == "sales_agent"
        assert to_snake_case("Campaign Manager") == "campaign_manager"

    def test_consecutive_capitals(self):
        """Test consecutive capitals like AI, API, SDR"""
        assert to_snake_case("AI SDR Agent") == "ai_sdr_agent"
        assert to_snake_case("APIManager") == "api_manager"
        assert to_snake_case("HTTPSConnection") == "https_connection"

    def test_special_characters(self):
        """Test special characters are removed"""
        assert to_snake_case("Campaign-Manager") == "campaign_manager"
        assert to_snake_case("Sales@Agent") == "sales_agent"
        assert to_snake_case("Test#Agent!") == "test_agent"

    def test_multiple_spaces(self):
        """Test multiple spaces are collapsed"""
        assert to_snake_case("Multiple   Spaces") == "multiple_spaces"
        assert to_snake_case("Too    Many     Spaces") == "too_many_spaces"

    def test_leading_trailing_spaces(self):
        """Test leading and trailing spaces are removed"""
        assert to_snake_case("  Leading") == "leading"
        assert to_snake_case("Trailing  ") == "trailing"
        assert to_snake_case("  Both  ") == "both"

    def test_already_snake_case(self):
        """Test already snake_case strings"""
        assert to_snake_case("already_snake_case") == "already_snake_case"
        assert to_snake_case("test_agent") == "test_agent"

    def test_all_uppercase(self):
        """Test all uppercase strings"""
        assert to_snake_case("AI") == "ai"
        assert to_snake_case("API") == "api"
        assert to_snake_case("HTTP") == "http"

    def test_all_lowercase(self):
        """Test all lowercase strings"""
        assert to_snake_case("lowercase") == "lowercase"
        assert to_snake_case("test") == "test"

    def test_mixed_formats(self):
        """Test mixed format strings"""
        assert to_snake_case("AI-SDR Agent") == "ai_sdr_agent"
        assert to_snake_case("HTTPSConnection Manager") == "https_connection_manager"

    def test_empty_string(self):
        """Test empty string"""
        assert to_snake_case("") == ""

    def test_single_word(self):
        """Test single word"""
        assert to_snake_case("Agent") == "agent"
        assert to_snake_case("test") == "test"

    def test_numbers_in_string(self):
        """Test strings with numbers"""
        assert to_snake_case("Agent123") == "agent123"
        assert to_snake_case("Test 2 Agent") == "test_2_agent"

    def test_consecutive_underscores_removed(self):
        """Test that consecutive underscores are removed"""
        # This happens when special chars are replaced
        assert to_snake_case("Test--Agent") == "test_agent"
        assert to_snake_case("Test___Agent") == "test_agent"

    def test_leading_trailing_underscores_removed(self):
        """Test that leading/trailing underscores are removed"""
        assert to_snake_case("-Test") == "test"
        assert to_snake_case("Test-") == "test"
        assert to_snake_case("-Test-") == "test"

    def test_complex_camel_case(self):
        """Test complex CamelCase patterns"""
        assert to_snake_case("XMLHttpRequest") == "xml_http_request"
        assert to_snake_case("getHTTPResponseCode") == "get_http_response_code"

    def test_unicode_characters(self):
        """Test that unicode characters are handled"""
        # Unicode characters should be preserved if they're word characters
        result = to_snake_case("Test Agent")
        assert result == "test_agent"


class TestBuildSubagent:
    """Test suite for build_subagent function"""

    @patch("app.agent.utils.agent_utils.create_react_agent")
    def test_build_subagent_basic(self, mock_create_react_agent):
        """Test basic subagent creation"""
        # Setup
        mock_model = Mock()
        mock_tools = [Mock(), Mock()]
        prompt = "Test prompt"
        name = "test_agent"
        mock_agent = Mock()
        mock_create_react_agent.return_value = mock_agent

        # Execute
        result = build_subagent(
            model=mock_model,
            tools=mock_tools,
            prompt=prompt,
            name=name
        )

        # Verify
        assert result == mock_agent
        mock_create_react_agent.assert_called_once_with(
            model=mock_model,
            tools=mock_tools,
            prompt=prompt,
            name=name,
            state_schema=None
        )

    @patch("app.agent.utils.agent_utils.create_react_agent")
    def test_build_subagent_with_state_schema(self, mock_create_react_agent):
        """Test subagent creation with state schema"""
        # Setup
        mock_model = Mock()
        mock_tools = [Mock()]
        prompt = "Test prompt"
        name = "test_agent"
        mock_state_schema = Mock()
        mock_agent = Mock()
        mock_create_react_agent.return_value = mock_agent

        # Execute
        result = build_subagent(
            model=mock_model,
            tools=mock_tools,
            prompt=prompt,
            name=name,
            state_schema=mock_state_schema
        )

        # Verify
        assert result == mock_agent
        mock_create_react_agent.assert_called_once_with(
            model=mock_model,
            tools=mock_tools,
            prompt=prompt,
            name=name,
            state_schema=mock_state_schema
        )

    @patch("app.agent.utils.agent_utils.create_react_agent")
    def test_build_subagent_empty_tools(self, mock_create_react_agent):
        """Test subagent creation with empty tools list"""
        # Setup
        mock_model = Mock()
        mock_tools = []
        prompt = "Test prompt"
        name = "test_agent"
        mock_agent = Mock()
        mock_create_react_agent.return_value = mock_agent

        # Execute
        result = build_subagent(
            model=mock_model,
            tools=mock_tools,
            prompt=prompt,
            name=name
        )

        # Verify
        assert result == mock_agent
        mock_create_react_agent.assert_called_once()


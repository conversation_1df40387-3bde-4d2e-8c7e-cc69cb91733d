"""Tests for mcp_utils.py"""
import pytest
from app.agent.utils.mcp_utils import (
    build_mcp_supervisor_prompt,
    build_mcp_subagent_prompt,
)


class TestBuildMcpSupervisorPrompt:
    """Test suite for build_mcp_supervisor_prompt"""

    def test_build_supervisor_prompt_empty_list(self):
        """Test building supervisor prompt with empty MCP list"""
        mcps_data = []
        
        result = build_mcp_supervisor_prompt(mcps_data)
        
        assert "## MCP TOOLS" in result
        assert "These are the MCP tools available" in result

    def test_build_supervisor_prompt_single_mcp(self):
        """Test building supervisor prompt with single MCP"""
        mcps_data = [
            {
                "name": "GitHub MCP",
                "description": "Access GitHub repositories",
                "name_slug": "github-mcp",
                "mcp_tools_config": {
                    "tools": [
                        {"name": "search_repos"},
                        {"name": "get_issues"}
                    ]
                }
            }
        ]
        
        result = build_mcp_supervisor_prompt(mcps_data)
        
        assert "GitHub MCP" in result
        assert "github-mcp" in result
        assert "Access GitHub repositories" in result
        assert "search_repos" in result
        assert "get_issues" in result

    def test_build_supervisor_prompt_multiple_mcps(self):
        """Test building supervisor prompt with multiple MCPs"""
        mcps_data = [
            {
                "name": "GitHub MCP",
                "description": "Access GitHub",
                "name_slug": "github",
                "mcp_tools_config": {"tools": [{"name": "search"}]}
            },
            {
                "name": "Slack MCP",
                "description": "Access Slack",
                "name_slug": "slack",
                "mcp_tools_config": {"tools": [{"name": "send_message"}]}
            }
        ]
        
        result = build_mcp_supervisor_prompt(mcps_data)
        
        assert "GitHub MCP" in result
        assert "Slack MCP" in result
        assert "github" in result
        assert "slack" in result

    def test_build_supervisor_prompt_no_tools(self):
        """Test building supervisor prompt with MCP that has no tools"""
        mcps_data = [
            {
                "name": "Empty MCP",
                "description": "No tools",
                "name_slug": "empty",
                "mcp_tools_config": {"tools": []}
            }
        ]
        
        result = build_mcp_supervisor_prompt(mcps_data)
        
        assert "Empty MCP" in result
        assert "empty" in result

    def test_build_supervisor_prompt_missing_fields(self):
        """Test building supervisor prompt with missing fields"""
        mcps_data = [
            {
                "name": "Partial MCP",
                # Missing description, name_slug, mcp_tools_config
            }
        ]
        
        result = build_mcp_supervisor_prompt(mcps_data)
        
        assert "Partial MCP" in result


class TestBuildMcpSubagentPrompt:
    """Test suite for build_mcp_subagent_prompt"""

    def test_build_subagent_prompt_empty_list(self):
        """Test building subagent prompt with empty MCP list"""
        mcps_data = []

        result = build_mcp_subagent_prompt(mcps_data)

        assert "Your task is to execute MCP tools" in result
        assert "execute_mcp" in result
        assert "mcp_name_slug" in result

    def test_build_subagent_prompt_single_mcp_with_tools(self):
        """Test building subagent prompt with single MCP and tools"""
        mcps_data = [
            {
                "id": "mcp-123",
                "name": "GitHub MCP",
                "logo": "https://example.com/logo.png",
                "description": "Access GitHub repositories",
                "name_slug": "github-mcp",
                "mcp_tools_config": {
                    "tools": [
                        {
                            "name": "search_repos",
                            "description": "Search for repositories",
                            "input_schema": {
                                "properties": {
                                    "query": {
                                        "type": "string",
                                        "title": "Search query"
                                    },
                                    "limit": {
                                        "type": "integer",
                                        "title": "Result limit",
                                        "default": 10
                                    }
                                },
                                "required": ["query"]
                            }
                        }
                    ]
                }
            }
        ]
        
        result = build_mcp_subagent_prompt(mcps_data)
        
        assert "GitHub MCP" in result
        assert "mcp-123" in result
        assert "github-mcp" in result
        assert "https://example.com/logo.png" in result
        assert "search_repos" in result
        assert "Search for repositories" in result
        assert "query" in result
        assert "[REQUIRED]" in result
        assert "limit" in result
        assert "default: 10" in result

    def test_build_subagent_prompt_no_logo(self):
        """Test building subagent prompt with MCP without logo"""
        mcps_data = [
            {
                "id": "mcp-456",
                "name": "Test MCP",
                "logo": None,
                "description": "Test description",
                "name_slug": "test-mcp",
                "mcp_tools_config": {"tools": []}
            }
        ]
        
        result = build_mcp_subagent_prompt(mcps_data)
        
        assert "Test MCP" in result
        assert "None" in result  # Logo should show as None

    def test_build_subagent_prompt_tool_without_parameters(self):
        """Test building subagent prompt with tool that has no parameters"""
        mcps_data = [
            {
                "id": "mcp-789",
                "name": "Simple MCP",
                "description": "Simple MCP",
                "name_slug": "simple",
                "mcp_tools_config": {
                    "tools": [
                        {
                            "name": "simple_tool",
                            "description": "A simple tool",
                            "input_schema": {
                                "properties": {},
                                "required": []
                            }
                        }
                    ]
                }
            }
        ]
        
        result = build_mcp_subagent_prompt(mcps_data)
        
        assert "simple_tool" in result
        assert "A simple tool" in result

    def test_build_subagent_prompt_multiple_mcps_multiple_tools(self):
        """Test building subagent prompt with multiple MCPs and tools"""
        mcps_data = [
            {
                "id": "mcp-1",
                "name": "MCP One",
                "description": "First MCP",
                "name_slug": "mcp-one",
                "mcp_tools_config": {
                    "tools": [
                        {
                            "name": "tool1",
                            "description": "Tool 1",
                            "input_schema": {"properties": {}, "required": []}
                        },
                        {
                            "name": "tool2",
                            "description": "Tool 2",
                            "input_schema": {"properties": {}, "required": []}
                        }
                    ]
                }
            },
            {
                "id": "mcp-2",
                "name": "MCP Two",
                "description": "Second MCP",
                "name_slug": "mcp-two",
                "mcp_tools_config": {
                    "tools": [
                        {
                            "name": "tool3",
                            "description": "Tool 3",
                            "input_schema": {"properties": {}, "required": []}
                        }
                    ]
                }
            }
        ]
        
        result = build_mcp_subagent_prompt(mcps_data)
        
        assert "MCP One" in result
        assert "MCP Two" in result
        assert "tool1" in result
        assert "tool2" in result
        assert "tool3" in result


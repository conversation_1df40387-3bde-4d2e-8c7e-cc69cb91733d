"""
Tests for app/agent/utils/message_utils.py
"""
import pytest
from app.agent.utils.message_utils import build_user_message


class TestBuildUserMessage:
    """Test suite for build_user_message function"""

    def test_no_attachments(self):
        """Test message with no attachments returns plain string"""
        # Setup
        user_message = "Hello, how are you?"

        # Execute
        result = build_user_message(user_message)

        # Verify
        assert result == "Hello, how are you?"
        assert isinstance(result, str)

    def test_none_attachments(self):
        """Test message with None attachments returns plain string"""
        # Setup
        user_message = "Test message"

        # Execute
        result = build_user_message(user_message, attachments=None)

        # Verify
        assert result == "Test message"
        assert isinstance(result, str)

    def test_empty_attachments_list(self):
        """Test message with empty attachments list returns plain string"""
        # Setup
        user_message = "Test message"

        # Execute
        result = build_user_message(user_message, attachments=[])

        # Verify
        assert result == "Test message"
        assert isinstance(result, str)

    def test_single_image_attachment(self):
        """Test message with single image attachment"""
        # Setup
        user_message = "Check this image"
        attachments = [
            {
                "file_type": "image/png",
                "file_name": "test.png",
                "file_url": "https://example.com/test.png"
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0] == {"type": "text", "text": "Check this image"}
        assert result[1] == {
            "type": "image_url",
            "image_url": {"url": "https://example.com/test.png"}
        }

    def test_multiple_image_attachments(self):
        """Test message with multiple image attachments"""
        # Setup
        user_message = "Check these images"
        attachments = [
            {
                "file_type": "image/png",
                "file_name": "test1.png",
                "file_url": "https://example.com/test1.png"
            },
            {
                "file_type": "image/jpeg",
                "file_name": "test2.jpg",
                "file_url": "https://example.com/test2.jpg"
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify
        assert isinstance(result, list)
        assert len(result) == 3
        assert result[0]["type"] == "text"
        assert result[1]["type"] == "image_url"
        assert result[2]["type"] == "image_url"

    def test_single_pdf_attachment(self):
        """Test message with single PDF attachment"""
        # Setup
        user_message = "Review this document"
        attachments = [
            {
                "file_type": "application/pdf",
                "file_name": "report.pdf",
                "file_url": "https://example.com/report.pdf"
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify
        assert isinstance(result, str)
        assert "Review this document" in result
        assert "📎 Attached documents for reference:" in result
        assert "1. report.pdf" in result
        assert "URL: https://example.com/report.pdf" in result
        assert "Type: PDF" in result
        assert "Use the 'read_file' tool" in result

    def test_multiple_document_attachments(self):
        """Test message with multiple document attachments"""
        # Setup
        user_message = "Review these documents"
        attachments = [
            {
                "file_type": "application/pdf",
                "file_name": "report.pdf",
                "file_url": "https://example.com/report.pdf"
            },
            {
                "file_type": "text/csv",
                "file_name": "data.csv",
                "file_url": "https://example.com/data.csv"
            },
            {
                "file_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                "file_name": "notes.docx",
                "file_url": "https://example.com/notes.docx"
            },
            {
                "file_type": "text/plain",
                "file_name": "readme.txt",
                "file_url": "https://example.com/readme.txt"
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify
        assert isinstance(result, str)
        assert "1. report.pdf" in result
        assert "2. data.csv" in result
        assert "3. notes.docx" in result
        assert "4. readme.txt" in result
        assert "Type: PDF" in result
        assert "Type: CSV" in result
        assert "Type: DOCX" in result
        assert "Type: TXT" in result

    def test_mixed_image_and_document_attachments(self):
        """Test message with both image and document attachments"""
        # Setup
        user_message = "Check these files"
        attachments = [
            {
                "file_type": "image/png",
                "file_name": "chart.png",
                "file_url": "https://example.com/chart.png"
            },
            {
                "file_type": "application/pdf",
                "file_name": "report.pdf",
                "file_url": "https://example.com/report.pdf"
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify
        assert isinstance(result, list)
        assert len(result) == 2
        assert result[0]["type"] == "text"
        assert "Check these files" in result[0]["text"]
        assert "📎 Attached documents for reference:" in result[0]["text"]
        assert "1. report.pdf" in result[0]["text"]
        assert result[1]["type"] == "image_url"

    def test_image_without_url(self):
        """Test image attachment without URL is skipped"""
        # Setup
        user_message = "Test message"
        attachments = [
            {
                "file_type": "image/png",
                "file_name": "test.png",
                "file_url": ""  # Empty URL
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify - should return plain string since image has no URL
        assert result == "Test message"

    def test_unsupported_file_type(self):
        """Test unsupported file type is ignored"""
        # Setup
        user_message = "Test message"
        attachments = [
            {
                "file_type": "application/zip",
                "file_name": "archive.zip",
                "file_url": "https://example.com/archive.zip"
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify - should return plain string since zip is not supported
        assert result == "Test message"

    def test_attachment_missing_fields(self):
        """Test attachment with missing fields"""
        # Setup
        user_message = "Test message"
        attachments = [
            {}  # Empty attachment dict
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify
        assert result == "Test message"

    def test_case_insensitive_extension_matching(self):
        """Test that extension matching is case-insensitive"""
        # Setup
        user_message = "Test"
        attachments = [
            {
                "file_type": "application/pdf",
                "file_name": "report.PDF",  # Uppercase extension
                "file_url": "https://example.com/report.PDF"
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify
        assert isinstance(result, str)
        assert "1. report.PDF" in result
        assert "Type: PDF" in result

    def test_document_numbering(self):
        """Test that documents are numbered correctly"""
        # Setup
        user_message = "Test"
        attachments = [
            {
                "file_type": "application/pdf",
                "file_name": "first.pdf",
                "file_url": "https://example.com/first.pdf"
            },
            {
                "file_type": "text/csv",
                "file_name": "second.csv",
                "file_url": "https://example.com/second.csv"
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify
        assert "1. first.pdf" in result
        assert "2. second.csv" in result

    def test_only_images_returns_list(self):
        """Test that only images returns a list, not a string"""
        # Setup
        user_message = "Test"
        attachments = [
            {
                "file_type": "image/png",
                "file_name": "test.png",
                "file_url": "https://example.com/test.png"
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify
        assert isinstance(result, list)
        assert len(result) == 2

    def test_only_documents_returns_string(self):
        """Test that only documents returns a string"""
        # Setup
        user_message = "Test"
        attachments = [
            {
                "file_type": "application/pdf",
                "file_name": "test.pdf",
                "file_url": "https://example.com/test.pdf"
            }
        ]

        # Execute
        result = build_user_message(user_message, attachments)

        # Verify
        assert isinstance(result, str)
        assert "Test" in result
        assert "1. test.pdf" in result


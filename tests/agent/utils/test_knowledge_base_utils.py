"""Tests for knowledge_base_utils.py"""
import pytest
import json
from unittest.mock import Mock
from app.agent.utils.knowledge_base_utils import (
    extract_tool_response,
    format_identity_response,
    format_knowledge_base_response,
    extract_knowledge_base_sources,
    parse_global_search_response,
    extract_global_search_sources,
)


class TestExtractToolResponse:
    """Test suite for extract_tool_response"""

    def test_extract_dict_response(self):
        """Test extracting response when already a dict"""
        result = {"key": "value", "isError": False}
        extracted = extract_tool_response(result)
        assert extracted == result

    def test_extract_from_call_tool_result(self):
        """Test extracting from CallToolResult object"""
        # Mock CallToolResult with content
        mock_content_item = Mock()
        mock_content_item.text = '{"success": true, "data": "test"}'
        
        mock_result = Mock()
        mock_result.content = [mock_content_item]
        
        extracted = extract_tool_response(mock_result)
        assert extracted == {"success": True, "data": "test"}

    def test_extract_invalid_json_in_content(self):
        """Test handling invalid JSON in content"""
        mock_content_item = Mock()
        mock_content_item.text = "not valid json"
        
        mock_result = Mock()
        mock_result.content = [mock_content_item]
        
        extracted = extract_tool_response(mock_result)
        assert extracted["isError"] is True
        assert "Invalid JSON response" in extracted["error"]

    def test_extract_fallback_string_parsing(self):
        """Test fallback to string parsing"""
        result = '{"fallback": true}'
        extracted = extract_tool_response(result)
        assert extracted == {"fallback": True}

    def test_extract_failure_returns_error(self):
        """Test that extraction failure returns error dict"""
        result = Mock()
        result.content = []
        result.__str__ = Mock(return_value="unparseable")
        
        extracted = extract_tool_response(result)
        assert extracted["isError"] is True
        assert "Could not extract response" in extracted["error"]


class TestFormatIdentityResponse:
    """Test suite for format_identity_response"""

    def test_format_identity_with_content_wrapper(self):
        """Test formatting identity response with content wrapper"""
        response = {
            "content": [{
                "text": json.dumps({
                    "success": True,
                    "total_results": 2,
                    "results": [
                        {"name": "John Doe", "email": "<EMAIL>", "org_department": "Engineering"},
                        {"name": "Jane Smith", "email": "<EMAIL>", "org_role": "Manager"}
                    ]
                })
            }]
        }
        
        total, results = format_identity_response(response, "John")
        assert total == 2
        assert len(results) == 2
        assert results[0]["name"] == "John Doe"
        assert results[0]["email"] == "<EMAIL>"

    def test_format_identity_direct_dict(self):
        """Test formatting identity response as direct dict"""
        response = {
            "total_results": 1,
            "results": [{"name": "Test User", "email": "<EMAIL>"}]
        }
        
        total, results = format_identity_response(response, "Test")
        assert total == 1
        assert len(results) == 1

    def test_format_identity_empty_results(self):
        """Test formatting with no results"""
        response = {"total_results": 0, "results": []}
        
        total, results = format_identity_response(response, "Nobody")
        assert total == 0
        assert len(results) == 0

    def test_format_identity_with_optional_fields(self):
        """Test formatting with optional fields"""
        response = {
            "total_results": 1,
            "results": [{
                "name": "Manager User",
                "email": "<EMAIL>",
                "org_department": "Sales",
                "org_role": "Director",
                "manager": "CEO"
            }]
        }
        
        total, results = format_identity_response(response, "Manager")
        assert total == 1
        assert results[0]["org_department"] == "Sales"
        assert results[0]["org_role"] == "Director"
        assert results[0]["manager"] == "CEO"

    def test_format_identity_malformed_response(self):
        """Test handling malformed response"""
        response = {"invalid": "structure"}
        
        total, results = format_identity_response(response, "Test")
        assert total == 0
        assert results == []


class TestFormatKnowledgeBaseResponse:
    """Test suite for format_knowledge_base_response"""

    def test_format_kb_unstructured_response(self):
        """Test formatting UNSTRUCTURED KB response"""
        response = {
            "success": True,
            "connector_type": "UNSTRUCTURED",
            "source_used": "GOOGLE_DRIVE",
            "result": {
                "results": [{"content": "Result 1", "score": 0.95}],
                "total_results": 1,
                "graph_context": "context"
            },
            "search_time_ms": 100
        }

        formatted = format_knowledge_base_response(response, "test query")
        assert formatted["connector_type"] == "UNSTRUCTURED"
        assert formatted["isError"] is False
        assert len(formatted["results"]) == 1

    def test_format_kb_structured_response(self):
        """Test formatting STRUCTURED KB response"""
        response = {
            "success": True,
            "connector_type": "STRUCTURED",
            "source_used": "JIRA",
            "result": {
                "neo4j_results": [{"issue": "TEST-1"}],
                "results_count": 1,
                "execution_time_ms": 50,
                "cypher_query": "MATCH (n) RETURN n"
            },
            "search_time_ms": 100
        }

        formatted = format_knowledge_base_response(response, "test query")
        assert formatted["connector_type"] == "STRUCTURED"
        assert formatted["isError"] is False
        assert "cypher_query" in formatted

    def test_format_kb_hybrid_response(self):
        """Test formatting HYBRID KB response"""
        response = {
            "success": True,
            "connector_type": "HYBRID",
            "source_used": "MIXED",
            "result": {
                "neo4j_results": [{"structured": "data"}],
                "results": [{"unstructured": "data"}],
                "graph_context": "context",
                "results_count": 1,
                "total_results": 1
            }
        }

        formatted = format_knowledge_base_response(response, "test query")
        assert formatted["connector_type"] == "HYBRID"
        assert "structured_results" in formatted
        assert "unstructured_results" in formatted

    def test_format_kb_error_response(self):
        """Test formatting error response"""
        response = {
            "success": False,
            "message": "Search failed",
            "source_used": "GOOGLE_DRIVE"
        }

        formatted = format_knowledge_base_response(response, "test query")
        assert formatted["isError"] is True
        assert "error" in formatted

    def test_format_kb_no_result(self):
        """Test formatting response with no result"""
        response = {
            "success": True,
            "connector_type": "UNSTRUCTURED",
            "source_used": "GOOGLE_DRIVE",
            "result": None
        }

        formatted = format_knowledge_base_response(response, "test query")
        assert formatted["isError"] is True

    def test_format_kb_unknown_connector(self):
        """Test formatting with unknown connector type"""
        response = {
            "success": True,
            "connector_type": "UNKNOWN",
            "source_used": "CUSTOM",
            "result": {"data": "test"}
        }

        formatted = format_knowledge_base_response(response, "test query")
        assert formatted["connector_type"] == "UNKNOWN"
        assert formatted["isError"] is False


class TestExtractKnowledgeBaseSources:
    """Test suite for extract_knowledge_base_sources"""

    def test_extract_sources_unstructured(self):
        """Test extracting sources from UNSTRUCTURED response"""
        response = {
            "connector_type": "UNSTRUCTURED",
            "source": "GOOGLE_DRIVE",
            "results": [
                {"file_name": "doc1.pdf", "web_view_link": "http://example.com/doc1", "score": 0.95},
                {"file_name": "doc2.pdf", "web_view_link": "http://example.com/doc2"}
            ],
            "isError": False
        }

        sources = extract_knowledge_base_sources(response)
        assert len(sources) == 2
        assert sources[0]["title"] == "doc1.pdf"
        assert sources[0]["score"] == 0.95

    def test_extract_sources_structured(self):
        """Test extracting sources from STRUCTURED response"""
        response = {
            "connector_type": "STRUCTURED",
            "source": "JIRA",
            "results": [
                {"summary": "Issue 1", "url": "http://jira.com/issue1"}
            ],
            "isError": False
        }

        sources = extract_knowledge_base_sources(response)
        assert len(sources) == 1
        assert sources[0]["title"] == "Issue 1"

    def test_extract_sources_error_response(self):
        """Test extracting from error response"""
        response = {"isError": True, "error": "Failed"}

        sources = extract_knowledge_base_sources(response)
        assert sources == []

    def test_extract_sources_hybrid(self):
        """Test extracting sources from HYBRID response"""
        response = {
            "connector_type": "HYBRID",
            "source": "MIXED",
            "structured_results": [{"summary": "Structured"}],
            "unstructured_results": [{"file_name": "Unstructured", "web_view_link": "http://example.com"}],
            "isError": False
        }

        sources = extract_knowledge_base_sources(response)
        assert len(sources) >= 0  # Implementation dependent


class TestParseGlobalSearchResponse:
    """Test suite for parse_global_search_response"""

    def test_parse_global_search_success(self):
        """Test parsing successful global search response"""
        response = {
            "content": [{
                "text": json.dumps({
                    "success": True,
                    "results": [
                        {"file_name": "doc1.pdf", "chunk_text": "content1"},
                        {"file_name": "doc2.pdf", "chunk_text": "content2"}
                    ],
                    "total_results": 2,
                    "search_time_ms": 150
                })
            }]
        }

        parsed = parse_global_search_response(response, "test query")
        assert parsed["isError"] is False
        assert parsed["connector_type"] == "GLOBAL_SEARCH"
        assert len(parsed["results"]) == 2
        assert parsed["total_results"] == 2

    def test_parse_global_search_direct_dict(self):
        """Test parsing direct dict response"""
        response = {
            "success": True,
            "results": [{"file_name": "test.pdf"}],
            "total_results": 1
        }

        parsed = parse_global_search_response(response, "test query")
        assert parsed["isError"] is False
        assert len(parsed["results"]) == 1

    def test_parse_global_search_error(self):
        """Test parsing error response"""
        response = {
            "success": False,
            "message": "Search failed"
        }

        parsed = parse_global_search_response(response, "test query")
        assert parsed["isError"] is True
        assert "error" in parsed

    def test_parse_global_search_invalid_json(self):
        """Test parsing invalid JSON"""
        response = {
            "content": [{
                "text": "not valid json"
            }]
        }

        parsed = parse_global_search_response(response, "test query")
        assert parsed["isError"] is True
    
    def test_parse_global_search_direct_format(self):
        """Test parsing direct GLOBAL_SEARCH connector format"""
        response = {
            "query": "corrections suggested during initial PSD review",
            "source": "ALL",
            "connector_type": "GLOBAL_SEARCH",
            "results": [
                {
                    "file_id": "35848193",
                    "file_name": "Confluence Connector PSD",
                    "web_view_link": "https://example.com/page",
                    "chunk_text": "Sample content",
                    "source_type": "CONFLUENCE"
                }
            ],
            "total_results": 4,
            "search_time_ms": 28360.14453125,
            "format_type": "global_search_results",
            "isError": False
        }

        parsed = parse_global_search_response(response, "test query")
        assert parsed["isError"] is False
        assert parsed["connector_type"] == "GLOBAL_SEARCH"
        assert parsed["source"] == "ALL"
        assert len(parsed["results"]) == 1
        assert parsed["total_results"] == 4
        assert parsed["search_time_ms"] == 28360.14453125
        assert parsed["format_type"] == "global_search_results"
    
    def test_parse_global_search_error_with_iserror_flag(self):
        """Test parsing error response with isError flag"""
        response = {
            "query": "test query",
            "source": "ALL",
            "isError": True,
            "error": "Search service unavailable"
        }

        parsed = parse_global_search_response(response, "test query")
        assert parsed["isError"] is True
        assert "error" in parsed
        assert parsed["error"] == "Search service unavailable"


class TestExtractGlobalSearchSources:
    """Test suite for extract_global_search_sources"""

    def test_extract_global_sources_with_results(self):
        """Test extracting sources from global search"""
        response = {
            "results": [
                {
                    "file_name": "doc1.pdf",
                    "web_view_link": "http://example.com/doc1",
                    "source_type": "GOOGLE_DRIVE",
                    "file_id": "123",
                    "score": 0.95,
                    "chunk_text": "content"
                },
                {
                    "file_name": "doc2.pdf",
                    "web_view_link": "http://example.com/doc2",
                    "source_type": "CONFLUENCE",
                    "file_id": "456"
                }
            ],
            "isError": False
        }

        sources = extract_global_search_sources(response)
        assert len(sources) == 2
        assert sources[0]["title"] == "doc1.pdf"
        assert sources[0]["url"] == "http://example.com/doc1"
        assert sources[0]["score"] == 0.95

    def test_extract_global_sources_error(self):
        """Test extracting from error response"""
        response = {"isError": True}

        sources = extract_global_search_sources(response)
        assert sources == []

    def test_extract_global_sources_filters_empty(self):
        """Test that sources without title/url are filtered"""
        response = {
            "results": [
                {"file_name": "", "web_view_link": ""},
                {"file_name": "valid.pdf", "web_view_link": "http://example.com"}
            ],
            "isError": False
        }

        sources = extract_global_search_sources(response)
        assert len(sources) == 1
        assert sources[0]["title"] == "valid.pdf"
    
    def test_extract_global_sources_with_full_metadata(self):
        """Test extracting sources with comprehensive metadata"""
        response = {
            "results": [
                {
                    "file_id": "35848193",
                    "file_name": "Confluence Connector PSD",
                    "mime_type": "text/html",
                    "web_view_link": "https://ruh-ai-team.atlassian.net/wiki/spaces/CT/pages/35848193",
                    "created_time": "2024-01-01T00:00:00Z",
                    "modified_time": "2024-01-15T12:00:00Z",
                    "score": 0.3815489709377289,
                    "vector_id": "f729da7f-7bb8-4b30-a208-f1aae6dca0e7_chunk_18",
                    "chunk_text": "Token Guidance | As an Admin, I want clear guidance...",
                    "search_type": "semantic",
                    "source_type": "CONFLUENCE",
                    "source_id": "f070bc6e-fb7b-4c6d-a26a-dd2ad8ca6483",
                    "metadata": {
                        "labels": "",
                        "version": "1.0",
                        "author": "<EMAIL>",
                        "space_name": "Team Space",
                        "page_id": "35848193"
                    },
                    "comments": [],
                    "comment_count": 0,
                    "latest_comment_date": "",
                    "highlights": ["Token Guidance | As an Admin, I want clear guidance..."],
                    "breadcrumb": "Confluence Connector PSD",
                    "thumbnail_url": "",
                    "preview_text": "Token Guidance preview...",
                    "title": "Confluence Connector PSD",
                    "source_name": "Confluence",
                    "relevance_score": 0.3815489709377289
                }
            ],
            "isError": False
        }

        sources = extract_global_search_sources(response)
        assert len(sources) == 1
        source = sources[0]
        
        # Core fields
        assert source["title"] == "Confluence Connector PSD"
        assert source["url"] == "https://ruh-ai-team.atlassian.net/wiki/spaces/CT/pages/35848193"
        assert source["file_id"] == "35848193"
        assert source["source_type"] == "CONFLUENCE"
        
        # Search metadata
        assert source["score"] == 0.3815489709377289
        assert source["relevance_score"] == 0.3815489709377289
        assert source["search_type"] == "semantic"
        assert source["vector_id"] == "f729da7f-7bb8-4b30-a208-f1aae6dca0e7_chunk_18"
        
        # Content fields
        assert "chunk_text" in source
        assert "preview_text" in source
        assert "breadcrumb" in source
        assert "highlights" in source
        
        # Metadata
        assert "metadata" in source
        assert source["metadata"]["page_id"] == "35848193"


class TestExtractKnowledgeBaseSourcesStructured:
    """Test extract_knowledge_base_sources with STRUCTURED connector type"""

    def test_structured_with_nodes(self):
        """Test STRUCTURED connector with nodes"""
        response = {
            "connector_type": "STRUCTURED",
            "source": "JIRA",
            "results": [
                {
                    "nodes": {
                        "node1": {
                            "properties": {
                                "title": "Test Issue",
                                "url": "http://jira.com/issue/1",
                                "summary": "Summary"
                            }
                        }
                    }
                }
            ],
            "isError": False
        }

        sources = extract_knowledge_base_sources(response)
        assert len(sources) == 1
        assert sources[0]["title"] == "Test Issue"
        assert sources[0]["url"] == "http://jira.com/issue/1"

    def test_structured_without_nodes(self):
        """Test STRUCTURED connector without nodes (direct properties)"""
        response = {
            "connector_type": "STRUCTURED",
            "source": "JIRA",
            "results": [
                {
                    "summary": "Direct Summary",
                    "url": "http://jira.com/issue/2"
                }
            ],
            "isError": False
        }

        sources = extract_knowledge_base_sources(response)
        assert len(sources) == 1
        assert sources[0]["title"] == "Direct Summary"
        assert sources[0]["url"] == "http://jira.com/issue/2"


class TestExtractKnowledgeBaseSourcesHybrid:
    """Test extract_knowledge_base_sources with HYBRID connector type"""

    def test_hybrid_structured_results(self):
        """Test HYBRID connector with structured results"""
        response = {
            "connector_type": "HYBRID",
            "source": "MIXED",
            "structured_results": [
                {
                    "nodes": {
                        "node1": {
                            "properties": {
                                "title": "Structured Item",
                                "url": "http://example.com/structured"
                            }
                        }
                    }
                }
            ],
            "unstructured_results": [
                {
                    "file_name": "Unstructured.pdf",
                    "web_view_link": "http://example.com/unstructured",
                    "score": 0.95
                }
            ],
            "isError": False
        }

        sources = extract_knowledge_base_sources(response)
        assert len(sources) == 2
        assert sources[0]["title"] == "Structured Item"
        assert sources[1]["title"] == "Unstructured.pdf"
        assert sources[1]["score"] == 0.95


class TestExtractKnowledgeBaseSourcesLegacy:
    """Test extract_knowledge_base_sources with legacy format"""

    def test_legacy_google_drive(self):
        """Test legacy format for GOOGLE_DRIVE"""
        response = {
            "source": "GOOGLE_DRIVE",
            "results": [
                {
                    "file_name": "Legacy Doc.pdf",
                    "web_view_link": "http://drive.google.com/file/123",
                    "score": 0.88
                }
            ],
            "isError": False
        }

        sources = extract_knowledge_base_sources(response)
        assert len(sources) == 1
        assert sources[0]["title"] == "Legacy Doc.pdf"
        assert sources[0]["url"] == "http://drive.google.com/file/123"
        assert sources[0]["score"] == 0.88

    def test_legacy_jira_with_nodes(self):
        """Test legacy format for JIRA with nodes"""
        response = {
            "source": "JIRA",
            "results": [
                {
                    "nodes": {
                        "issue1": {
                            "properties": {
                                "summary": "Legacy Issue",
                                "html_url": "http://jira.com/legacy/1"
                            }
                        }
                    }
                }
            ],
            "isError": False
        }

        sources = extract_knowledge_base_sources(response)
        assert len(sources) == 1
        assert sources[0]["title"] == "Legacy Issue"
        assert sources[0]["url"] == "http://jira.com/legacy/1"

    def test_legacy_jira_without_nodes(self):
        """Test legacy format for JIRA without nodes"""
        response = {
            "source": "JIRA",
            "results": [
                {
                    "summary": "Direct Legacy Issue",
                    "self": "http://jira.com/api/issue/1"
                }
            ],
            "isError": False
        }

        sources = extract_knowledge_base_sources(response)
        assert len(sources) == 1
        assert sources[0]["title"] == "Direct Legacy Issue"
        assert sources[0]["url"] == "http://jira.com/api/issue/1"

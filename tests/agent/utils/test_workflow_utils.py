"""Tests for workflow utility functions."""

import pytest
import json
from app.agent.utils.workflow_utils import (
    build_workflow_supervisor_prompt,
    build_workflow_subagent_prompt,
)


class TestBuildWorkflowSupervisorPrompt:
    """Tests for build_workflow_supervisor_prompt function."""

    def test_build_prompt_with_workflows(self):
        """Test building supervisor prompt with workflow data."""
        workflows_data = [
            {
                "name": "Email Workflow",
                "description": "Send automated emails",
                "id": "wf-email-123",
            },
            {
                "name": "Data Processing",
                "description": "Process data files",
                "id": "wf-data-456",
            },
        ]
        
        result = build_workflow_supervisor_prompt(workflows_data)
        
        assert "## WORKFLOWS" in result
        assert "Email Workflow" in result
        assert "wf-email-123" in result
        assert "Send automated emails" in result
        assert "Data Processing" in result
        assert "wf-data-456" in result

    def test_build_prompt_empty_workflows(self):
        """Test building supervisor prompt with empty workflow list."""
        result = build_workflow_supervisor_prompt([])
        
        assert "## WORKFLOWS" in result
        # Should have header but no workflow entries

    def test_build_prompt_missing_fields(self):
        """Test building prompt with missing workflow fields."""
        workflows_data = [
            {"name": "Workflow Only Name"},
        ]
        
        result = build_workflow_supervisor_prompt(workflows_data)
        
        assert "Workflow Only Name" in result


class TestBuildWorkflowSubagentPrompt:
    """Tests for build_workflow_subagent_prompt function."""

    def test_build_subagent_prompt_with_workflows(self):
        """Test building subagent prompt with workflow data."""
        workflows_data = [
            {
                "id": "wf-123",
                "name": "Test Workflow",
                "description": "A test workflow",
                "start_nodes": [
                    {
                        "field": "email",
                        "type": "string",
                        "description": "User email",
                        "required": True,
                        "format": "email",
                    },
                    {
                        "field": "count",
                        "type": "number",
                        "description": "Item count",
                        "required": False,
                    },
                ],
            },
        ]
        
        result = build_workflow_subagent_prompt(workflows_data)
        
        assert "workflow_execution" in result
        assert "Test Workflow" in result
        assert "wf-123" in result
        assert "email" in result
        assert "[REQUIRED]" in result
        assert "number" in result
        assert "Example Payload" in result

    def test_build_subagent_prompt_no_start_nodes(self):
        """Test building subagent prompt without start nodes."""
        workflows_data = [
            {
                "id": "wf-456",
                "name": "Simple Workflow",
                "description": "No inputs needed",
                "start_nodes": [],
            },
        ]
        
        result = build_workflow_subagent_prompt(workflows_data)
        
        assert "No input fields required" in result

    def test_build_subagent_prompt_with_boolean_field(self):
        """Test building subagent prompt with boolean field type."""
        workflows_data = [
            {
                "id": "wf-789",
                "name": "Boolean Workflow",
                "description": "Has boolean",
                "start_nodes": [
                    {
                        "field": "enabled",
                        "type": "boolean",
                        "description": "Enable feature",
                        "required": True,
                    },
                ],
            },
        ]
        
        result = build_workflow_subagent_prompt(workflows_data)
        
        assert "enabled" in result
        assert "boolean" in result

    def test_build_subagent_prompt_empty_workflows(self):
        """Test building subagent prompt with empty workflow list."""
        result = build_workflow_subagent_prompt([])
        
        assert "workflow_execution" in result
        assert "Available Workflows" in result

    def test_build_subagent_prompt_example_payload_format(self):
        """Test that example payload is valid JSON."""
        workflows_data = [
            {
                "id": "wf-test",
                "name": "Test",
                "description": "Test",
                "start_nodes": [
                    {"field": "name", "type": "string"},
                    {"field": "age", "type": "number"},
                    {"field": "active", "type": "boolean"},
                ],
            },
        ]
        
        result = build_workflow_subagent_prompt(workflows_data)
        
        # The prompt should contain JSON that can be parsed
        assert '"name"' in result or "'name'" in result


"""Tests for ChatOpenAI with reasoning configuration."""

import pytest
import openai
from unittest.mock import Magic<PERSON><PERSON>, patch, AsyncMock
from langchain_core.messages import AIMessage, AIMessageChunk
from langchain_core.outputs import ChatResult, ChatGeneration, ChatGenerationChunk

from app.agent.config.chat_openai_with_reasoning import ChatOpenAIWithReasoning


class TestChatOpenAIWithReasoning:
    """Tests for ChatOpenAIWithReasoning class."""

    def test_model_inherits_from_chat_openai(self):
        """Test that ChatOpenAIWithReasoning inherits from ChatOpenAI."""
        from langchain_openai import ChatOpenA<PERSON>

        assert issubclass(ChatOpenAIWithReasoning, ChatOpenAI)

    def test_create_chat_result_with_reasoning_content(self):
        """Test _create_chat_result extracts reasoning_content."""
        # Create a mock response with reasoning_content
        mock_message = MagicMock()
        mock_message.content = "Final answer"
        mock_message.reasoning_content = "Step by step reasoning"
        mock_message.tool_calls = None
        mock_message.function_call = None

        mock_choice = MagicMock()
        mock_choice.message = mock_message
        mock_choice.finish_reason = "stop"
        mock_choice.logprobs = None

        mock_response = MagicMock(spec=openai.BaseModel)
        mock_response.choices = [mock_choice]
        mock_response.model = "gpt-4o"
        mock_response.id = "test-id"
        mock_response.created = 1234567890
        mock_response.usage = MagicMock(
            prompt_tokens=10,
            completion_tokens=20,
            total_tokens=30
        )

        # Create a mock parent result
        mock_ai_message = AIMessage(content="Final answer", additional_kwargs={})
        mock_generation = ChatGeneration(message=mock_ai_message)
        mock_chat_result = ChatResult(generations=[mock_generation])

        with patch("langchain_openai.ChatOpenAI._create_chat_result", return_value=mock_chat_result):
            with patch("langchain_openai.ChatOpenAI.__init__", return_value=None):
                model = ChatOpenAIWithReasoning()
                result = model._create_chat_result(mock_response)

                # Check that reasoning_content was extracted
                assert "reasoning_content" in result.generations[0].message.additional_kwargs
                assert result.generations[0].message.additional_kwargs["reasoning_content"] == "Step by step reasoning"

    def test_create_chat_result_with_model_extra_reasoning(self):
        """Test _create_chat_result extracts reasoning from model_extra."""
        # Create a mock response with reasoning in model_extra
        mock_message = MagicMock()
        mock_message.content = "Final answer"
        mock_message.model_extra = {"reasoning": "Reasoning from model_extra"}
        # Remove reasoning_content attribute
        del mock_message.reasoning_content
        mock_message.tool_calls = None
        mock_message.function_call = None

        mock_choice = MagicMock()
        mock_choice.message = mock_message
        mock_choice.finish_reason = "stop"
        mock_choice.logprobs = None

        mock_response = MagicMock(spec=openai.BaseModel)
        mock_response.choices = [mock_choice]

        # Create a mock parent result
        mock_ai_message = AIMessage(content="Final answer", additional_kwargs={})
        mock_generation = ChatGeneration(message=mock_ai_message)
        mock_chat_result = ChatResult(generations=[mock_generation])

        with patch("langchain_openai.ChatOpenAI._create_chat_result", return_value=mock_chat_result):
            with patch("langchain_openai.ChatOpenAI.__init__", return_value=None):
                model = ChatOpenAIWithReasoning()
                result = model._create_chat_result(mock_response)

                # Check that reasoning was extracted from model_extra
                assert "reasoning_content" in result.generations[0].message.additional_kwargs
                assert result.generations[0].message.additional_kwargs["reasoning_content"] == "Reasoning from model_extra"

    def test_create_chat_result_with_dict_response(self):
        """Test _create_chat_result with dict response (no reasoning extraction)."""
        # Dict responses should not have reasoning extracted
        mock_response = {"choices": [{"message": {"content": "test"}}]}

        mock_ai_message = AIMessage(content="test", additional_kwargs={})
        mock_generation = ChatGeneration(message=mock_ai_message)
        mock_chat_result = ChatResult(generations=[mock_generation])

        with patch("langchain_openai.ChatOpenAI._create_chat_result", return_value=mock_chat_result):
            with patch("langchain_openai.ChatOpenAI.__init__", return_value=None):
                model = ChatOpenAIWithReasoning()
                result = model._create_chat_result(mock_response)

                # Should return result without reasoning_content
                assert "reasoning_content" not in result.generations[0].message.additional_kwargs

    def test_convert_chunk_with_reasoning_content(self):
        """Test _convert_chunk_to_generation_chunk with reasoning_content."""
        chunk = {
            "choices": [{
                "delta": {
                    "content": "chunk content",
                    "reasoning_content": "reasoning chunk",
                },
                "finish_reason": None,
                "index": 0,
            }]
        }

        mock_message_chunk = AIMessageChunk(content="chunk content", additional_kwargs={})
        mock_generation_chunk = ChatGenerationChunk(message=mock_message_chunk)

        with patch("langchain_openai.ChatOpenAI._convert_chunk_to_generation_chunk", return_value=mock_generation_chunk):
            with patch("langchain_openai.ChatOpenAI.__init__", return_value=None):
                model = ChatOpenAIWithReasoning()
                result = model._convert_chunk_to_generation_chunk(chunk, AIMessageChunk, None)

                # Check that reasoning_content was extracted
                assert "reasoning_content" in result.message.additional_kwargs
                assert result.message.additional_kwargs["reasoning_content"] == "reasoning chunk"

    def test_convert_chunk_with_reasoning_field(self):
        """Test _convert_chunk_to_generation_chunk with reasoning field (OpenRouter)."""
        chunk = {
            "choices": [{
                "delta": {
                    "content": "chunk content",
                    "reasoning": "reasoning from OpenRouter",
                },
                "finish_reason": None,
                "index": 0,
            }]
        }

        mock_message_chunk = AIMessageChunk(content="chunk content", additional_kwargs={})
        mock_generation_chunk = ChatGenerationChunk(message=mock_message_chunk)

        with patch("langchain_openai.ChatOpenAI._convert_chunk_to_generation_chunk", return_value=mock_generation_chunk):
            with patch("langchain_openai.ChatOpenAI.__init__", return_value=None):
                model = ChatOpenAIWithReasoning()
                result = model._convert_chunk_to_generation_chunk(chunk, AIMessageChunk, None)

                # Check that reasoning was extracted
                assert "reasoning_content" in result.message.additional_kwargs
                assert result.message.additional_kwargs["reasoning_content"] == "reasoning from OpenRouter"

    def test_convert_chunk_without_reasoning(self):
        """Test _convert_chunk_to_generation_chunk without reasoning."""
        chunk = {
            "choices": [{
                "delta": {
                    "content": "chunk content",
                },
                "finish_reason": None,
                "index": 0,
            }]
        }

        mock_message_chunk = AIMessageChunk(content="chunk content", additional_kwargs={})
        mock_generation_chunk = ChatGenerationChunk(message=mock_message_chunk)

        with patch("langchain_openai.ChatOpenAI._convert_chunk_to_generation_chunk", return_value=mock_generation_chunk):
            with patch("langchain_openai.ChatOpenAI.__init__", return_value=None):
                model = ChatOpenAIWithReasoning()
                result = model._convert_chunk_to_generation_chunk(chunk, AIMessageChunk, None)

                # Should not have reasoning_content
                assert "reasoning_content" not in result.message.additional_kwargs


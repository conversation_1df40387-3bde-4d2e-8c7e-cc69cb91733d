"""Tests for Mem0 configuration classes."""

import pytest
from unittest.mock import MagicMock, patch, AsyncMock

from app.agent.config.mem0_config import (
    OpenAIConfigWithHeaders,
    OpenAILLMWithHeaders,
    OpenAIEmbeddingConfigWithHeaders,
    OpenAIEmbeddingWithHeaders,
)


class TestOpenAIConfigWithHeaders:
    """Tests for OpenAIConfigWithHeaders class."""

    def test_initialization_with_headers(self):
        """Test initialization with custom headers."""
        config = OpenAIConfigWithHeaders(
            model="gpt-4o",
            api_key="test-key",
            default_headers={"X-Custom": "value"},
        )

        assert config.model == "gpt-4o"
        assert config.api_key == "test-key"
        assert config.default_headers == {"X-Custom": "value"}

    def test_initialization_without_headers(self):
        """Test initialization without custom headers."""
        config = OpenAIConfigWithHeaders(
            model="gpt-4o",
            api_key="test-key",
        )

        assert config.model == "gpt-4o"
        # default_headers defaults to empty dict when not provided
        assert config.default_headers is None or config.default_headers == {}

    def test_initialization_with_all_params(self):
        """Test initialization with all parameters."""
        config = OpenAIConfigWithHeaders(
            model="gpt-4o",
            temperature=0.5,
            api_key="test-key",
            max_tokens=4000,
            top_p=0.9,
            openai_base_url="https://api.openai.com/v1",
            default_headers={"X-Custom": "value"},
        )

        assert config.model == "gpt-4o"
        assert config.temperature == 0.5
        assert config.max_tokens == 4000


class TestOpenAILLMWithHeaders:
    """Tests for OpenAILLMWithHeaders class."""

    def test_initialization(self):
        """Test initialization of LLM with headers."""
        config = OpenAIConfigWithHeaders(
            model="gpt-4o",
            api_key="test-key",
            default_headers={"X-Custom": "value"},
        )

        with patch("app.agent.config.mem0_config.OpenAI") as mock_openai:
            mock_client = MagicMock()
            mock_openai.return_value = mock_client

            llm = OpenAILLMWithHeaders(config)

            assert llm.config == config

    def test_generate_response(self):
        """Test generate_response method."""
        config = OpenAIConfigWithHeaders(
            model="gpt-4o",
            api_key="test-key",
            default_headers={"X-Custom": "value"},
        )

        with patch("app.agent.config.mem0_config.OpenAI") as mock_openai:
            mock_client = MagicMock()
            mock_response = MagicMock()
            mock_response.choices = [MagicMock(message=MagicMock(content="Test response"))]
            mock_client.chat.completions.create.return_value = mock_response
            mock_openai.return_value = mock_client

            llm = OpenAILLMWithHeaders(config)

            messages = [{"role": "user", "content": "Hello"}]
            response = llm.generate_response(messages)

            assert response == "Test response"


class TestOpenAIEmbeddingConfigWithHeaders:
    """Tests for OpenAIEmbeddingConfigWithHeaders class."""

    def test_initialization_with_headers(self):
        """Test initialization with custom headers."""
        config = OpenAIEmbeddingConfigWithHeaders(
            model="text-embedding-3-small",
            api_key="test-key",
            default_headers={"X-Custom": "value"},
        )

        assert config.model == "text-embedding-3-small"
        assert config.api_key == "test-key"
        assert config.default_headers == {"X-Custom": "value"}

    def test_initialization_with_embedding_dims(self):
        """Test initialization with embedding dimensions."""
        config = OpenAIEmbeddingConfigWithHeaders(
            model="text-embedding-3-small",
            api_key="test-key",
            embedding_dims=1536,
        )

        assert config.embedding_dims == 1536


class TestOpenAIEmbeddingWithHeaders:
    """Tests for OpenAIEmbeddingWithHeaders class."""

    def test_initialization(self):
        """Test initialization of embedding with headers."""
        config = OpenAIEmbeddingConfigWithHeaders(
            model="text-embedding-3-small",
            api_key="test-key",
            default_headers={"X-Custom": "value"},
        )

        with patch("app.agent.config.mem0_config.OpenAI") as mock_openai:
            mock_client = MagicMock()
            mock_openai.return_value = mock_client

            embedding = OpenAIEmbeddingWithHeaders(config)

            assert embedding.config == config

    def test_embed(self):
        """Test embed method."""
        config = OpenAIEmbeddingConfigWithHeaders(
            model="text-embedding-3-small",
            api_key="test-key",
            default_headers={"X-Custom": "value"},
        )

        with patch("app.agent.config.mem0_config.OpenAI") as mock_openai:
            mock_client = MagicMock()
            mock_embedding = MagicMock()
            mock_embedding.embedding = [0.1, 0.2, 0.3]
            mock_response = MagicMock()
            mock_response.data = [mock_embedding]
            mock_client.embeddings.create.return_value = mock_response
            mock_openai.return_value = mock_client

            embedding = OpenAIEmbeddingWithHeaders(config)

            result = embedding.embed("Test text")

            assert result == [0.1, 0.2, 0.3]


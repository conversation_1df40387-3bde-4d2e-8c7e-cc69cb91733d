"""
Tests for app/helper/stop_signal.py
"""
import pytest
from unittest.mock import Mo<PERSON>, AsyncMock, patch
from app.helper.stop_signal import check_stop_signal, clear_stop_signal


class TestCheckStopSignal:
    """Test check_stop_signal function"""

    @pytest.mark.asyncio
    async def test_check_stop_signal_exists(self):
        """Test when stop signal exists"""
        # Setup
        mock_redis = Mock()
        mock_redis._ensure_connection = AsyncMock()
        mock_redis.get_value = AsyncMock(return_value="true")

        # Execute
        result = await check_stop_signal("conv-123", mock_redis)

        # Verify
        assert result is True
        mock_redis._ensure_connection.assert_called_once()
        mock_redis.get_value.assert_called_once_with("stop_signal:conv-123")

    @pytest.mark.asyncio
    async def test_check_stop_signal_not_exists(self):
        """Test when stop signal does not exist"""
        # Setup
        mock_redis = Mock()
        mock_redis._ensure_connection = AsyncMock()
        mock_redis.get_value = AsyncMock(return_value=None)

        # Execute
        result = await check_stop_signal("conv-123", mock_redis)

        # Verify
        assert result is False
        mock_redis.get_value.assert_called_once_with("stop_signal:conv-123")

    @pytest.mark.asyncio
    async def test_check_stop_signal_empty_string(self):
        """Test when stop signal is empty string"""
        # Setup
        mock_redis = Mock()
        mock_redis._ensure_connection = AsyncMock()
        mock_redis.get_value = AsyncMock(return_value="")

        # Execute
        result = await check_stop_signal("conv-123", mock_redis)

        # Verify
        assert result is False

    @pytest.mark.asyncio
    async def test_check_stop_signal_truthy_value(self):
        """Test when stop signal has truthy value"""
        # Setup
        mock_redis = Mock()
        mock_redis._ensure_connection = AsyncMock()
        mock_redis.get_value = AsyncMock(return_value="1")

        # Execute
        result = await check_stop_signal("conv-123", mock_redis)

        # Verify
        assert result is True

    @pytest.mark.asyncio
    async def test_check_stop_signal_redis_error(self):
        """Test error handling when Redis fails"""
        # Setup
        mock_redis = Mock()
        mock_redis._ensure_connection = AsyncMock()
        mock_redis.get_value = AsyncMock(side_effect=Exception("Redis connection error"))

        # Execute
        result = await check_stop_signal("conv-123", mock_redis)

        # Verify - should return False on error
        assert result is False

    @pytest.mark.asyncio
    async def test_check_stop_signal_connection_error(self):
        """Test error handling when connection fails"""
        # Setup
        mock_redis = Mock()
        mock_redis._ensure_connection = AsyncMock(side_effect=Exception("Connection failed"))

        # Execute
        result = await check_stop_signal("conv-123", mock_redis)

        # Verify - should return False on error
        assert result is False


class TestClearStopSignal:
    """Test clear_stop_signal function"""

    @pytest.mark.asyncio
    async def test_clear_stop_signal_success(self):
        """Test successful stop signal clearing"""
        # Setup
        mock_redis = Mock()
        mock_redis._ensure_connection = AsyncMock()
        mock_redis.delete = AsyncMock()

        # Execute
        await clear_stop_signal("conv-123", mock_redis)

        # Verify
        mock_redis._ensure_connection.assert_called_once()
        mock_redis.delete.assert_called_once_with("stop_signal:conv-123")

    @pytest.mark.asyncio
    async def test_clear_stop_signal_redis_error(self):
        """Test error handling when Redis delete fails"""
        # Setup
        mock_redis = Mock()
        mock_redis._ensure_connection = AsyncMock()
        mock_redis.delete = AsyncMock(side_effect=Exception("Delete failed"))

        # Execute - should not raise exception
        await clear_stop_signal("conv-123", mock_redis)

        # Verify - function completes without error
        mock_redis.delete.assert_called_once()

    @pytest.mark.asyncio
    async def test_clear_stop_signal_connection_error(self):
        """Test error handling when connection fails"""
        # Setup
        mock_redis = Mock()
        mock_redis._ensure_connection = AsyncMock(side_effect=Exception("Connection failed"))

        # Execute - should not raise exception
        await clear_stop_signal("conv-123", mock_redis)

        # Verify - function completes without error (exception is caught)
        mock_redis._ensure_connection.assert_called_once()

    @pytest.mark.asyncio
    async def test_clear_stop_signal_different_conversation_ids(self):
        """Test clearing signals for different conversation IDs"""
        # Setup
        mock_redis = Mock()
        mock_redis._ensure_connection = AsyncMock()
        mock_redis.delete = AsyncMock()

        # Execute
        await clear_stop_signal("conv-abc", mock_redis)
        await clear_stop_signal("conv-xyz", mock_redis)

        # Verify
        assert mock_redis.delete.call_count == 2
        mock_redis.delete.assert_any_call("stop_signal:conv-abc")
        mock_redis.delete.assert_any_call("stop_signal:conv-xyz")


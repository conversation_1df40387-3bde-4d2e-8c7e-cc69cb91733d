"""
Tests for app/helper/background_tasks.py
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from app.helper.background_tasks import background_summarize


class TestBackgroundSummarize:
    """Test background_summarize function"""

    @pytest.mark.asyncio
    @patch("app.helper.background_tasks.trace_operation")
    @patch("app.helper.background_tasks.check_and_summarize")
    async def test_background_summarize_success(self, mock_check_and_summarize, mock_trace_operation):
        """Test successful background summarization"""
        # Setup
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_trace_operation.return_value = mock_context_manager
        
        mock_check_and_summarize.return_value = AsyncMock()
        mock_supervisor = Mock()

        # Execute
        await background_summarize(
            supervisor=mock_supervisor,
            conversation_id="conv-123",
            user_id="user-456",
            organisation_id="org-789",
            agent_id="agent-abc",
            total_tokens=1000,
        )

        # Verify
        mock_trace_operation.assert_called_once_with(
            "background.summarize",
            attributes={
                "conversation_id": "conv-123",
                "user_id": "user-456",
                "agent_id": "agent-abc",
                "total_tokens": 1000,
            },
        )
        mock_span.set_attribute.assert_called_once_with("success", True)

    @pytest.mark.asyncio
    @patch("app.helper.background_tasks.trace_operation")
    @patch("app.helper.background_tasks.check_and_summarize")
    async def test_background_summarize_with_none_agent_id(self, mock_check_and_summarize, mock_trace_operation):
        """Test background summarization with None agent_id"""
        # Setup
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_trace_operation.return_value = mock_context_manager
        
        mock_check_and_summarize.return_value = AsyncMock()
        mock_supervisor = Mock()

        # Execute
        await background_summarize(
            supervisor=mock_supervisor,
            conversation_id="conv-123",
            user_id="user-456",
            organisation_id="org-789",
            agent_id=None,
            total_tokens=1000,
        )

        # Verify - agent_id should be "none" in attributes
        call_args = mock_trace_operation.call_args
        assert call_args[1]["attributes"]["agent_id"] == "none"

    @pytest.mark.asyncio
    @patch("app.helper.background_tasks.logger")
    @patch("app.helper.background_tasks.trace_operation")
    @patch("app.helper.background_tasks.check_and_summarize")
    async def test_background_summarize_error_handling(self, mock_check_and_summarize, mock_trace_operation, mock_logger):
        """Test error handling in background summarization"""
        # Setup
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_trace_operation.return_value = mock_context_manager
        
        test_error = Exception("Summarization failed")
        mock_check_and_summarize.side_effect = test_error
        mock_supervisor = Mock()

        # Execute - should not raise exception
        await background_summarize(
            supervisor=mock_supervisor,
            conversation_id="conv-123",
            user_id="user-456",
            organisation_id="org-789",
            agent_id="agent-abc",
            total_tokens=1000,
        )

        # Verify error was logged and recorded
        mock_span.set_attribute.assert_any_call("error", True)
        mock_span.record_exception.assert_called_once_with(test_error)
        mock_logger.error.assert_called_once()

    @pytest.mark.asyncio
    @patch("app.helper.background_tasks.trace_operation")
    @patch("app.helper.background_tasks.check_and_summarize")
    async def test_background_summarize_calls_check_and_summarize(self, mock_check_and_summarize, mock_trace_operation):
        """Test that check_and_summarize is called with correct parameters"""
        # Setup
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_trace_operation.return_value = mock_context_manager
        
        mock_check_and_summarize.return_value = AsyncMock()
        mock_supervisor = Mock()

        # Execute
        await background_summarize(
            supervisor=mock_supervisor,
            conversation_id="conv-123",
            user_id="user-456",
            organisation_id="org-789",
            agent_id="agent-abc",
            total_tokens=1000,
        )

        # Verify
        mock_check_and_summarize.assert_called_once_with(
            supervisor=mock_supervisor,
            conversation_id="conv-123",
            user_id="user-456",
            organisation_id="org-789",
            agent_id="agent-abc",
            total_tokens=1000,
        )

    @pytest.mark.asyncio
    @patch("app.helper.background_tasks.trace_operation")
    @patch("app.helper.background_tasks.check_and_summarize")
    async def test_background_summarize_different_token_counts(self, mock_check_and_summarize, mock_trace_operation):
        """Test background summarization with different token counts"""
        # Setup
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_trace_operation.return_value = mock_context_manager
        
        mock_check_and_summarize.return_value = AsyncMock()
        mock_supervisor = Mock()

        # Execute with different token counts
        for token_count in [100, 5000, 10000]:
            await background_summarize(
                supervisor=mock_supervisor,
                conversation_id="conv-123",
                user_id="user-456",
                organisation_id="org-789",
                agent_id="agent-abc",
                total_tokens=token_count,
            )

        # Verify called multiple times
        assert mock_check_and_summarize.call_count == 3


"""Tests for datetime helper functions."""

import pytest
from unittest.mock import patch
from datetime import datetime
from zoneinfo import ZoneInfo

from app.helper.datetime_helper import (
    normalize_timezone,
    get_current_datetime,
    get_current_date,
    get_current_time,
    TIMEZONE_ALIASES,
)


class TestNormalizeTimezone:
    """Tests for normalize_timezone function."""

    def test_normalize_known_alias(self):
        """Test normalizing a known timezone alias."""
        assert normalize_timezone("Asia/Calcutta") == "Asia/Kolkata"
        assert normalize_timezone("Calcutta") == "Asia/Kolkata"
        assert normalize_timezone("Kolkata") == "Asia/Kolkata"

    def test_normalize_unknown_timezone(self):
        """Test that unknown timezones are returned as-is."""
        assert normalize_timezone("America/New_York") == "America/New_York"
        assert normalize_timezone("UTC") == "UTC"
        assert normalize_timezone("Europe/London") == "Europe/London"

    def test_normalize_empty_string(self):
        """Test normalizing empty string."""
        assert normalize_timezone("") == ""


class TestGetCurrentDatetime:
    """Tests for get_current_datetime function."""

    def test_get_current_datetime_utc(self):
        """Test getting current datetime in UTC."""
        result = get_current_datetime("UTC")
        assert "(UTC)" in result
        # Format should be YYYY-MM-DD HH:MM (Timezone)
        assert len(result.split(" ")) >= 2

    def test_get_current_datetime_with_valid_timezone(self):
        """Test getting current datetime with valid timezone."""
        result = get_current_datetime("America/New_York")
        assert "(America/New_York)" in result

    def test_get_current_datetime_with_alias(self):
        """Test getting current datetime with timezone alias."""
        result = get_current_datetime("Asia/Calcutta")
        # Should normalize to Asia/Kolkata
        assert "(Asia/Kolkata)" in result

    def test_get_current_datetime_with_invalid_timezone(self):
        """Test getting current datetime with invalid timezone falls back to UTC."""
        result = get_current_datetime("Invalid/Timezone")
        assert "(UTC)" in result

    def test_get_current_datetime_default(self):
        """Test getting current datetime with default timezone."""
        result = get_current_datetime()
        assert "(UTC)" in result

    @patch("app.helper.datetime_helper.datetime")
    def test_get_current_datetime_format(self, mock_datetime):
        """Test the format of the datetime string."""
        mock_now = datetime(2025, 12, 8, 14, 30, 0, tzinfo=ZoneInfo("UTC"))
        mock_datetime.now.return_value = mock_now
        
        result = get_current_datetime("UTC")
        assert "2025-12-08 14:30 (UTC)" == result


class TestGetCurrentDate:
    """Tests for get_current_date function."""

    def test_get_current_date_utc(self):
        """Test getting current date in UTC."""
        result = get_current_date("UTC")
        # Format should be YYYY-MM-DD
        parts = result.split("-")
        assert len(parts) == 3
        assert len(parts[0]) == 4  # Year
        assert len(parts[1]) == 2  # Month
        assert len(parts[2]) == 2  # Day

    def test_get_current_date_with_valid_timezone(self):
        """Test getting current date with valid timezone."""
        result = get_current_date("America/New_York")
        parts = result.split("-")
        assert len(parts) == 3

    def test_get_current_date_with_invalid_timezone(self):
        """Test getting current date with invalid timezone falls back to UTC."""
        result = get_current_date("Invalid/Timezone")
        parts = result.split("-")
        assert len(parts) == 3

    def test_get_current_date_default(self):
        """Test getting current date with default timezone."""
        result = get_current_date()
        parts = result.split("-")
        assert len(parts) == 3


class TestGetCurrentTime:
    """Tests for get_current_time function."""

    def test_get_current_time_utc(self):
        """Test getting current time in UTC."""
        result = get_current_time("UTC")
        # Format should be HH:MM
        parts = result.split(":")
        assert len(parts) == 2
        assert len(parts[0]) == 2  # Hours
        assert len(parts[1]) == 2  # Minutes

    def test_get_current_time_with_valid_timezone(self):
        """Test getting current time with valid timezone."""
        result = get_current_time("America/New_York")
        parts = result.split(":")
        assert len(parts) == 2

    def test_get_current_time_with_invalid_timezone(self):
        """Test getting current time with invalid timezone falls back to UTC."""
        result = get_current_time("Invalid/Timezone")
        parts = result.split(":")
        assert len(parts) == 2

    def test_get_current_time_default(self):
        """Test getting current time with default timezone."""
        result = get_current_time()
        parts = result.split(":")
        assert len(parts) == 2

    def test_get_current_time_with_alias(self):
        """Test getting current time with timezone alias."""
        result = get_current_time("Asia/Calcutta")
        parts = result.split(":")
        assert len(parts) == 2


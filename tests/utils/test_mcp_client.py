"""
Tests for app/utils/mcp_client.py
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from app.utils.mcp_client import MCPClient


class TestMCPClient:
    """Test MCPClient class"""

    @patch("app.utils.mcp_client.get_settings")
    def test_init(self, mock_get_settings):
        """Test MCPClient initialization"""
        # Setup
        mock_settings = Mock()
        mock_get_settings.return_value = mock_settings

        # Execute
        client = MCPClient(user_id="user-123")

        # Verify
        assert client.user_id == "user-123"
        assert client.settings == mock_settings

    @pytest.mark.asyncio
    @patch("app.utils.mcp_client.trace_operation")
    @patch("app.utils.mcp_client.streamablehttp_client")
    @patch("app.utils.mcp_client.ClientSession")
    @patch("app.utils.mcp_client.get_settings")
    async def test_execute_tool_success(self, mock_get_settings, mock_client_session_class, mock_streamable_client, mock_trace_operation):
        """Test successful tool execution"""
        # Setup
        mock_settings = Mock()
        mock_settings.mcp.gateway_url = "https://gateway.example.com/"
        mock_settings.mcp.api_key = "test-api-key"
        mock_get_settings.return_value = mock_settings

        # Mock trace operation
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_trace_operation.return_value = mock_context_manager

        # Mock streamable client
        mock_read_stream = Mock()
        mock_write_stream = Mock()
        mock_streamable_context = AsyncMock()
        mock_streamable_context.__aenter__ = AsyncMock(return_value=(mock_read_stream, mock_write_stream, None))
        mock_streamable_context.__aexit__ = AsyncMock(return_value=False)
        mock_streamable_client.return_value = mock_streamable_context

        # Mock client session
        mock_session = Mock()
        mock_session.initialize = AsyncMock()
        mock_session.call_tool = AsyncMock(return_value={"result": "success"})
        mock_session_context = AsyncMock()
        mock_session_context.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session_context.__aexit__ = AsyncMock(return_value=False)
        mock_client_session_class.return_value = mock_session_context

        # Execute
        client = MCPClient(user_id="user-123")
        result = await client.execute_tool(
            mcp_name_slug="test-mcp",
            tool_name="test-tool",
            tool_parameters={"param1": "value1"}
        )

        # Verify
        assert result == {"result": "success"}
        mock_session.initialize.assert_called_once()
        mock_session.call_tool.assert_called_once_with("test-tool", {"param1": "value1"})

    @pytest.mark.asyncio
    @patch("app.utils.mcp_client.logger")
    @patch("app.utils.mcp_client.trace_operation")
    @patch("app.utils.mcp_client.streamablehttp_client")
    @patch("app.utils.mcp_client.get_settings")
    async def test_execute_tool_error(self, mock_get_settings, mock_streamable_client, mock_trace_operation, mock_logger):
        """Test tool execution error handling"""
        # Setup
        mock_settings = Mock()
        mock_settings.mcp.gateway_url = "https://gateway.example.com/"
        mock_settings.mcp.api_key = "test-api-key"
        mock_get_settings.return_value = mock_settings

        # Mock trace operation
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_trace_operation.return_value = mock_context_manager

        # Mock streamable client to raise error
        test_error = Exception("Connection failed")
        mock_streamable_client.side_effect = test_error

        # Execute and verify exception is raised
        client = MCPClient(user_id="user-123")
        with pytest.raises(Exception) as exc_info:
            await client.execute_tool(
                mcp_name_slug="test-mcp",
                tool_name="test-tool",
                tool_parameters={"param1": "value1"}
            )

        # Verify error was logged and recorded
        assert exc_info.value == test_error
        mock_logger.error.assert_called()
        mock_span.set_attribute.assert_called_with("error", True)
        mock_span.record_exception.assert_called_with(test_error)

    @pytest.mark.asyncio
    @patch("app.utils.mcp_client.trace_operation")
    @patch("app.utils.mcp_client.streamablehttp_client")
    @patch("app.utils.mcp_client.ClientSession")
    @patch("app.utils.mcp_client.get_settings")
    async def test_execute_tool_url_construction(self, mock_get_settings, mock_client_session_class, mock_streamable_client, mock_trace_operation):
        """Test MCP URL construction"""
        # Setup
        mock_settings = Mock()
        mock_settings.mcp.gateway_url = "https://gateway.example.com/"
        mock_settings.mcp.api_key = "test-api-key"
        mock_get_settings.return_value = mock_settings

        # Mock trace operation
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_trace_operation.return_value = mock_context_manager

        # Mock streamable client
        mock_read_stream = Mock()
        mock_write_stream = Mock()
        mock_streamable_context = AsyncMock()
        mock_streamable_context.__aenter__ = AsyncMock(return_value=(mock_read_stream, mock_write_stream, None))
        mock_streamable_context.__aexit__ = AsyncMock(return_value=False)
        mock_streamable_client.return_value = mock_streamable_context

        # Mock client session
        mock_session = Mock()
        mock_session.initialize = AsyncMock()
        mock_session.call_tool = AsyncMock(return_value={"result": "success"})
        mock_session_context = AsyncMock()
        mock_session_context.__aenter__ = AsyncMock(return_value=mock_session)
        mock_session_context.__aexit__ = AsyncMock(return_value=False)
        mock_client_session_class.return_value = mock_session_context

        # Execute
        client = MCPClient(user_id="user-123")
        await client.execute_tool(
            mcp_name_slug="test-mcp",
            tool_name="test-tool",
            tool_parameters={}
        )

        # Verify URL was constructed correctly
        expected_url = "https://gateway.example.com?name_slug=test-mcp&apiKey=test-api-key&userId=user-123"
        mock_streamable_client.assert_called_once_with(
            expected_url,
            timeout=300,
            sse_read_timeout=300
        )


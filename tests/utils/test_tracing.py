"""
Tests for app/utils/tracing.py
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from opentelemetry.trace import SpanKind
from app.utils.tracing import trace_operation


class TestTraceOperation:
    """Test trace_operation context manager"""

    @patch("app.utils.tracing.trace.get_tracer")
    def test_trace_operation_basic(self, mock_get_tracer):
        """Test basic trace operation"""
        # Setup
        mock_tracer = Mock()
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_tracer.start_as_current_span.return_value = mock_context_manager
        mock_get_tracer.return_value = mock_tracer

        # Execute
        with trace_operation("test_operation") as span:
            assert span == mock_span

        # Verify
        mock_get_tracer.assert_called_once()
        mock_tracer.start_as_current_span.assert_called_once_with(
            "test_operation",
            kind=SpanKind.INTERNAL,
            attributes=None
        )

    @patch("app.utils.tracing.trace.get_tracer")
    def test_trace_operation_with_attributes(self, mock_get_tracer):
        """Test trace operation with custom attributes"""
        # Setup
        mock_tracer = Mock()
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_tracer.start_as_current_span.return_value = mock_context_manager
        mock_get_tracer.return_value = mock_tracer

        attributes = {"user_id": "123", "request_id": "abc"}

        # Execute
        with trace_operation("test_operation", attributes=attributes) as span:
            assert span == mock_span

        # Verify
        mock_tracer.start_as_current_span.assert_called_once_with(
            "test_operation",
            kind=SpanKind.INTERNAL,
            attributes=attributes
        )

    @patch("app.utils.tracing.trace.get_tracer")
    def test_trace_operation_with_custom_kind(self, mock_get_tracer):
        """Test trace operation with custom span kind"""
        # Setup
        mock_tracer = Mock()
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_tracer.start_as_current_span.return_value = mock_context_manager
        mock_get_tracer.return_value = mock_tracer

        # Execute
        with trace_operation("test_operation", kind=SpanKind.CLIENT) as span:
            assert span == mock_span

        # Verify
        mock_tracer.start_as_current_span.assert_called_once_with(
            "test_operation",
            kind=SpanKind.CLIENT,
            attributes=None
        )

    @patch("app.utils.tracing.trace.get_tracer")
    def test_trace_operation_exception_handling(self, mock_get_tracer):
        """Test trace operation handles exceptions correctly"""
        # Setup
        mock_tracer = Mock()
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_tracer.start_as_current_span.return_value = mock_context_manager
        mock_get_tracer.return_value = mock_tracer

        test_error = ValueError("test error")

        # Execute and verify exception is raised
        with pytest.raises(ValueError):
            with trace_operation("test_operation") as span:
                raise test_error

        # Verify error was recorded
        mock_span.set_attribute.assert_called_once_with("error", True)
        mock_span.record_exception.assert_called_once_with(test_error)

    @patch("app.utils.tracing.trace.get_tracer")
    def test_trace_operation_all_span_kinds(self, mock_get_tracer):
        """Test trace operation with all span kinds"""
        # Setup
        mock_tracer = Mock()
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_tracer.start_as_current_span.return_value = mock_context_manager
        mock_get_tracer.return_value = mock_tracer

        span_kinds = [
            SpanKind.INTERNAL,
            SpanKind.CLIENT,
            SpanKind.SERVER,
            SpanKind.PRODUCER,
            SpanKind.CONSUMER,
        ]

        # Execute with each span kind
        for kind in span_kinds:
            with trace_operation("test_operation", kind=kind):
                pass

        # Verify called for each kind
        assert mock_tracer.start_as_current_span.call_count == len(span_kinds)

    @patch("app.utils.tracing.trace.get_tracer")
    def test_trace_operation_yields_span(self, mock_get_tracer):
        """Test that trace operation yields the span object"""
        # Setup
        mock_tracer = Mock()
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_tracer.start_as_current_span.return_value = mock_context_manager
        mock_get_tracer.return_value = mock_tracer

        # Execute
        with trace_operation("test_operation") as yielded_span:
            # Verify the yielded object is the span
            assert yielded_span is mock_span
            # Verify we can call methods on it
            yielded_span.set_attribute("custom_attr", "value")

        # Verify the method was called
        mock_span.set_attribute.assert_called_with("custom_attr", "value")

    @patch("app.utils.tracing.trace.get_tracer")
    def test_trace_operation_no_exception(self, mock_get_tracer):
        """Test trace operation when no exception occurs"""
        # Setup
        mock_tracer = Mock()
        mock_span = Mock()
        mock_context_manager = MagicMock()
        mock_context_manager.__enter__ = Mock(return_value=mock_span)
        mock_context_manager.__exit__ = Mock(return_value=False)
        mock_tracer.start_as_current_span.return_value = mock_context_manager
        mock_get_tracer.return_value = mock_tracer

        # Execute
        with trace_operation("test_operation") as span:
            pass

        # Verify error attributes were NOT set
        mock_span.set_attribute.assert_not_called()
        mock_span.record_exception.assert_not_called()


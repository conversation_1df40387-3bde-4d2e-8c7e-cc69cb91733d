"""Tests for workflow parser functions."""

import pytest
from app.utils.workflow_parser import (
    parse_workflow_event,
    format_workflow_output,
    get_workflow_summary,
)


class TestParseWorkflowEvent:
    """Tests for parse_workflow_event function."""

    def test_parse_event_with_db_save_true(self):
        """Test parsing event with db_save=True."""
        event_data = {
            "db_save": True,
            "workflow_id": "wf-123",
            "correlation_id": "corr-456",
            "workflow_name": "Test Workflow",
            "node_name": "Node 1",
            "node_id": "node-1",
            "workflow_status": "running",
            "event_type": "node.completed",
            "created_at": "2025-12-08T14:30:00Z",
            "data": {"key": "value"},
        }
        
        result = parse_workflow_event(event_data)
        
        assert result is not None
        assert result["workflow_id"] == "wf-123"
        assert result["correlation_id"] == "corr-456"
        assert result["workflow_name"] == "Test Workflow"
        assert result["node_name"] == "Node 1"
        assert result["node_id"] == "node-1"
        assert result["workflow_status"] == "running"
        assert result["node_status"] == "node.completed"
        assert result["created_at"] == "2025-12-08T14:30:00Z"
        assert result["data"] == {"key": "value"}

    def test_parse_event_with_db_save_false(self):
        """Test parsing event with db_save=False returns None."""
        event_data = {
            "db_save": False,
            "workflow_id": "wf-123",
        }
        
        result = parse_workflow_event(event_data)
        assert result is None

    def test_parse_event_without_db_save(self):
        """Test parsing event without db_save field returns None."""
        event_data = {
            "workflow_id": "wf-123",
        }
        
        result = parse_workflow_event(event_data)
        assert result is None

    def test_parse_event_with_missing_fields(self):
        """Test parsing event with missing fields uses None defaults."""
        event_data = {
            "db_save": True,
        }
        
        result = parse_workflow_event(event_data)
        
        assert result is not None
        assert result["workflow_id"] is None
        assert result["correlation_id"] is None
        assert result["workflow_name"] is None
        assert result["data"] == {}


class TestFormatWorkflowOutput:
    """Tests for format_workflow_output function."""

    def test_format_empty_list(self):
        """Test formatting empty event list."""
        result = format_workflow_output([])
        assert result == []

    def test_format_with_none_events(self):
        """Test formatting list with None events filters them out."""
        events = [
            {"workflow_id": "wf-1"},
            None,
            {"workflow_id": "wf-2"},
            None,
        ]
        
        result = format_workflow_output(events)
        
        assert len(result) == 2
        assert result[0]["workflow_id"] == "wf-1"
        assert result[1]["workflow_id"] == "wf-2"

    def test_format_all_valid_events(self):
        """Test formatting list with all valid events."""
        events = [
            {"workflow_id": "wf-1", "node_status": "node.completed"},
            {"workflow_id": "wf-2", "node_status": "node.started"},
        ]
        
        result = format_workflow_output(events)
        assert len(result) == 2


class TestGetWorkflowSummary:
    """Tests for get_workflow_summary function."""

    def test_summary_empty_events(self):
        """Test summary with empty event list."""
        result = get_workflow_summary([])
        assert result == "No workflow events recorded"

    def test_summary_completed_workflow(self):
        """Test summary for completed workflow."""
        events = [
            {"node_status": "node.completed", "workflow_status": "running"},
            {"node_status": "node.completed", "workflow_status": "running"},
            {"node_status": "node.completed", "workflow_status": "completed"},
        ]
        
        result = get_workflow_summary(events)
        
        assert "Workflow completed successfully" in result
        assert "3 nodes" in result
        assert "3 completed" in result
        assert "0 failed" in result

    def test_summary_failed_workflow(self):
        """Test summary for failed workflow."""
        events = [
            {"node_status": "node.completed", "workflow_status": "running"},
            {"node_status": "node.failed", "workflow_status": "failed"},
        ]
        
        result = get_workflow_summary(events)
        
        assert "Workflow failed" in result
        assert "2 nodes" in result
        assert "1 completed" in result
        assert "1 failed" in result

    def test_summary_unknown_status(self):
        """Test summary for unknown workflow status."""
        events = [
            {"node_status": "node.started", "workflow_status": "in_progress"},
        ]
        
        result = get_workflow_summary(events)
        
        assert "Workflow status: in_progress" in result
        assert "1 nodes" in result


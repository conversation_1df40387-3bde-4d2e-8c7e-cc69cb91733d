"""
Tests for app/utils/metrics.py
"""
import pytest
from unittest.mock import Mock, patch
from app.utils.metrics import MetricsManager, get_metrics_manager


class TestMetricsManager:
    """Test MetricsManager class"""

    @patch("app.utils.metrics.metrics.get_meter")
    def test_init(self, mock_get_meter):
        """Test MetricsManager initialization"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        
        # Mock all counter and histogram creations
        mock_meter.create_counter = Mock()
        mock_meter.create_histogram = Mock()

        # Execute
        manager = MetricsManager()

        # Verify
        assert manager.meter is not None
        mock_get_meter.assert_called_once()

    @patch("app.utils.metrics.metrics.get_meter")
    def test_agent_metrics_created(self, mock_get_meter):
        """Test that agent metrics are created"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)

        # Execute
        manager = MetricsManager()

        # Verify agent metrics exist
        assert manager.agent_executions is not None
        assert manager.agent_execution_duration is not None
        assert manager.agent_errors is not None

    @patch("app.utils.metrics.metrics.get_meter")
    def test_tool_metrics_created(self, mock_get_meter):
        """Test that tool metrics are created"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)

        # Execute
        manager = MetricsManager()

        # Verify tool metrics exist
        assert manager.tool_calls is not None
        assert manager.tool_execution_duration is not None
        assert manager.tool_errors is not None

    @patch("app.utils.metrics.metrics.get_meter")
    def test_api_metrics_created(self, mock_get_meter):
        """Test that API metrics are created"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)

        # Execute
        manager = MetricsManager()

        # Verify API metrics exist
        assert manager.api_calls is not None
        assert manager.api_duration is not None
        assert manager.api_errors is not None

    @patch("app.utils.metrics.metrics.get_meter")
    def test_memory_metrics_created(self, mock_get_meter):
        """Test that memory metrics are created"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)

        # Execute
        manager = MetricsManager()

        # Verify memory metrics exist
        assert manager.memory_operations is not None
        assert manager.memory_duration is not None

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_agent_execution(self, mock_get_meter):
        """Test recording agent execution metric"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter

        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)

        manager = MetricsManager()

        # Execute
        manager.record_agent_execution(1500, {"agent_id": "agent-123", "status": "success"})

        # Verify
        mock_counter.add.assert_called_once_with(1, {"agent_id": "agent-123", "status": "success"})
        mock_histogram.record.assert_called_once_with(1500, {"agent_id": "agent-123", "status": "success"})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_tool_call(self, mock_get_meter):
        """Test recording tool call metric"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter

        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)

        manager = MetricsManager()

        # Execute
        manager.record_tool_call("web_search", {"status": "success"})

        # Verify
        mock_counter.add.assert_called_once_with(1, {"tool_name": "web_search", "status": "success"})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_agent_error(self, mock_get_meter):
        """Test recording agent error metric"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter

        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)

        manager = MetricsManager()

        # Execute
        manager.record_agent_error("ValueError", {"agent_id": "agent-123"})

        # Verify
        mock_counter.add.assert_called_once_with(1, {"error_type": "ValueError", "agent_id": "agent-123"})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_tool_execution(self, mock_get_meter):
        """Test recording tool execution"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)
        manager = MetricsManager()

        # Execute
        manager.record_tool_execution("test_tool", 150.5, {"status": "success"})

        # Verify
        mock_histogram.record.assert_called_once_with(150.5, {"tool_name": "test_tool", "status": "success"})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_tool_error(self, mock_get_meter):
        """Test recording tool error"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)
        manager = MetricsManager()

        # Execute
        manager.record_tool_error("test_tool", "validation_error", {"details": "invalid"})

        # Verify
        mock_counter.add.assert_called_once_with(1, {"tool_name": "test_tool", "error_type": "validation_error", "details": "invalid"})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_external_api_call(self, mock_get_meter):
        """Test recording external API call"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)
        manager = MetricsManager()

        # Execute
        manager.record_external_api_call("openai", "/v1/chat", {"model": "gpt-4"})

        # Verify
        mock_counter.add.assert_called_once_with(1, {"api_name": "openai", "endpoint": "/v1/chat", "model": "gpt-4"})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_external_api_duration(self, mock_get_meter):
        """Test recording external API duration"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)
        manager = MetricsManager()

        # Execute
        manager.record_external_api_duration(250.0, "openai", "/v1/chat", {"status": 200})

        # Verify
        mock_histogram.record.assert_called_once_with(250.0, {"api_name": "openai", "endpoint": "/v1/chat", "status": 200})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_external_api_error(self, mock_get_meter):
        """Test recording external API error"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)
        manager = MetricsManager()

        # Execute
        manager.record_external_api_error("openai", "/v1/chat", "rate_limit", {"retry": 60})

        # Verify
        mock_counter.add.assert_called_once_with(1, {"api_name": "openai", "endpoint": "/v1/chat", "error_type": "rate_limit", "retry": 60})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_memory_operation(self, mock_get_meter):
        """Test recording memory operation"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)
        manager = MetricsManager()

        # Execute
        manager.record_memory_operation("cache_hit", 5.0, {"key": "test"})

        # Verify
        mock_counter.add.assert_called_once_with(1, {"operation": "cache_hit", "key": "test"})
        mock_histogram.record.assert_called_once_with(5.0, {"operation": "cache_hit", "key": "test"})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_workflow_execution(self, mock_get_meter):
        """Test recording workflow execution"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)
        manager = MetricsManager()

        # Execute
        manager.record_workflow_execution(1000.0, {"workflow_id": "test"})

        # Verify
        mock_counter.add.assert_called_once_with(1, {"workflow_id": "test"})
        mock_histogram.record.assert_called_once_with(1000.0, {"workflow_id": "test"})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_workflow_error(self, mock_get_meter):
        """Test recording workflow error"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)
        manager = MetricsManager()

        # Execute
        manager.record_workflow_error("timeout", {"workflow_id": "test"})

        # Verify
        mock_counter.add.assert_called_once_with(1, {"error_type": "timeout", "workflow_id": "test"})

    @patch("app.utils.metrics.metrics.get_meter")
    def test_record_exception(self, mock_get_meter):
        """Test recording exception"""
        # Setup
        mock_meter = Mock()
        mock_get_meter.return_value = mock_meter
        mock_counter = Mock()
        mock_histogram = Mock()
        mock_meter.create_counter = Mock(return_value=mock_counter)
        mock_meter.create_histogram = Mock(return_value=mock_histogram)
        manager = MetricsManager()

        # Execute
        manager.record_exception("ValueError", "error", {"context": "test"})

        # Verify
        mock_counter.add.assert_called_once_with(1, {"exception_type": "ValueError", "severity": "error", "context": "test"})


class TestGetMetricsManager:
    """Test get_metrics_manager function"""

    def test_get_metrics_manager_singleton(self):
        """Test that get_metrics_manager returns singleton instance"""
        manager1 = get_metrics_manager()
        manager2 = get_metrics_manager()
        assert manager1 is manager2

    def test_get_metrics_manager_returns_instance(self):
        """Test that get_metrics_manager returns MetricsManager instance"""
        manager = get_metrics_manager()
        assert isinstance(manager, MetricsManager)


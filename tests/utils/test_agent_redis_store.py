"""Tests for agent Redis store functions."""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch

from app.utils.agent_redis_store import (
    _is_duplicate_source,
    store_sources,
    get_sources,
    clear_sources,
    store_approval_data,
    get_approval_data,
    clear_approval_data,
    SOURCES_PREFIX,
    APPROVAL_PREFIX,
    DEFAULT_TTL,
)


class TestIsDuplicateSource:
    """Tests for _is_duplicate_source function."""

    def test_duplicate_same_url(self):
        """Test that sources with same URL are duplicates."""
        source1 = {"url": "https://example.com", "title": "Example 1"}
        source2 = {"url": "https://example.com", "title": "Example 2"}
        
        assert _is_duplicate_source(source1, source2) is True

    def test_not_duplicate_different_url(self):
        """Test that sources with different URLs are not duplicates."""
        source1 = {"url": "https://example1.com", "title": "Example"}
        source2 = {"url": "https://example2.com", "title": "Example"}
        
        assert _is_duplicate_source(source1, source2) is False

    def test_not_duplicate_missing_url(self):
        """Test that sources without URLs are not duplicates."""
        source1 = {"title": "Example 1"}
        source2 = {"url": "https://example.com", "title": "Example 2"}
        
        assert _is_duplicate_source(source1, source2) is False

    def test_not_duplicate_both_missing_url(self):
        """Test that sources both without URLs are not duplicates."""
        source1 = {"title": "Example 1"}
        source2 = {"title": "Example 2"}
        
        assert _is_duplicate_source(source1, source2) is False


class TestStoreSources:
    """Tests for store_sources function."""

    @pytest.mark.asyncio
    async def test_store_sources_success(self):
        """Test successfully storing sources."""
        mock_redis_client = MagicMock()
        mock_redis_client.get_value = AsyncMock(return_value={})
        mock_redis_client.set_value = AsyncMock()
        
        mock_redis_manager = MagicMock()
        mock_redis_manager.redis_client = mock_redis_client
        
        with patch(
            "app.utils.agent_redis_store.get_redis_manager",
            return_value=AsyncMock(return_value=mock_redis_manager)
        ):
            with patch(
                "app.utils.agent_redis_store.get_redis_manager",
                AsyncMock(return_value=mock_redis_manager)
            ):
                await store_sources(
                    conversation_id="conv-123",
                    source_type="web",
                    sources=[{"url": "https://example.com", "title": "Example"}]
                )
                
                mock_redis_client.set_value.assert_called_once()

    @pytest.mark.asyncio
    async def test_store_sources_with_duplicates(self):
        """Test storing sources with duplicates."""
        mock_redis_client = MagicMock()
        mock_redis_client.get_value = AsyncMock(return_value={
            "web": [{"url": "https://example.com", "title": "Existing"}]
        })
        mock_redis_client.set_value = AsyncMock()
        
        mock_redis_manager = MagicMock()
        mock_redis_manager.redis_client = mock_redis_client
        
        with patch(
            "app.utils.agent_redis_store.get_redis_manager",
            AsyncMock(return_value=mock_redis_manager)
        ):
            await store_sources(
                conversation_id="conv-123",
                source_type="web",
                sources=[
                    {"url": "https://example.com", "title": "Duplicate"},
                    {"url": "https://new.com", "title": "New"}
                ]
            )
            
            # Should have stored with only new source added
            mock_redis_client.set_value.assert_called_once()


class TestGetSources:
    """Tests for get_sources function."""

    @pytest.mark.asyncio
    async def test_get_sources_success(self):
        """Test successfully getting sources."""
        expected_sources = {"web": [{"url": "https://example.com"}]}
        
        mock_redis_client = MagicMock()
        mock_redis_client.get_value = AsyncMock(return_value=expected_sources)
        
        mock_redis_manager = MagicMock()
        mock_redis_manager.redis_client = mock_redis_client
        
        with patch(
            "app.utils.agent_redis_store.get_redis_manager",
            AsyncMock(return_value=mock_redis_manager)
        ):
            result = await get_sources("conv-123")
            
            assert result == expected_sources

    @pytest.mark.asyncio
    async def test_get_sources_not_found(self):
        """Test getting sources when none exist."""
        mock_redis_client = MagicMock()
        mock_redis_client.get_value = AsyncMock(return_value=None)
        
        mock_redis_manager = MagicMock()
        mock_redis_manager.redis_client = mock_redis_client
        
        with patch(
            "app.utils.agent_redis_store.get_redis_manager",
            AsyncMock(return_value=mock_redis_manager)
        ):
            result = await get_sources("conv-123")
            
            assert result == {}


class TestClearSources:
    """Tests for clear_sources function."""

    @pytest.mark.asyncio
    async def test_clear_sources_success(self):
        """Test successfully clearing sources."""
        mock_redis_client = MagicMock()
        mock_redis_client.delete = AsyncMock()

        mock_redis_manager = MagicMock()
        mock_redis_manager.redis_client = mock_redis_client

        with patch(
            "app.utils.agent_redis_store.get_redis_manager",
            AsyncMock(return_value=mock_redis_manager)
        ):
            await clear_sources("conv-123")

            mock_redis_client.delete.assert_called_once()


class TestStoreApprovalData:
    """Tests for store_approval_data function."""

    @pytest.mark.asyncio
    async def test_store_approval_data_success(self):
        """Test successfully storing approval data."""
        mock_redis_client = MagicMock()
        mock_redis_client.set_value = AsyncMock()

        mock_redis_manager = MagicMock()
        mock_redis_manager.redis_client = mock_redis_client

        with patch(
            "app.utils.agent_redis_store.get_redis_manager",
            AsyncMock(return_value=mock_redis_manager)
        ):
            await store_approval_data(
                conversation_id="conv-123",
                approval_type="mcp_search",
                approval_data={"action": "approve"}
            )

            mock_redis_client.set_value.assert_called_once()


class TestGetApprovalData:
    """Tests for get_approval_data function."""

    @pytest.mark.asyncio
    async def test_get_approval_data_success(self):
        """Test successfully getting approval data."""
        expected_data = {
            "requires_approval": True,
            "approval_type": "mcp_search",
            "approval_data": {"action": "approve"}
        }

        mock_redis_client = MagicMock()
        mock_redis_client.get_value = AsyncMock(return_value=expected_data)

        mock_redis_manager = MagicMock()
        mock_redis_manager.redis_client = mock_redis_client

        with patch(
            "app.utils.agent_redis_store.get_redis_manager",
            AsyncMock(return_value=mock_redis_manager)
        ):
            result = await get_approval_data("conv-123")

            assert result == expected_data

    @pytest.mark.asyncio
    async def test_get_approval_data_not_found(self):
        """Test getting approval data when none exists."""
        mock_redis_client = MagicMock()
        mock_redis_client.get_value = AsyncMock(return_value=None)

        mock_redis_manager = MagicMock()
        mock_redis_manager.redis_client = mock_redis_client

        with patch(
            "app.utils.agent_redis_store.get_redis_manager",
            AsyncMock(return_value=mock_redis_manager)
        ):
            result = await get_approval_data("conv-123")

            assert result is None


class TestClearApprovalData:
    """Tests for clear_approval_data function."""

    @pytest.mark.asyncio
    async def test_clear_approval_data_success(self):
        """Test successfully clearing approval data."""
        mock_redis_client = MagicMock()
        mock_redis_client.delete = AsyncMock()

        mock_redis_manager = MagicMock()
        mock_redis_manager.redis_client = mock_redis_client

        with patch(
            "app.utils.agent_redis_store.get_redis_manager",
            AsyncMock(return_value=mock_redis_manager)
        ):
            await clear_approval_data("conv-123")

            mock_redis_client.delete.assert_called_once()


"""
Tests for app/utils/exceptions.py
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from app.utils.exceptions import ExceptionTracker, get_exception_tracker
import os


class TestExceptionTracker:
    """Test ExceptionTracker class"""

    def test_init(self):
        """Test ExceptionTracker initialization"""
        tracker = ExceptionTracker()
        assert tracker.meter is not None
        assert tracker.exception_counter is not None
        assert tracker.service_name is not None

    def test_init_with_service_name_env(self):
        """Test initialization with OTEL_SERVICE_NAME environment variable"""
        with patch.dict(os.environ, {"OTEL_SERVICE_NAME": "test-service"}):
            tracker = ExceptionTracker()
            assert tracker.service_name == "test-service"

    def test_init_default_service_name(self):
        """Test initialization with default service name"""
        with patch.dict(os.environ, {}, clear=True):
            tracker = ExceptionTracker()
            assert tracker.service_name == "my-service"

    def test_otel_enabled_true(self):
        """Test OTEL enabled flag when set to true"""
        with patch.dict(os.environ, {"OTEL_ENABLED": "true"}):
            tracker = ExceptionTracker()
            assert tracker.otel_enabled is True

    def test_otel_enabled_false(self):
        """Test OTEL enabled flag when set to false"""
        with patch.dict(os.environ, {"OTEL_ENABLED": "false"}):
            tracker = ExceptionTracker()
            assert tracker.otel_enabled is False

    def test_otel_enabled_default(self):
        """Test OTEL enabled flag default value"""
        with patch.dict(os.environ, {}, clear=True):
            tracker = ExceptionTracker()
            assert tracker.otel_enabled is False

    @patch("app.utils.exceptions.trace.get_current_span")
    @patch("app.utils.exceptions.logger")
    def test_track_exception_basic(self, mock_logger, mock_get_span):
        """Test basic exception tracking"""
        mock_span = Mock()
        mock_span.is_recording.return_value = False
        mock_get_span.return_value = mock_span

        tracker = ExceptionTracker()
        exc = ValueError("test error")
        tracker.track_exception(exc)

        # Verify logging was called
        mock_logger.error.assert_called_once()
        call_args = mock_logger.error.call_args
        assert "Exception occurred" in call_args[0]

    @patch("app.utils.exceptions.trace.get_current_span")
    @patch("app.utils.exceptions.logger")
    def test_track_exception_with_otel_enabled(self, mock_logger, mock_get_span):
        """Test exception tracking with OTEL enabled"""
        mock_span = Mock()
        mock_span.is_recording.return_value = True
        mock_get_span.return_value = mock_span

        with patch.dict(os.environ, {"OTEL_ENABLED": "true"}):
            tracker = ExceptionTracker()
            exc = ValueError("test error")
            tracker.track_exception(exc)

            # Verify span attributes were set
            mock_span.set_attribute.assert_any_call("error", True)
            mock_span.set_attribute.assert_any_call("exception.type", "ValueError")
            mock_span.set_attribute.assert_any_call("exception.severity", "error")
            mock_span.set_attribute.assert_any_call("exception.message", "test error")
            mock_span.record_exception.assert_called_once_with(exc)

    @patch("app.utils.exceptions.trace.get_current_span")
    @patch("app.utils.exceptions.logger")
    def test_track_exception_with_custom_severity(self, mock_logger, mock_get_span):
        """Test exception tracking with custom severity"""
        mock_span = Mock()
        mock_span.is_recording.return_value = False
        mock_get_span.return_value = mock_span

        tracker = ExceptionTracker()
        exc = ValueError("test error")
        tracker.track_exception(exc, severity="warning")

        # Verify logging includes severity
        call_kwargs = mock_logger.error.call_args[1]
        assert call_kwargs["severity"] == "warning"

    @patch("app.utils.exceptions.trace.get_current_span")
    @patch("app.utils.exceptions.logger")
    def test_track_exception_with_attributes(self, mock_logger, mock_get_span):
        """Test exception tracking with custom attributes"""
        mock_span = Mock()
        mock_span.is_recording.return_value = False
        mock_get_span.return_value = mock_span

        tracker = ExceptionTracker()
        exc = ValueError("test error")
        attributes = {"user_id": "123", "request_id": "abc"}
        tracker.track_exception(exc, attributes=attributes)

        # Verify attributes are logged
        call_kwargs = mock_logger.error.call_args[1]
        assert call_kwargs["user_id"] == "123"
        assert call_kwargs["request_id"] == "abc"

    @patch("app.utils.exceptions.trace.get_current_span")
    @patch("app.utils.exceptions.logger")
    def test_track_exception_metric_recording_failure(self, mock_logger, mock_get_span):
        """Test exception tracking when metric recording fails"""
        mock_span = Mock()
        mock_span.is_recording.return_value = True
        mock_get_span.return_value = mock_span

        with patch.dict(os.environ, {"OTEL_ENABLED": "true"}):
            tracker = ExceptionTracker()
            tracker.exception_counter.add = Mock(side_effect=Exception("Metric error"))
            
            exc = ValueError("test error")
            tracker.track_exception(exc)

            # Verify error was logged but didn't crash
            assert mock_logger.error.call_count >= 2  # Original error + metric error


class TestGetExceptionTracker:
    """Test get_exception_tracker function"""

    def test_get_exception_tracker_singleton(self):
        """Test that get_exception_tracker returns singleton instance"""
        tracker1 = get_exception_tracker()
        tracker2 = get_exception_tracker()
        assert tracker1 is tracker2

    def test_get_exception_tracker_returns_instance(self):
        """Test that get_exception_tracker returns ExceptionTracker instance"""
        tracker = get_exception_tracker()
        assert isinstance(tracker, ExceptionTracker)


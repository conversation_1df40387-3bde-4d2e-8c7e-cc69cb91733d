from app.agent.run_agent import run_agent_stream, run_agent
import os


async def main():
    user_input = input("Enter query: ")
    provider = input("Enter provider (e.g., anthropic, openai): ") or "anthropic"
    model = (
        input("Enter model (e.g., claude-sonnet-4.5, gpt-4o-mini): ")
        or "claude-sonnet-4.5"
    )
    # This is for knowledge base
    # user_id = input("Enter user_id: ") or "024f69ab-49dc-4689-bace-220e1448d232"

    # This is for google calendar
    # user_id = input("Enter user_id: ") or "edc34cb6-4fd1-4f14-820e-d8738b093698"
    user_id = input("Enter user_id: ") or "024f69ab-49dc-4689-bace-220e1448d232"
    # user_id = input("Enter user_id: ") or "bcbbc3cc-ccb9-4866-94a8-72266c1c269f"
    # user_id = input("Enter user_id: ") or "6e75537b-e7dd-4f2e-9455-2b9acce6f351"
    # organisation_id = (
    #     input("Enter organisation_id: ") or "713ab768-3096-43bb-b510-91ac4d542edf"
    # )
    # organisation_id = (
    #     input("Enter organisation_id: ") or "befe9f8a-c42a-43e4-aac6-aa1db021a67e"
    # )
    organisation_id = (
        input("Enter organisation_id: ") or "713ab768-3096-43bb-b510-91ac4d542edf"
    )
    conversation_id = input("Enter conversation_id: ") or "69244751ccd3e737036ec0aa"
    agent_id = (
        input("Enter agent_id (optional, press enter to skip): ")
        or "41918f57-5934-4eea-825b-dac580cc8513"
    )
    user_name = input("Enter user_name (default: John Doe): ") or "John Doe"
    user_email = (
        input("Enter user_email (default: <EMAIL>): ")
        or "<EMAIL>"
    )
    timezone = (
        input("Enter timezone (default: Asia/Calcutta): ") or "Asia/Calcutta"
    )
    use_memory_input = input("Use memory tools? (y/n, default: y): ").lower()
    use_memory = use_memory_input != "n"
    use_kb = input("Use knowledge base? (y/n): ").lower() == "y"
    use_search = input("Use web search? (y/n): ").lower() == "y"
    use_thinking = input("Use thinking/reasoning mode? (y/n): ").lower() == "y"
    use_stream = input("Use streaming? (y/n): ").lower() == "y"

    # Knowledge base source and file IDs (defaults)
    kb_source = "GOOGLE_DRIVE"  # Default source
    kb_file_ids = []  # Default empty list

    if use_kb:
        add_kb_filters = (
            input(
                "Add knowledge base filters (source/file_ids)? (y/n, default uses GOOGLE_DRIVE): "
            ).lower()
            == "y"
        )
        if add_kb_filters:
            kb_source_input = input("Enter kb_source (default: GOOGLE_DRIVE): ").strip()
            kb_source = kb_source_input if kb_source_input else "GOOGLE_DRIVE"

            kb_file_ids_input = input(
                "Enter kb_file_ids (comma-separated, or press enter for empty): "
            ).strip()
            if kb_file_ids_input:
                kb_file_ids = [fid.strip() for fid in kb_file_ids_input.split(",")]
            else:
                kb_file_ids = []

    mcp_ids = [
        "ef30a525-c157-48d2-a370-06aff3aa71e7",
        "7924caa0-db5b-484d-bc65-49360cb84c3a",
    ]

    attachments = None
    use_attachments = input("Add attachments? (y/n): ").lower() == "y"
    if use_attachments:
        attachments = [
            # {
            #     "file_name": "sample.pdf",
            #     "file_type": "application/pdf",
            #     "file_size": 500000,
            #     "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1760639808-1760639808216-sample.pdf",
            # },
            {
                "file_name": "prospects_filtered.csv",
                "file_type": "text/csv",
                "file_size": 1318,
                "file_url": "https://storage.googleapis.com/ruh-prod/chat-uploads/1762942772-1762942770789-ProspectAI-Sheet11.csv",
            },
            # {
            #     "file_name": "Arcadia_Campaign_Testing_Updated.docx",
            #     "file_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            #     "file_size": 200000,
            #     "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1760639812-1760639812429-Arcadia_Campaign_Testing_Updated.docx",
            # },
            # {
            #     "file_name": "example.txt",
            #     "file_type": "text/plain",
            #     "file_size": 1000,
            #     "file_url": "https://example-files.online-convert.com/document/txt/example.txt",
            # },
            # {
            #     "file_name": "file_example_PNG_500kB.png",
            #     "file_type": "image/png",
            #     "file_size": 500000,
            #     "file_url": "https://storage.googleapis.com/ruh-dev/chat-uploads/1760632108-1760632107030-file_example_PNG_500kB.png",
            # },
        ]
        print(f"\nAttachments added: {len(attachments)} files")

    save_to_file = input("Save output to file? (y/n): ").lower() == "y"
    file_name = None

    if save_to_file:
        user_file_name = input("Enter file name (default: output.txt): ").strip()
        if not user_file_name:
            file_name = "output.txt"
        else:
            base_name, ext = os.path.splitext(user_file_name)
            if ext.lower() == ".txt":
                file_name = user_file_name
            else:
                file_name = base_name + ".txt"

    if use_stream:
        if file_name:
            with open(file_name, "w") as f:
                async for chunk in run_agent_stream(
                    user_message=user_input,
                    provider=provider,
                    model=model,
                    use_knowledge=use_kb,
                    use_search=use_search,
                    user_id=user_id,
                    organisation_id=organisation_id,
                    conversation_id=conversation_id,
                    agent_id=agent_id,
                    use_memory=use_memory,
                    mcp_ids=mcp_ids,
                    attachments=attachments,
                    use_thinking=use_thinking,
                    kb_source=kb_source,
                    kb_file_ids=kb_file_ids,
                    timezone=timezone,
                    user_name=user_name,
                    user_email=user_email,
                ):
                    f.write(str(chunk) + "\n")
                    print(chunk)
            print(f"\nOutput saved to {file_name}")
        else:
            async for chunk in run_agent_stream(
                user_message=user_input,
                provider=provider,
                model=model,
                use_knowledge=use_kb,
                use_search=use_search,
                user_id=user_id,
                organisation_id=organisation_id,
                conversation_id=conversation_id,
                agent_id=agent_id,
                use_memory=use_memory,
                mcp_ids=mcp_ids,
                attachments=attachments,
                use_thinking=use_thinking,
                kb_source=kb_source,
                kb_file_ids=kb_file_ids,
                timezone=timezone,
                user_name=user_name,
                user_email=user_email,
            ):
                print(chunk)

    else:
        result = await run_agent(
            user_message=user_input,
            provider=provider,
            model=model,
            use_knowledge=use_kb,
            use_search=use_search,
            user_id=user_id,
            organisation_id=organisation_id,
            conversation_id=conversation_id,
            agent_id=agent_id,
            use_memory=use_memory,
            mcp_ids=mcp_ids,
            attachments=attachments,
            use_thinking=use_thinking,
            kb_source=kb_source,
            kb_file_ids=kb_file_ids,
            timezone=timezone,
            user_name=user_name,
            user_email=user_email,
        )
        print("\n=== RESULT ===")
        print(result)

        if file_name:
            with open(file_name, "w") as f:
                f.write(str(result))
            print(f"\nOutput saved to {file_name}")


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
